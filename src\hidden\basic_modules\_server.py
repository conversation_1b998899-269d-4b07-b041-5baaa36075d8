import argparse
import logging
import os
import asyncio
from typing import Dict, Any, Optional, List

from mcp import MCPServer
from src.hidden.basic_modules.digest_toolkit import (
    DigestManagerToolkit,
    SingleDigestModuleToolkit,
    SingleDigestNeuronToolkit,
    MergeDigestToolkit
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@MCPServer()
class DigestManagerServer(DigestManagerToolkit):
    
    async def orchestrate_survey_processing(
        self,
        survey_dict: Dict[str, Any],
        module_url: str,
        neuron_url: str, 
        merge_url: str
    ) -> Dict[str, Any]:
        """协调整个调研的处理流程
        
        Args:
            survey_dict: 调研字典
            module_url: 模块服务URL
            neuron_url: 神经元服务URL
            merge_url: 合并服务URL
            
        Returns:
            Dict[str, Any]: 处理后的调研字典
        """
        from mcp_toolkit import MC<PERSON><PERSON>
        
        from src.data_structure import Survey
        survey = Survey.from_dict(survey_dict)
        outline = survey.skeleton
        
        logger.info(f"Orchestrating survey processing for: {survey.title}")
        
        module_client = MCPClient(module_url)
        neuron_client = MCPClient(neuron_url)
        merge_client = MCPClient(merge_url)
        
        try:
            await module_client.connect()
            await neuron_client.connect()
            await merge_client.connect()
            
            preprocess_tool = next(
                tool for tool in module_client.get_tools() 
                if tool.get_function_name() == "process_digest_collection"
            )
            generate_tool = next(
                tool for tool in neuron_client.get_tools()
                if tool.get_function_name() == "generate_single_digest"
            )
            merge_tool = next(
                tool for tool in merge_client.get_tools()
                if tool.get_function_name() == "merge_digests"
            )
            
            processed_collections = []
            for digest_key, digest in survey.digests.items():
                preproc_result = await preprocess_tool.async_call(
                    digest_dict={**digest.to_dict(), "key": digest_key},
                    outline_dict=outline.to_dict()
                )
                
                if not preproc_result.get("success", False):
                    logger.warning(f"Preprocessing failed for collection: {digest_key}")
                    continue
                
                paper_infos = preproc_result.get("paper_infos", [])
                single_results = []
                
                for paper_info in paper_infos:
                    result = await generate_tool.async_call(
                        paper_info=paper_info,
                        outline_dict=outline.to_dict(),
                        survey_title=survey.title,
                        bibkeys=digest.bibkeys
                    )
                    single_results.append(result)
                
                if single_results:
                    merged_result = await merge_tool.async_call(
                        digest_dicts=single_results,
                        outline_dict=outline.to_dict(),
                        origin_digest_dict=digest.to_dict(),
                        collection_key=digest_key
                    )
                    
                    if merged_result.get("success", False):
                        processed_collections.append(merged_result)
                
            final_result = await self.process_survey_digests(
                survey_dict=survey_dict,
                processed_digest_collections=processed_collections
            )
            
            logger.info(f"Completed orchestrating survey: {survey.title}")
            return final_result
            
        finally:
            await module_client.disconnect()
            await neuron_client.disconnect()
            await merge_client.disconnect()
    
    def get_tools(self) -> List[FunctionTool]:
        from camel.toolkits import FunctionTool
        tools = super().get_tools()
        tools.append(FunctionTool(self.orchestrate_survey_processing))
        return tools


@MCPServer()
class SingleDigestModuleServer(SingleDigestModuleToolkit):
    pass


@MCPServer()
class SingleDigestNeuronServer(SingleDigestNeuronToolkit):
    pass


@MCPServer()
class MergeDigestServer(MergeDigestToolkit):
    pass


def parse_args():
    parser = argparse.ArgumentParser(description="启动摘要服务MCP服务器")
    parser.add_argument("--model", type=str, default="gpt-4", 
                        help="LLM模型")
    parser.add_argument("--infer-type", type=str, default="OpenAI", 
                        help="推理类型")
    parser.add_argument("--server-type", type=str, 
                        choices=["manager", "module", "neuron", "merge", "all"], 
                        default="all", help="要启动的服务器类型")
    parser.add_argument("--port-manager", type=int, default=8091, 
                        help="摘要管理服务器端口")
    parser.add_argument("--port-module", type=int, default=8092, 
                        help="摘要模块服务器端口")
    parser.add_argument("--port-neuron", type=int, default=8093, 
                        help="摘要神经元服务器端口")
    parser.add_argument("--port-merge", type=int, default=8094, 
                        help="摘要合并服务器端口")
    return parser.parse_args()


def main():
    args = parse_args()
    
    if args.server_type == "manager" or args.server_type == "all":
        manager_server = DigestManagerServer()
        # 0.0.0.0 表示监听所有网络接口，这样服务可以从任何IP访问
        manager_server.mcp.run_server(host="0.0.0.0", port=args.port_manager)
        logger.info(f"Digest Manager Server started on port {args.port_manager}")
    
    if args.server_type == "module" or args.server_type == "all":
        module_server = SingleDigestModuleServer(
            model=args.model,
            infer_type=args.infer_type
        )
        module_server.mcp.run_server(host="0.0.0.0", port=args.port_module)
        logger.info(f"Single Digest Module Server started on port {args.port_module}")
    
    if args.server_type == "neuron" or args.server_type == "all":
        neuron_server = SingleDigestNeuronServer(
            model=args.model,
            infer_type=args.infer_type
        )
        neuron_server.mcp.run_server(host="0.0.0.0", port=args.port_neuron)
        logger.info(f"Single Digest Neuron Server started on port {args.port_neuron}")
    
    if args.server_type == "merge" or args.server_type == "all":
        merge_server = MergeDigestServer(
            model=args.model,
            infer_type=args.infer_type
        )
        merge_server.mcp.run_server(host="0.0.0.0", port=args.port_merge)
        logger.info(f"Merge Digest Server started on port {args.port_merge}")


if __name__ == "__main__":
    main()