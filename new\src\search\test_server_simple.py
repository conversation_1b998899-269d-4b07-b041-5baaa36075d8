#!/usr/bin/env python3
"""
简化的 LLM Search MCP Server 测试
"""

import asyncio
import json
import logging
import sys
import os

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'LLMxMapReduce_V2'))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_server_imports():
    """测试服务器导入"""
    try:
        logger.info("=== 测试导入 ===")
        
        # 测试 MCP 导入
        try:
            from mcp.server import Server
            from mcp.types import Resource, Tool, TextContent
            import mcp.server.stdio
            logger.info("✓ MCP 模块导入成功")
        except ImportError as e:
            logger.error(f"✗ MCP 模块导入失败: {e}")
            return False
        
        # 测试 LLM_search 导入
        try:
            from LLM_search import LLM_search
            logger.info("✓ LLM_search 导入成功")
        except ImportError as e:
            logger.warning(f"⚠ LLM_search 导入失败: {e}")
        
        # 测试 RequestWrapper 导入
        try:
            from request import RequestWrapper
            logger.info("✓ RequestWrapper 导入成功")
        except ImportError as e:
            logger.warning(f"⚠ RequestWrapper 导入失败: {e}")
        
        # 测试 crawl4ai 导入
        try:
            from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig
            logger.info("✓ crawl4ai 导入成功")
        except ImportError as e:
            logger.warning(f"⚠ crawl4ai 导入失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"导入测试失败: {e}")
        return False

async def test_server_creation():
    """测试服务器创建"""
    try:
        logger.info("=== 测试服务器创建 ===")
        
        from mcp.server import Server
        from mcp.types import Resource, Tool, TextContent
        
        # 创建服务器
        app = Server("test-llm-search-server")
        logger.info("✓ 服务器创建成功")
        
        # 测试资源定义
        @app.list_resources()
        async def list_resources():
            return [
                Resource(
                    uri="test://search/prompts",
                    name="Test Search Prompts",
                    description="测试搜索提示词",
                    mimeType="application/json"
                )
            ]
        
        # 测试工具定义
        @app.list_tools()
        async def list_tools():
            return [
                Tool(
                    name="test_tool",
                    description="测试工具",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "message": {
                                "type": "string",
                                "description": "测试消息"
                            }
                        },
                        "required": ["message"]
                    }
                )
            ]
        
        # 测试工具调用
        @app.call_tool()
        async def call_tool(name: str, arguments: dict):
            if name == "test_tool":
                return [TextContent(
                    type="text", 
                    text=f"收到消息: {arguments.get('message', 'No message')}"
                )]
            else:
                return [TextContent(type="text", text=f"未知工具: {name}")]
        
        # 测试资源和工具列表
        resources = await list_resources()
        tools = await list_tools()
        
        logger.info(f"✓ 资源数量: {len(resources)}")
        logger.info(f"✓ 工具数量: {len(tools)}")
        
        # 测试工具调用
        result = await call_tool("test_tool", {"message": "Hello World"})
        logger.info(f"✓ 工具调用结果: {result[0].text}")
        
        return True
        
    except Exception as e:
        logger.error(f"服务器创建测试失败: {e}")
        return False

async def test_llm_search_functionality():
    """测试 LLM 搜索功能"""
    try:
        logger.info("=== 测试 LLM 搜索功能 ===")
        
        # 尝试创建 LLM_search 实例
        try:
            from LLM_search import LLM_search
            llm_search = LLM_search(
                model="gemini-2.0-flash-thinking-exp-01-21",
                infer_type="OpenAI",
                engine="google",
                each_query_result=5
            )
            logger.info("✓ LLM_search 实例创建成功")
            
            # 测试查询生成（如果可能）
            try:
                queries = llm_search.get_queries("机器学习", "深度学习研究")
                logger.info(f"✓ 查询生成成功，生成了 {len(queries)} 个查询")
                for i, query in enumerate(queries[:3]):
                    logger.info(f"  查询 {i+1}: {query}")
            except Exception as e:
                logger.warning(f"⚠ 查询生成失败: {e}")
            
        except ImportError as e:
            logger.warning(f"⚠ LLM_search 不可用: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"LLM 搜索功能测试失败: {e}")
        return False

async def test_request_wrapper():
    """测试 RequestWrapper 功能"""
    try:
        logger.info("=== 测试 RequestWrapper 功能 ===")
        
        try:
            from request import RequestWrapper
            request_wrapper = RequestWrapper(
                model="gemini-2.0-flash-thinking-exp-01-21",
                infer_type="OpenAI"
            )
            logger.info("✓ RequestWrapper 实例创建成功")
            
            # 测试简单的完成请求（如果可能）
            try:
                response = request_wrapper.completion("Hello, this is a test message.")
                logger.info(f"✓ 请求完成成功，响应长度: {len(response)}")
            except Exception as e:
                logger.warning(f"⚠ 请求完成失败: {e}")
            
        except ImportError as e:
            logger.warning(f"⚠ RequestWrapper 不可用: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"RequestWrapper 功能测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("开始简化测试 LLM Search MCP Server")
    
    tests = [
        ("导入测试", test_server_imports),
        ("服务器创建", test_server_creation),
        ("LLM搜索功能", test_llm_search_functionality),
        ("RequestWrapper功能", test_request_wrapper),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            success = await test_func()
            results[test_name] = success
            logger.info(f"测试 {test_name}: {'通过' if success else '失败'}")
        except Exception as e:
            logger.error(f"测试 {test_name} 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结")
    logger.info(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✓ 通过" if success else "✗ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n总计: {passed}/{total} 测试通过")
    
    if passed >= total * 0.5:  # 至少50%通过
        logger.info("🎉 基本功能正常！")
    else:
        logger.warning(f"⚠️ 需要修复依赖问题")

if __name__ == "__main__":
    asyncio.run(main())
