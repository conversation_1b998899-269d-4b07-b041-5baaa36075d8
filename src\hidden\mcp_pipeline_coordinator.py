#!/usr/bin/env python3
"""
MCP Pipeline Coordinator - MCP流水线协调器
负责协调所有MCP服务器，实现完全基于自然语言指令的智能处理流程
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from contextlib import AsyncExitStack

from src.data_structure import Survey
from src.hidden.mcp_client.intelligent_pipeline_client import IntelligentPipelineClient

logger = logging.getLogger(__name__)


class MCPPipelineCoordinator:
    """MCP流水线协调器 - 统一管理所有MCP服务器的高级协调器"""
    
    def __init__(self, config_path: str):
        """初始化MCP流水线协调器
        
        Args:
            config_path: MCP配置文件路径
        """
        self.config_path = config_path
        self.client = IntelligentPipelineClient(config_path)
        self.processing_history = []
        self.performance_metrics = {}
        
        logger.info("MCP Pipeline Coordinator initialized")
    
    async def initialize(self):
        """初始化协调器，连接所有MCP服务器"""
        try:
            await self.client.connect()
            logger.info("MCP Pipeline Coordinator connected to all servers")
        except Exception as e:
            logger.error(f"Failed to initialize MCP coordinator: {e}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.client.disconnect()
            logger.info("MCP Pipeline Coordinator disconnected")
        except Exception as e:
            logger.warning(f"Error during cleanup: {e}")
    
    async def process_survey_with_natural_language(
        self, 
        survey: Survey, 
        user_instructions: str,
        processing_strategy: str = "adaptive"
    ) -> Survey:
        """使用自然语言指令处理调研
        
        Args:
            survey: 输入的调研对象
            user_instructions: 用户的自然语言指令
            processing_strategy: 处理策略
            
        Returns:
            Survey: 处理后的调研对象
        """
        logger.info(f"Starting natural language processing for survey: {survey.topic}")
        logger.info(f"User instructions: {user_instructions}")
        
        try:
            # 第一步：分析用户指令，制定处理计划
            processing_plan = await self._analyze_user_instructions(
                user_instructions, survey, processing_strategy
            )
            
            # 第二步：执行智能处理流程
            processed_survey = await self._execute_intelligent_workflow(
                survey, processing_plan
            )
            
            # 第三步：质量评估和优化
            final_survey = await self._quality_assurance_and_optimization(
                processed_survey, processing_plan
            )
            
            # 记录处理历史
            self._record_processing_history(survey, user_instructions, processing_plan)
            
            logger.info(f"Natural language processing completed for survey: {survey.topic}")
            return final_survey
            
        except Exception as e:
            logger.error(f"Error in natural language processing: {e}")
            return survey
    
    async def _analyze_user_instructions(
        self, 
        instructions: str, 
        survey: Survey, 
        strategy: str
    ) -> Dict[str, Any]:
        """分析用户指令并制定处理计划"""
        logger.info("Analyzing user instructions and creating processing plan")
        
        try:
            # 使用编排器分析指令
            analysis_result = await self.client.orchestrate_pipeline([
                {
                    "type": "instruction_analysis",
                    "content": instructions,
                    "survey_info": {
                        "topic": survey.topic,
                        "paper_count": len(survey.papers),
                        "complexity": "medium"  # 可以基于内容动态评估
                    },
                    "strategy": strategy
                }
            ])
            
            # 基于分析结果制定具体的处理计划
            processing_plan = {
                "user_instructions": instructions,
                "strategy": strategy,
                "workflow_steps": analysis_result.get("recommended_workflow", []),
                "quality_requirements": analysis_result.get("quality_requirements", {}),
                "processing_parameters": analysis_result.get("parameters", {}),
                "expected_outcomes": analysis_result.get("expected_outcomes", [])
            }
            
            logger.info(f"Processing plan created with {len(processing_plan['workflow_steps'])} steps")
            return processing_plan
            
        except Exception as e:
            logger.warning(f"Error analyzing instructions, using default plan: {e}")
            return self._create_default_processing_plan(instructions, strategy)
    
    async def _execute_intelligent_workflow(
        self, 
        survey: Survey, 
        plan: Dict[str, Any]
    ) -> Survey:
        """执行智能工作流程"""
        logger.info("Executing intelligent workflow")
        
        workflow_steps = plan.get("workflow_steps", [])
        parameters = plan.get("processing_parameters", {})
        
        try:
            # 步骤1：基础模块处理 - 初始化和分组
            if "initialize_structure" in workflow_steps:
                logger.info("Step 1: Structure initialization")
                skeleton_result = await self.client.initialize_survey_skeleton(
                    topic=survey.topic,
                    structure_requirements=parameters.get("skeleton_requirements", {})
                )
                survey.skeleton = skeleton_result.get("skeleton", {})
            
            if "group_papers" in workflow_steps:
                logger.info("Step 2: Paper grouping")
                papers_data = [
                    {"id": paper_id, "title": paper.get("title", ""), "content": paper.get("content", "")} 
                    for paper_id, paper in survey.papers.items()
                ]
                grouping_result = await self.client.group_papers_intelligently(
                    papers=papers_data,
                    criteria=parameters.get("grouping_criteria", "semantic_similarity")
                )
                survey.groups = grouping_result.get("groups", [])
            
            # 步骤2：摘要处理
            if "process_digests" in workflow_steps:
                logger.info("Step 3: Digest processing")
                digest_result = await self.client.process_survey_digests(
                    surveys=[{"papers": papers_data, "topic": survey.topic}],
                    strategy=parameters.get("digest_strategy", "comprehensive")
                )
                survey.digests = digest_result.get("processed_digests", [])
            
            # 步骤3：卷积层优化
            if "apply_convolution" in workflow_steps:
                logger.info("Step 4: Convolution layer processing")
                convolution_input = {
                    "skeleton": survey.skeleton,
                    "groups": survey.groups,
                    "digests": survey.digests,
                    "papers": papers_data
                }
                convolution_result = await self.client.apply_convolution_layer(
                    input_data=convolution_input,
                    strategy=parameters.get("convolution_strategy", "adaptive"),
                    parameters=parameters.get("convolution_parameters", {})
                )
                
                # 更新调研结构
                output_data = convolution_result.get("output", {})
                if "skeleton" in output_data:
                    survey.skeleton = output_data["skeleton"]
                if "groups" in output_data:
                    survey.groups = output_data["groups"]
                if "digests" in output_data:
                    survey.digests = output_data["digests"]
            
            # 步骤4：神经元级别的优化
            if "neural_optimization" in workflow_steps:
                logger.info("Step 5: Neural optimization")
                
                # 评估当前内容质量
                current_content = {
                    "skeleton": survey.skeleton,
                    "groups": survey.groups,
                    "digests": survey.digests
                }
                
                evaluation_result = await self.client.evaluate_content_intelligently(
                    content=current_content,
                    criteria=parameters.get("evaluation_criteria", "comprehensive")
                )
                
                # 如果质量不达标，进行智能修改
                quality_score = evaluation_result.get("score", 0)
                quality_threshold = parameters.get("quality_threshold", 0.8)
                
                if quality_score < quality_threshold:
                    logger.info(f"Quality score {quality_score} below threshold {quality_threshold}, applying intelligent modifications")
                    
                    modification_instructions = f"根据质量评估结果改进内容。当前得分：{quality_score}，需要达到：{quality_threshold}"
                    modification_result = await self.client.modify_content_intelligently(
                        content=current_content,
                        instructions=modification_instructions,
                        strategy=parameters.get("modification_strategy", "moderate")
                    )
                    
                    # 应用修改结果
                    modified_content = modification_result.get("modified_content", current_content)
                    survey.skeleton = modified_content.get("skeleton", survey.skeleton)
                    survey.groups = modified_content.get("groups", survey.groups)
                    survey.digests = modified_content.get("digests", survey.digests)
            
            logger.info("Intelligent workflow execution completed")
            return survey
            
        except Exception as e:
            logger.error(f"Error in workflow execution: {e}")
            return survey
    
    async def _quality_assurance_and_optimization(
        self, 
        survey: Survey, 
        plan: Dict[str, Any]
    ) -> Survey:
        """质量保证和最终优化"""
        logger.info("Performing quality assurance and final optimization")
        
        try:
            quality_requirements = plan.get("quality_requirements", {})
            
            # 最终质量评估
            final_content = {
                "skeleton": survey.skeleton,
                "groups": survey.groups,
                "digests": survey.digests,
                "topic": survey.topic
            }
            
            final_evaluation = await self.client.evaluate_content_intelligently(
                content=final_content,
                criteria="comprehensive"
            )
            
            final_score = final_evaluation.get("score", 0)
            logger.info(f"Final quality score: {final_score}")
            
            # 如果质量仍然不够，进行最后一轮优化
            if final_score < quality_requirements.get("minimum_score", 0.7):
                logger.info("Applying final optimization")
                
                optimization_result = await self.client.modify_content_intelligently(
                    content=final_content,
                    instructions="进行最终优化，确保内容质量、结构完整性和逻辑连贯性",
                    strategy="conservative"
                )
                
                optimized_content = optimization_result.get("modified_content", final_content)
                survey.skeleton = optimized_content.get("skeleton", survey.skeleton)
                survey.groups = optimized_content.get("groups", survey.groups)
                survey.digests = optimized_content.get("digests", survey.digests)
            
            # 记录质量指标
            if not hasattr(survey, "metadata"):
                survey.metadata = {}
            
            survey.metadata.update({
                "final_quality_score": final_score,
                "processing_completed": True,
                "mcp_processed": True
            })
            
            logger.info("Quality assurance completed")
            return survey
            
        except Exception as e:
            logger.error(f"Error in quality assurance: {e}")
            return survey
    
    def _create_default_processing_plan(self, instructions: str, strategy: str) -> Dict[str, Any]:
        """创建默认处理计划"""
        return {
            "user_instructions": instructions,
            "strategy": strategy,
            "workflow_steps": [
                "initialize_structure",
                "group_papers", 
                "process_digests",
                "apply_convolution",
                "neural_optimization"
            ],
            "quality_requirements": {
                "minimum_score": 0.7,
                "completeness_threshold": 0.8
            },
            "processing_parameters": {
                "grouping_criteria": "semantic_similarity",
                "digest_strategy": "comprehensive",
                "convolution_strategy": "adaptive",
                "quality_threshold": 0.8,
                "modification_strategy": "moderate"
            },
            "expected_outcomes": [
                "structured_skeleton",
                "grouped_papers",
                "quality_digests"
            ]
        }
    
    def _record_processing_history(
        self, 
        survey: Survey, 
        instructions: str, 
        plan: Dict[str, Any]
    ):
        """记录处理历史"""
        history_entry = {
            "timestamp": asyncio.get_event_loop().time(),
            "survey_topic": survey.topic,
            "user_instructions": instructions,
            "processing_plan": plan,
            "final_quality": getattr(survey, "metadata", {}).get("final_quality_score", 0)
        }
        
        self.processing_history.append(history_entry)
        
        # 保持历史记录数量限制
        if len(self.processing_history) > 100:
            self.processing_history = self.processing_history[-100:]
    
    async def get_processing_recommendations(
        self, 
        survey: Survey
    ) -> Dict[str, Any]:
        """基于历史数据获取处理建议"""
        try:
            # 分析历史处理数据，提供个性化建议
            recommendations = {
                "suggested_strategy": "adaptive",
                "recommended_parameters": {},
                "quality_predictions": {},
                "optimization_suggestions": []
            }
            
            # 基于调研主题和历史数据进行分析
            similar_cases = [
                entry for entry in self.processing_history
                if survey.topic.lower() in entry["survey_topic"].lower()
            ]
            
            if similar_cases:
                avg_quality = sum(case.get("final_quality", 0) for case in similar_cases) / len(similar_cases)
                recommendations["quality_predictions"]["expected_score"] = avg_quality
                
                # 提取最佳实践参数
                best_case = max(similar_cases, key=lambda x: x.get("final_quality", 0))
                recommendations["recommended_parameters"] = best_case["processing_plan"]["processing_parameters"]
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return {"suggested_strategy": "adaptive"}
    
    async def batch_process_surveys_with_nl(
        self,
        surveys: List[Survey],
        batch_instructions: str,
        processing_strategy: str = "adaptive"
    ) -> List[Survey]:
        """批量处理调研，使用自然语言指令"""
        logger.info(f"Starting batch processing of {len(surveys)} surveys")
        
        processed_surveys = []
        
        for i, survey in enumerate(surveys):
            try:
                logger.info(f"Processing survey {i+1}/{len(surveys)}: {survey.topic}")
                
                # 为每个调研生成个性化指令
                personalized_instructions = f"{batch_instructions}\n特定于本调研: {survey.topic}"
                
                processed_survey = await self.process_survey_with_natural_language(
                    survey=survey,
                    user_instructions=personalized_instructions,
                    processing_strategy=processing_strategy
                )
                
                processed_surveys.append(processed_survey)
                
            except Exception as e:
                logger.error(f"Error processing survey {i}: {e}")
                processed_surveys.append(survey)  # 返回原始调研
        
        logger.info(f"Batch processing completed. Processed {len(processed_surveys)} surveys")
        return processed_surveys


# 使用示例和工厂函数
async def create_mcp_pipeline_coordinator(config: Dict[str, Any]) -> MCPPipelineCoordinator:
    """创建并初始化MCP流水线协调器"""
    coordinator = MCPPipelineCoordinator(config)
    await coordinator.initialize()
    return coordinator


# 便捷接口函数
async def process_survey_with_natural_language_interface(
    survey: Survey,
    user_instructions: str,
    config: Dict[str, Any] = None
) -> Survey:
    """便捷的自然语言处理接口"""
    config = config or {}
    
    coordinator = await create_mcp_pipeline_coordinator(config)
    
    try:
        result = await coordinator.process_survey_with_natural_language(
            survey=survey,
            user_instructions=user_instructions
        )
        return result
    finally:
        await coordinator.cleanup()
