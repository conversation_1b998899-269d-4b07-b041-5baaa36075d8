#!/usr/bin/env python3
"""
Pipeline Orchestrator MCP Server
基于大语言模型提示词的 pipeline 编排服务器
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional

from mcp.server import Server
from mcp.types import Resource, Tool, TextContent
import mcp.server.stdio
from ...utils.process_str import parse_md_content

from src.data_structure import Survey
from request import RequestWrapper

import traceback

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Server("pipeline-orchestrator")

# 全局请求包装器
request_wrapper = None

@app.list_resources()
async def list_resources() -> List[Resource]:
    """列出可用的资源"""
    return [
        Resource(
            uri="pipeline://orchestrator/prompts",
            name="Pipeline Orchestration Prompts",
            description="用于 pipeline 编排的提示词模板",
            mimeType="application/json"
        )
    ]

@app.read_resource()
async def read_resource(uri: str) -> str:
    """读取资源内容"""
    if uri == "pipeline://orchestrator/prompts":
        prompts = {
            "pipeline_orchestration": """
你是一个智能的数据处理流水线编排器。你的任务是分析输入的调研数据，并决定如何最优地处理这些数据。

## 当前数据状态分析
调研标题: {title}
数据块数量: {block_count}
当前处理轮次: {current_cycle}
最大处理轮次: {max_cycles}
骨架完整度: {skeleton_completeness}
摘要数量: {digest_count}

## 处理策略
请分析当前数据状态，并提供下一步的处理建议：

1. **数据分组策略**: 基于数据相似性和内容主题，决定如何对数据进行分组
2. **骨架优化**: 分析当前骨架结构，提出改进建议
3. **摘要生成**: 确定需要生成或更新的摘要内容
4. **迭代策略**: 决定是否需要进一步迭代处理

## 输出格式
请以 JSON 格式输出你的决策：
{{
  "next_action": "group|skeleton|digest|refine|complete",
  "reasoning": "决策理由",
  "parameters": {{
    "group_strategy": "分组策略",
    "skeleton_modifications": ["骨架修改建议"],
    "digest_priorities": ["摘要优先级"],
    "iteration_needed": true/false
  }},
  "confidence": 0.0-1.0
}}
""",
            "data_analysis": """
请分析以下调研数据，并提供数据质量和处理建议：

## 数据内容
{data_content}

## 分析维度
1. **内容质量**: 评估数据的完整性、准确性和相关性
2. **结构一致性**: 检查数据结构是否符合预期格式
3. **信息密度**: 评估信息的丰富程度和价值密度
4. **处理难度**: 评估数据处理的复杂程度

## 输出建议
基于分析结果，提供具体的处理建议和参数设置。
"""
        }
        return json.dumps(prompts, ensure_ascii=False, indent=2)
    else:
        raise ValueError(f"Unknown resource: {uri}")

@app.list_tools()
async def list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="orchestrate_pipeline",
            description="基于 LLM 分析进行智能 pipeline 编排",
            inputSchema={
                "type": "object",
                "properties": {
                    "survey": {
                        "type": "object",
                        "description": "调研数据对象"
                    },
                    "current_state": {
                        "type": "object", 
                        "description": "当前处理状态"
                    },
                },
                "required": ["survey", "current_state"]
            }
        ),
        Tool(
            name="analyze_data_quality",
            description="分析数据质量并提供处理建议",
            inputSchema={
                "type": "object",
                "properties": {
                    "data": {
                        "type": "object",
                        "description": "需要分析的数据"
                    },
                    "analysis_type": {
                        "type": "string",
                        "enum": ["quality", "structure", "content", "comprehensive"],
                        "description": "分析类型"
                    }
                },
                "required": ["data"]
            }
        ),
        Tool(
            name="generate_processing_strategy",
            description="生成数据处理策略",
            inputSchema={
                "type": "object",
                "properties": {
                    "survey_state": {
                        "type": "object",
                        "description": "调研当前状态"
                    },
                    "constraints": {
                        "type": "object", 
                        "description": "处理约束条件"
                    },
                    "objectives": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "处理目标列表"
                    }
                },
                "required": ["survey_state"]
            }
        )
    ]

@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """调用工具"""
    global request_wrapper
    
    if not request_wrapper:
        request_wrapper = RequestWrapper(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI"
        )
    
    try:
        if name == "orchestrate_pipeline":
            result = await _orchestrate_pipeline(
                arguments["survey"],
                arguments["current_state"],
            )
        elif name == "analyze_data_quality":
            result = await _analyze_data_quality(
                arguments["data"],
                arguments.get("analysis_type", "comprehensive")
            )
        elif name == "generate_processing_strategy":
            result = await _generate_processing_strategy(
                arguments["survey_state"],
                arguments.get("constraints", {}),
                arguments.get("objectives", [])
            )
        else:
            raise ValueError(f"Unknown tool: {name}")
        
        return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
    
    except Exception as e:
        error_msg = traceback.format_exc()
        return [TextContent(type="text", text=f"Error: {str(e)}\n\n traceback: {error_msg}")]

async def _orchestrate_pipeline(survey: Survey, current_state: Dict[str, Any]) -> Dict[str, Any]:
    """基于 LLM 分析进行 pipeline 编排"""
    
    # 构建分析提示词
    prompt_template = await read_resource("pipeline://orchestrator/prompts")
    prompts = json.loads(prompt_template)
    
    # 分析调研数据状态
    # survey = Survey.from_dict(survey_data) if isinstance(survey_data, dict) else survey_data
    # survey = Survey(json_data) if isinstance(json_data, dict) else json_data

    analysis_prompt = prompts["pipeline_orchestration"].format(
        title=survey.title,
        block_count=len(survey.digests) if hasattr(survey, 'digests') else 0,
        current_cycle=current_state.get("cycle", 0),
        max_cycles=current_state.get("max_cycles", 5),
        skeleton_completeness=_calculate_skeleton_completeness(survey),
        digest_count=len(survey.digests) if hasattr(survey, 'digests') else 0
    )
    
    # 调用 LLM 进行分析，
    # response = await request_wrapper.async_request([analysis_prompt])
    response = request_wrapper.completion(analysis_prompt)

    """现阶段LLM返回的内容示例：
    ```json\\n{\\n  \\"next_action\\": \\"skeleton\\",\\n  \\"reasoning\\": \\"当前数据块数量为0，无法进行基于数据的处理（如分组或摘要生成）。骨架完整度为0.5，表明骨架结构尚不完整。在接收数据之前，优先优化和完善骨架结构，可以确保当数据块到来时能够有效地组织和归类，为后续的数据处理奠定基础。\\",\\n  \\"parameters\\": {\\n    \\"group_strategy\\": \\"不适用，当前无数据。\\",\\n    \\"skeleton_modifications\\": [\\n      \\"根据调研标题“A Survey on Dialog Management: Recent Advances and Challenges”，明确骨架的核心部分。\\",\\n      \\"增加或完善“Recent Advances”和“Challenges”这两个主要章节及其初步的子章节结构。\\",\\n      \\"优化引言（Introduction）和结论（Conclusion）等基础章节的逻辑和细节。\\",\\n      \\"考虑添加背景（Background/Preliminaries）或相关工作（Related Work）章节。\\",\\n      \\"确保各章节之间的层级清晰，逻辑流畅，为后续填充数据块做准备。\\"\\n    ],\\n    \\"digest_priorities\\": \\"不适用，当前无数据。\\",\\n    \\"iteration_needed\\": true\\n  },\\n  \\"confidence\\": 1.0\\n}\\n```
    """

    # response = response.strip().replace("```json", "").replace("```", "").replace("\\n", "").replace("\\", "")
    response = parse_md_content(response, label="json").strip()

    try:
        # 解析 LLM 响应
        result = json.loads(response)
        
        # 添加元数据
        result["timestamp"] = current_state.get("timestamp")
        result["survey_id"] = survey.title
        result["orchestrator_version"] = "1.0.0"
        
        return result
    
    except json.JSONDecodeError:
        # 如果 LLM 没有返回有效的 JSON，则使用默认策略
        logger.warning("LLM response is not valid JSON, using default strategy")
        return {
            "next_action": "group",
            "reasoning": "Default fallback strategy",
            "parameters": {
                "group_strategy": "content_similarity",
                "skeleton_modifications": [],
                "digest_priorities": ["high"],
                "iteration_needed": True
            },
            "confidence": 0.5
        }

async def _analyze_data_quality(data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
    """分析数据质量"""
    
    prompt_template = await read_resource("pipeline://orchestrator/prompts")
    prompts = json.loads(prompt_template)
    
    analysis_prompt = prompts["data_analysis"].format(
        data_content=json.dumps(data, ensure_ascii=False, indent=2)[:2000]  # 限制长度
    )
    
    # response = await request_wrapper.async_request([analysis_prompt])
    response = await request_wrapper.completion(analysis_prompt)
    
    return {
        "analysis_type": analysis_type,
        "quality_score": 0.8,  # 这里应该基于 LLM 分析结果
        "recommendations": response,
        "issues_found": [],
        "confidence": 0.85
    }

async def _generate_processing_strategy(survey_state: Dict[str, Any], constraints: Dict[str, Any], objectives: List[str]) -> Dict[str, Any]:
    """生成处理策略"""
    
    strategy_prompt = f"""
基于以下信息生成最优的数据处理策略：

调研状态: {json.dumps(survey_state, ensure_ascii=False)}
约束条件: {json.dumps(constraints, ensure_ascii=False)}
处理目标: {objectives}

请提供一个详细的处理策略，包括：
1. 处理步骤的优先级
2. 资源分配建议
3. 风险评估
4. 预期结果
"""
    
    # response = await request_wrapper.async_request([strategy_prompt])
    response = await request_wrapper.completion(strategy_prompt)
    
    return {
        "strategy": response,
        "priority_actions": ["group", "digest", "skeleton"],
        "resource_allocation": {
            "cpu_intensive": ["group", "skeleton"],
            "memory_intensive": ["digest"],
            "io_intensive": ["data_loading"]
        },
        "estimated_duration": "5-10 minutes",
        "confidence": 0.9
    }

def _calculate_skeleton_completeness(survey) -> float:
    """计算骨架完整度"""
    if not hasattr(survey, 'skeleton') or not survey.skeleton:
        return 0.0
    
    # 简单的完整度计算逻辑
    skeleton_data = survey.skeleton.to_dict() if hasattr(survey.skeleton, 'to_dict') else survey.skeleton
    
    if isinstance(skeleton_data, dict):
        return min(1.0, len(skeleton_data) / 10.0)  # 假设完整骨架有10个字段
    
    return 0.5  # 默认值

async def main():
    """启动 MCP server"""
    logger.info("Starting Pipeline Orchestrator MCP Server...")
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await app.run(read_stream, write_stream, app.create_initialization_options())

if __name__ == "__main__":
    asyncio.run(main())
