#!/usr/bin/env python3
"""
测试 LLM Search MCP Server
"""

import asyncio
import json
import logging
import sys
import os

# 添加路径以便导入
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_server_basic():
    """测试服务器基本功能"""
    try:
        # 导入服务器模块
        from llm_search_mcp_server import app, list_resources, list_tools, call_tool
        
        logger.info("=== 测试资源列表 ===")
        resources = await list_resources()
        logger.info(f"可用资源: {len(resources)}")
        for resource in resources:
            logger.info(f"  - {resource.name}: {resource.uri}")
        
        logger.info("=== 测试工具列表 ===")
        tools = await list_tools()
        logger.info(f"可用工具: {len(tools)}")
        for tool in tools:
            logger.info(f"  - {tool.name}: {tool.description}")
        
        return True
        
    except Exception as e:
        logger.error(f"基本功能测试失败: {e}")
        return False

async def test_generate_queries():
    """测试查询生成功能"""
    try:
        from llm_search_mcp_server import call_tool
        
        logger.info("=== 测试查询生成 ===")
        
        # 测试参数
        arguments = {
            "topic": "机器学习",
            "description": "深度学习和神经网络相关研究",
            "model": "gemini-2.0-flash-thinking-exp-01-21"
        }
        
        result = await call_tool("generate_search_queries", arguments)
        logger.info(f"查询生成结果: {result[0].text[:200]}...")
        
        # 解析结果
        result_data = json.loads(result[0].text)
        if "queries" in result_data:
            logger.info(f"生成了 {len(result_data['queries'])} 个查询")
            for i, query in enumerate(result_data['queries'][:3]):
                logger.info(f"  查询 {i+1}: {query}")
        
        return True
        
    except Exception as e:
        logger.error(f"查询生成测试失败: {e}")
        return False

async def test_web_search():
    """测试网络搜索功能"""
    try:
        from llm_search_mcp_server import call_tool
        
        logger.info("=== 测试网络搜索 ===")
        
        # 测试参数
        arguments = {
            "queries": ["机器学习", "深度学习", "神经网络"],
            "topic": "机器学习",
            "top_n": 5,
            "engine": "google"
        }
        
        result = await call_tool("web_search", arguments)
        logger.info(f"搜索结果: {result[0].text[:200]}...")
        
        # 解析结果
        result_data = json.loads(result[0].text)
        if "urls" in result_data:
            logger.info(f"找到 {len(result_data['urls'])} 个URL")
            for i, url in enumerate(result_data['urls'][:3]):
                logger.info(f"  URL {i+1}: {url}")
        
        return True
        
    except Exception as e:
        logger.error(f"网络搜索测试失败: {e}")
        return False

async def test_crawl_urls():
    """测试爬虫功能"""
    try:
        from llm_search_mcp_server import call_tool
        
        logger.info("=== 测试爬虫功能 ===")
        
        # 使用一些测试URL
        test_urls = [
            "https://en.wikipedia.org/wiki/Machine_learning",
            "https://en.wikipedia.org/wiki/Deep_learning"
        ]
        
        arguments = {
            "topic": "机器学习",
            "url_list": test_urls,
            "top_n": 2,
            "model": "gemini-2.0-flash-thinking-exp-01-21",
            "similarity_threshold": 50,  # 降低阈值以便测试
            "min_length": 100,
            "max_length": 5000
        }
        
        result = await call_tool("crawl_urls", arguments)
        logger.info(f"爬虫结果: {result[0].text[:200]}...")
        
        # 解析结果
        result_data = json.loads(result[0].text)
        if "final_results" in result_data:
            logger.info(f"最终结果数量: {len(result_data['final_results'])}")
            for i, item in enumerate(result_data['final_results'][:2]):
                logger.info(f"  结果 {i+1}: {item.get('title', 'No title')[:50]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"爬虫功能测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("开始测试 LLM Search MCP Server")
    
    tests = [
        ("基本功能", test_server_basic),
        ("查询生成", test_generate_queries),
        ("网络搜索", test_web_search),
        ("爬虫功能", test_crawl_urls),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            success = await test_func()
            results[test_name] = success
            logger.info(f"测试 {test_name}: {'通过' if success else '失败'}")
        except Exception as e:
            logger.error(f"测试 {test_name} 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结")
    logger.info(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✓ 通过" if success else "✗ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！")
    else:
        logger.warning(f"⚠️  {total - passed} 个测试失败")

if __name__ == "__main__":
    asyncio.run(main())
