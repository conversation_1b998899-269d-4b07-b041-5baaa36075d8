"""
文献编码管道模块
在test/'topic'/下存储了调研到的paper
encode流程进行survey类的初始化和papers的载入与分组
"""

import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..data_structure.survey import Survey
from .group import group_papers

logger = logging.getLogger(__name__)


class EncodePipeline:

    def __init__(self, args=None):
        """
        初始化编码管道

        Args:
            args: 命令行参数对象，包含配置信息
        """
        self.args = args
        logger.info("EncodePipeline initialized")

    def load_papers_from_directory(self, args: dict) -> List[Dict[str, Any]]:
        task_dir = args['task_dir']
        task_path = Path(task_dir)
        if not task_path.exists():
            raise FileNotFoundError(f"Task directory not found: {task_dir}")

        papers = []

        json_files = [f for f in task_path.glob("*.json") if f.name != "summary.json"]

        for json_file in sorted(json_files):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    paper_data = json.load(f)
                    papers.append(paper_data)

            except (json.JSONDecodeError, IOError) as e:
                logger.warning(f"Failed to load paper from {json_file}: {e}")
                continue

        logger.info(f"Loaded {len(papers)} papers from {task_dir}")
        return papers

    def create_survey_from_papers(self, task: str, papers: List[Dict[str, Any]],
                                 outline: Optional[List[str]] = None,
                                 content: Optional[str] = None) -> Survey:

        # Survey所需的JSON数据结构
        survey_data = {
            "title": task,
            "outline": outline or [],
            "txt": content or "",
            "papers": papers
        }

        survey = Survey(survey_data)

        # 按照原始的survey实现，但是同时传args和survey的话，其实可以减少一下冲突
        if self.args:
            survey.skeleton_batch_size = getattr(self.args, 'skeleton_group_size', 3)
            survey.conv_layer = getattr(self.args, 'conv_layer', 6)
            survey.receptive_field = getattr(self.args, 'conv_kernel_width', 3)
            survey.top_k = getattr(self.args, 'top_k', 6)
            survey.result_num = getattr(self.args, 'conv_result_num', 10)
            survey.best_of = getattr(self.args, 'self_refine_best_of', 3)
            survey.refine_count = getattr(self.args, 'self_refine_count', 3)

        logger.info(f"Created Survey '{task}' with {len(papers)} papers")
        return survey

    def get_group_directories(self, task_dir: str) -> List[Path]:

        task_path = Path(task_dir)
        group_dirs = [d for d in task_path.iterdir() if d.is_dir() and d.name.startswith('组')]
        return sorted(group_dirs)

    def encode_from_directory(self, task_dir: str, task_name: Optional[str] = None) -> List[Survey]:
        if task_name is None:
            task_name = Path(task_dir).name

        logger.info(f"Starting encode process for task: {task_name}")

        if self.args:
            self.args.task_dir = task_dir
        group_papers(self.args)

        group_dirs = self.get_group_directories(task_dir)

        if not group_dirs:
            logger.warning("No group directories found, processing original directory")
            papers = self.load_papers_from_directory(task_dir)
            survey = self.create_survey_from_papers(task_name, papers)
            return [survey]

        # 为每个分组创建Survey对象
        surveys = []
        for group_dir in group_dirs:
            group_name = f"{task_name}_{group_dir.name}"
            papers = self.load_papers_from_directory(str(group_dir))
            survey = self.create_survey_from_papers(group_name, papers)
            surveys.append(survey)
            logger.info(f"Created survey for group: {group_name}")

        logger.info(f"Encode process completed: {len(surveys)} surveys created")
        return surveys


def encode_pipeline(task_dir: str, args=None, task_name: Optional[str] = None) -> List[Survey]:
    """
    便捷函数：执行完整的编码管道流程

    Args:
        task_dir: 任务目录路径
        args: 可选的参数对象
        task_name: 可选的任务名称

    Returns:
        List[Survey]: 编码完成的Survey对象列表
    """
    pipeline = EncodePipeline(args)
    return pipeline.encode_from_directory(task_dir, task_name)
