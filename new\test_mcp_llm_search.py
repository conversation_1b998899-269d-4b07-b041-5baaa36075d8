#!/usr/bin/env python3
"""
LLM搜索MCP服务器和客户端测试脚本
"""

import asyncio
import json
import logging
import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.search.llm_search_mcp_client import LLMSearchMCPClient, create_llm_search_client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_mcp_connection():
    """测试MCP连接"""
    print("=" * 60)
    print("测试1: MCP连接测试")
    print("=" * 60)
    
    try:
        # 手动创建客户端配置（用于测试）
        server_config = {
            "command": "python",
            "args": ["-m", "src.search.llm_search_mcp_server"],
            "env": {
                "PYTHONPATH": ".",
                "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", ""),
                "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY", ""),
                "SERP_API_KEY": os.getenv("SERP_API_KEY", ""),
                "BING_SEARCH_V7_SUBSCRIPTION_KEY": os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY", "")
            }
        }
        
        client = LLMSearchMCPClient(server_config)
        await client.connect()
        
        print("✅ MCP连接成功")
        
        # 列出可用工具
        tools = await client.list_tools()
        print(f"✅ 发现 {len(tools)} 个可用工具:")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        await client.disconnect()
        print("✅ MCP断开连接成功")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP连接测试失败: {e}")
        return False

async def test_query_generation():
    """测试查询生成功能"""
    print("\n" + "=" * 60)
    print("测试2: 查询生成测试")
    print("=" * 60)
    
    client = None
    try:
        # 检查API密钥
        if not os.getenv("OPENAI_API_KEY") and not os.getenv("GOOGLE_API_KEY"):
            print("⚠️  警告: 未设置API密钥，跳过查询生成测试")
            return False
        
        server_config = {
            "command": "python",
            "args": ["-m", "src.search.llm_search_mcp_server"],
            "env": {
                "PYTHONPATH": ".",
                "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", ""),
                "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY", ""),
                "SERP_API_KEY": os.getenv("SERP_API_KEY", ""),
                "BING_SEARCH_V7_SUBSCRIPTION_KEY": os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY", "")
            }
        }
        
        client = LLMSearchMCPClient(server_config)
        await client.connect()
        
        # 测试查询生成
        result = await client.generate_search_queries(
            topic="机器学习优化算法",
            description="研究机器学习中的优化技术和算法",
            model="gemini-2.0-flash-thinking-exp-01-21"
        )
        
        print("✅ 查询生成成功:")
        print(f"  主题: {result['topic']}")
        print(f"  生成查询数量: {result['query_count']}")
        print("  生成的查询:")
        for i, query in enumerate(result['queries'], 1):
            print(f"    {i}. {query}")
        
        return True
        
    except Exception as e:
        print(f"❌ 查询生成测试失败: {e}")
        return False
    finally:
        if client:
            await client.disconnect()

async def test_web_search():
    """测试网络搜索功能"""
    print("\n" + "=" * 60)
    print("测试3: 网络搜索测试")
    print("=" * 60)
    
    client = None
    try:
        # 检查搜索API密钥
        if not os.getenv("SERP_API_KEY") and not os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY"):
            print("⚠️  警告: 未设置搜索API密钥，跳过网络搜索测试")
            return False
        
        server_config = {
            "command": "python",
            "args": ["-m", "src.search.llm_search_mcp_server"],
            "env": {
                "PYTHONPATH": ".",
                "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", ""),
                "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY", ""),
                "SERP_API_KEY": os.getenv("SERP_API_KEY", ""),
                "BING_SEARCH_V7_SUBSCRIPTION_KEY": os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY", "")
            }
        }
        
        client = LLMSearchMCPClient(server_config)
        await client.connect()
        
        # 使用预定义查询进行搜索测试
        test_queries = [
            "machine learning optimization algorithms",
            "deep learning optimization techniques"
        ]
        
        result = await client.web_search(
            queries=test_queries,
            topic="机器学习优化",
            top_n=5,
            engine="google"
        )
        
        print("✅ 网络搜索成功:")
        print(f"  搜索查询数量: {len(result['queries'])}")
        print(f"  找到URL数量: {result['url_count']}")
        print("  搜索结果URL:")
        for i, url in enumerate(result['urls'][:3], 1):  # 只显示前3个
            print(f"    {i}. {url}")
        if len(result['urls']) > 3:
            print(f"    ... 还有 {len(result['urls']) - 3} 个结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 网络搜索测试失败: {e}")
        return False
    finally:
        if client:
            await client.disconnect()

async def test_full_pipeline():
    """测试完整搜索流水线"""
    print("\n" + "=" * 60)
    print("测试4: 完整流水线测试")
    print("=" * 60)
    
    client = None
    try:
        # 检查所有必要的API密钥
        required_keys = ["OPENAI_API_KEY", "GOOGLE_API_KEY", "SERP_API_KEY", "BING_SEARCH_V7_SUBSCRIPTION_KEY"]
        available_keys = [key for key in required_keys if os.getenv(key)]
        
        if len(available_keys) < 2:  # 至少需要LLM API和搜索API
            print("⚠️  警告: API密钥不足，跳过完整流水线测试")
            print(f"  可用密钥: {available_keys}")
            return False
        
        server_config = {
            "command": "python",
            "args": ["-m", "src.search.llm_search_mcp_server"],
            "env": {
                "PYTHONPATH": ".",
                "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", ""),
                "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY", ""),
                "SERP_API_KEY": os.getenv("SERP_API_KEY", ""),
                "BING_SEARCH_V7_SUBSCRIPTION_KEY": os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY", "")
            }
        }
        
        client = LLMSearchMCPClient(server_config)
        await client.connect()
        
        # 执行完整流水线
        result = await client.full_search_pipeline(
            topic="人工智能伦理",
            description="研究人工智能发展中的伦理问题和挑战",
            top_n=3,
            model="gemini-2.0-flash-thinking-exp-01-21",
            engine="google"
        )
        
        print("✅ 完整流水线执行成功:")
        print(f"  主题: {result['topic']}")
        print(f"  使用模型: {result['model']}")
        print(f"  搜索引擎: {result['engine']}")
        
        # 显示流水线步骤结果
        steps = result['pipeline_steps']
        print("\n  流水线步骤结果:")
        print(f"    1. 查询生成: {steps['1_query_generation']['query_count']} 个查询")
        print(f"    2. 网络搜索: {steps['2_web_search']['search_results']} 个结果")
        
        print(f"\n  最终结果 ({len(result['final_results'])} 个URL):")
        for i, url in enumerate(result['final_results'], 1):
            print(f"    {i}. {url}")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整流水线测试失败: {e}")
        return False
    finally:
        if client:
            await client.disconnect()

async def main():
    """主测试函数"""
    print("🚀 开始LLM搜索MCP服务器测试")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查环境变量
    print("\n📋 环境变量检查:")
    env_vars = ["OPENAI_API_KEY", "GOOGLE_API_KEY", "SERP_API_KEY", "BING_SEARCH_V7_SUBSCRIPTION_KEY"]
    for var in env_vars:
        value = os.getenv(var)
        status = "✅ 已设置" if value else "❌ 未设置"
        print(f"  {var}: {status}")
    
    # 运行测试
    tests = [
        ("MCP连接", test_mcp_connection),
        ("查询生成", test_query_generation),
        ("网络搜索", test_web_search),
        ("完整流水线", test_full_pipeline)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 发生异常: {e}")
            results.append((test_name, False))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！MCP实现工作正常。")
    else:
        print("⚠️  部分测试失败，请检查配置和API密钥。")

if __name__ == "__main__":
    asyncio.run(main())
