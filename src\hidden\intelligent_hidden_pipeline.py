#!/usr/bin/env python3
"""
Intelligent Hidden Pipeline
基于 LLM 提示词和 MCP 协议的智能隐藏层流水线
"""

import asyncio
import logging
from typing import Dict, Any, Optional

from async_d import Node, Pipeline
from src.data_structure import Survey
from src.hidden.mcp_client.intelligent_pipeline_client import IntelligentPipelineClient


from mcp import ClientSession

logger = logging.getLogger(__name__)

class IntelligentModule:
    """智能模块基类，使用 LLM 驱动的处理逻辑"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mcp_client = None
        
    async def initialize(self):
        """初始化 MCP 客户端"""
        if not self.mcp_client:
            self.mcp_client = IntelligentPipelineClient()
            await self.mcp_client.connect()
    
    async def cleanup(self):
        """清理资源"""
        if self.mcp_client:
            await self.mcp_client.disconnect()

class IntelligentGroupModule(IntelligentModule):
    """智能分组模块"""
    
    def __init__(self, config: Dict[str, Any], group_mode: str, kernel_size: int):
        super().__init__(config)
        self.group_mode = group_mode
        self.kernel_size = kernel_size
        
    async def __call__(self, survey: Survey) -> Survey:
        """使用 LLM 进行智能分组"""
        logger.info(f"IntelligentGroupModule processing survey: {survey.title}")
        
        await self.initialize()
        
        try:
            # 使用 MCP 客户端进行智能分组
            if hasattr(survey, 'digests') and survey.digests:
                # 正确处理 MultiKeyDict 的迭代，保持与 Digest.to_dict() 格式一致
                dataset = []
                for keys, digest in survey.digests.items():
                    # 使用与 Digest.to_dict() 相同的格式
                    bibkey_str = ", ".join(sorted(keys)) if isinstance(keys, frozenset) else str(keys)
                    dataset.append({
                        "id": bibkey_str,
                        "content": str(digest),
                        "metadata": getattr(digest, 'metadata', {})
                    })

                # 使用正确的 MCP 客户端调用方式
                read, write = self.mcp_client.group_session
                async with ClientSession(read, write) as session:
                    await session.initialize()

                    # 参数冗余且与client冲突
                    result = await session.call_tool(
                        "group_data_intelligently",
                        {
                            "dataset": dataset,
                            "grouping_objective": f"根据 {self.group_mode} 模式进行分组",
                            "expected_groups": max(2, len(dataset) // self.kernel_size),
                            "grouping_strategy": "adaptive",
                            "constraints": {
                                "min_group_size": 1,
                                "max_group_size": self.kernel_size * 2
                            }
                        }
                    )

                if result.content:
                    import json
                    grouping_result = json.loads(result.content[0].text)
                    # 这里可以基于分组结果更新 survey
                    logger.info(f"Intelligent grouping completed with confidence: {grouping_result.get('confidence', 'N/A')}")

            return survey
            
        except Exception as e:
            logger.error(f"Error in intelligent grouping: {e}")
            return survey

class IntelligentSkeletonModule(IntelligentModule):
    """智能骨架模块"""
    
    def __init__(self, config: Dict[str, Any], skeleton_group_size: int):
        super().__init__(config)
        self.skeleton_group_size = skeleton_group_size
        
    async def __call__(self, survey: Survey) -> Survey:
        """使用 LLM 进行智能骨架生成/优化"""
        logger.info(f"IntelligentSkeletonModule processing survey: {survey.title}")
        
        await self.initialize()
        
        try:
            read, write = self.mcp_client.skeleton_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                if hasattr(survey, 'skeleton') and survey.skeleton:
                    # 优化现有骨架
                    skeleton_data = survey.skeleton.all_skeleton(construction=True, analysis=True)
                    result = await session.call_tool(
                        "refine_skeleton_intelligently",
                        {
                            "current_skeleton": skeleton_data,
                            "new_information": f"基于 {len(survey.digests) if hasattr(survey, 'digests') else 0} 个摘要的内容",
                            "optimization_goals": ["提高结构清晰度", "增强逻辑连贯性", "优化内容覆盖"]
                        }
                    )
                else:
                    # 生成新骨架
                    data_overview = {
                        "digest_count": len(survey.digests) if hasattr(survey, 'digests') else 0,
                        "content_types": ["text"],  # 这里可以基于实际数据分析
                        "complexity": "medium"
                    }

                    result = await session.call_tool(
                        "generate_intelligent_skeleton",
                        {
                            "topic": survey.title,
                            "description": getattr(survey, 'description', ''),
                            "data_overview": data_overview,
                            "expected_depth": "medium",
                            "constraints": {
                                "max_sections": 10,
                                "target_group_size": self.skeleton_group_size
                            }
                        }
                    )

                if result.content:
                    import json
                    skeleton_result = json.loads(result.content[0].text)
                    # 这里可以基于骨架结果更新 survey
                    logger.info(f"Intelligent skeleton processing completed with confidence: {skeleton_result.get('confidence', 'N/A')}")

            return survey
            
        except Exception as e:
            logger.error(f"Error in intelligent skeleton processing: {e}")
            return survey

class IntelligentDigestModule(IntelligentModule):
    """智能摘要模块"""
    
    async def __call__(self, survey: Survey) -> Survey:
        """使用 LLM 进行智能摘要生成"""
        logger.info(f"IntelligentDigestModule processing survey: {survey.title}")
        
        await self.initialize()
        
        try:
            if hasattr(survey, 'digests') and survey.digests:
                read, write = self.mcp_client.digest_session
                async with ClientSession(read, write) as session:
                    await session.initialize()

                    # 正确处理 MultiKeyDict 的迭代，保持与 Digest.to_dict() 格式一致
                    for keys, digest in survey.digests.items():
                        # 使用与 Digest.to_dict() 相同的格式，并排序确保一致性
                        digest_key = ", ".join(sorted(keys)) if isinstance(keys, frozenset) else str(keys)

                        # 生成智能摘要
                        skeleton_data = survey.skeleton.all_skeleton() if hasattr(survey, 'skeleton') and survey.skeleton else ""
                        result = await session.call_tool(
                            "generate_intelligent_digest",
                            {
                                "content": str(digest),
                                "outline": skeleton_data,
                                "topic": survey.title,
                                "digest_type": "synthesis"
                            }
                        )
                        if result.content:
                            import json
                            digest_result = json.loads(result.content[0].text)

                            # 评估摘要质量
                            quality_result = await session.call_tool(
                                "assess_digest_quality",
                                {
                                    "digest": {"content": digest_result.get("digest_content", "")},
                                    "reference_content": str(digest),
                                    "quality_criteria": ["准确性", "完整性", "简洁性", "连贯性"]
                                }
                            )

                            if quality_result.content:
                                quality_data = json.loads(quality_result.content[0].text)
                                logger.info(f"Digest quality for {digest_key}: {quality_data.get('overall_score', 'N/A')}")

            return survey
            
        except Exception as e:
            logger.error(f"Error in intelligent digest processing: {e}")
            return survey

class IntelligentRefinementModule(IntelligentModule):
    """智能精化模块"""
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        super().__init__(config)
        self.refinement_params = kwargs
        
    async def __call__(self, survey: Survey) -> Survey:
        """使用 LLM 进行智能精化处理"""
        logger.info(f"IntelligentRefinementModule processing survey: {survey.title}")
        
        await self.initialize()
        
        try:
            # 使用编排器评估当前状态并决定精化策略
            orchestration_result = await self.mcp_client._call_orchestrator(
                survey.to_dict(),
                {"cycle": getattr(survey, 'block_cycle_count', 0), "refinement_stage": True},
                self.refinement_params
            )

            refinement_strategy = orchestration_result.get("parameters", {})

            # 执行基于 LLM 建议的精化操作
            if refinement_strategy.get("skeleton_modifications"):
                survey = await self._refine_skeleton(survey, refinement_strategy["skeleton_modifications"])

            if refinement_strategy.get("digest_priorities"):
                survey = await self._refine_digests(survey, refinement_strategy["digest_priorities"])

            # 增加处理轮次
            survey.block_cycle_count += 1

            logger.info(f"Intelligent refinement completed. Cycle: {survey.block_cycle_count}")
            return survey
            
        except Exception as e:
            logger.error(f"Error in intelligent refinement: {e}")
            return survey
    
    async def _refine_skeleton(self, survey: Survey, modifications: list) -> Survey:
        """精化骨架"""
        if hasattr(survey, 'skeleton') and survey.skeleton:
            read, write = self.mcp_client.skeleton_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                skeleton_data = survey.skeleton.all_skeleton(construction=True, analysis=True)
                result = await session.call_tool(
                    "refine_skeleton_intelligently",
                    {
                        "current_skeleton": skeleton_data,
                        "optimization_goals": modifications
                    }
                )

                if result.content:
                    import json
                    skeleton_result = json.loads(result.content[0].text)
                    # 这里可以基于结果更新骨架
                    logger.info(f"Skeleton refinement completed with confidence: {skeleton_result.get('confidence', 'N/A')}")
        return survey

    async def _refine_digests(self, survey: Survey, priorities: list) -> Survey:
        """精化摘要"""
        if hasattr(survey, 'digests') and survey.digests and priorities:
            read, write = self.mcp_client.digest_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                for priority in priorities:
                    # 根据优先级处理相应的摘要
                    logger.info(f"Processing digest priority: {priority}")
                    # 这里可以添加具体的摘要精化逻辑
        return survey

class IntelligentHiddenPipeline(Pipeline):
    """基于 LLM 提示词的智能隐藏层流水线"""
    
    def __init__(
        self,
        config: Dict[str, Any],
        output_each_block: bool,
        group_mode: str,
        skeleton_group_size: int,
        block_count: int,
        convolution_layer: int,
        convolution_kernel_size: int,
        convolution_result_num: int,
        top_k: int,
        self_refine_count: int,
        self_refine_best_of: int,
        worker_num: int = 1,
    ):
        self.output_each_block = output_each_block
        self.block_count = block_count
        
        # 创建智能模块
        
        # todo：conv参数
        self.group_module = IntelligentGroupModule(
            config["group"], group_mode, convolution_kernel_size
        )
        self.skeleton_module = IntelligentSkeletonModule(
            config["skeleton"], skeleton_group_size
        )
        self.digest_module = IntelligentDigestModule(config["digest"])
        self.refinement_module = IntelligentRefinementModule(
            config.get("skeleton_refinement", {}),
            convolution_layer=convolution_layer,
            convolution_kernel_size=convolution_kernel_size,
            convolution_result_num=convolution_result_num,
            top_k=top_k,
            self_refine_count=self_refine_count,
            self_refine_best_of=self_refine_best_of,
        )
        
        # 创建节点
        self.group_node = Node(self._async_wrapper(self.group_module), worker_num=worker_num)
        self.skeleton_node = Node(self._async_wrapper(self.skeleton_module), worker_num=worker_num)
        self.digest_node = Node(self._async_wrapper(self.digest_module), worker_num=worker_num)
        self.refinement_node = Node(self._async_wrapper(self.refinement_module), worker_num=worker_num)
        self.output_node = Node(self.output_data, worker_num=worker_num)
        
        all_nodes = [
            self.group_node,
            self.skeleton_node,
            self.digest_node,
            self.refinement_node,
            self.output_node,
        ]
        
        super().__init__(all_nodes, head=self.group_node, tail=self.output_node)
    
    def _async_wrapper(self, async_module):
        """异步执行"""
        def wrapper(survey):
            try:
                return asyncio.run(async_module(survey))
            except Exception as e:
                logger.error(f"Error in async wrapper: {e}")
                return survey
        return wrapper
    
    def _connect_nodes(self):
        """连接节点"""
        # 主要处理流程：group -> skeleton -> digest -> output
        self.group_node >> self.skeleton_node >> self.digest_node >> self.output_node

        # 精化流程：digest -> refinement -> output (避免循环连接)
        self.digest_node >> self.refinement_node >> self.output_node
        self.digest_node.set_dst_criteria(self.refinement_node, self.iter_criteria)
        self.digest_node.set_dst_criteria(self.output_node, lambda survey: not self.iter_criteria(survey))
    
    def output_data(self, survey: Survey) -> Optional[Survey]:
        """输出数据"""
        if not self.output_each_block and survey.block_cycle_count < self.block_count:
            logger.info(f"Survey {survey.title} has not reached block count, not outputting.")
            return None

        logger.info(f"Survey {survey.title} processing completed. Output ready.")
        return survey
    
    def iter_criteria(self, survey: Survey) -> bool:
        """迭代标准"""
        current_cycle = survey.block_cycle_count
        should_iterate = current_cycle < self.block_count

        if should_iterate:
            logger.info(f"Survey {survey.title} will continue iteration. Cycle: {current_cycle}/{self.block_count}")
        else:
            logger.info(f"Survey {survey.title} has completed all iterations.")

        return should_iterate
    
    async def cleanup(self):
        """清理所有模块的资源"""
        modules = [self.group_module, self.skeleton_module, self.digest_module, self.refinement_module]
        for module in modules:
            if hasattr(module, 'cleanup'):
                await module.cleanup()
