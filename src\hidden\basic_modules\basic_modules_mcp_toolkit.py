#!/usr/bin/env python3
"""
Basic Modules MCP Toolkit - 基础模块处理工具包
将digest、group、skeleton等基础算法转换为智能工具
"""

import json
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from camel.toolkits import BaseToolkit, FunctionTool

from src.data_structure import Survey, Digest, Skeleton
from src.hidden.basic_modules.digest_module import DigestModule, SingleDigestModule
from src.hidden.basic_modules.group_module import GroupModule
from src.hidden.basic_modules.skeleton_init_module import SkeletonInitModule

logger = logging.getLogger(__name__)


class BasicModulesToolkit(BaseToolkit):
    """基础模块工具包 - 提供digest、group、skeleton的智能处理能力"""

    def __init__(self, config: Dict[str, Any], timeout: Optional[float] = None):
        """初始化基础模块工具包
        
        Args:
            config: 模块配置
            timeout: 操作超时时间
        """
        super().__init__(timeout=timeout)
        self.config = config
        
        # 初始化各个模块
        self._init_modules()
        
        logger.info("BasicModulesToolkit initialized")

    def _init_modules(self):
        """初始化各个基础模块"""
        try:
            self.digest_module = DigestModule(self.config.get("digest", {}))
            self.group_module = GroupModule(
                self.config.get("group", {}),
                mode=self.config.get("group_mode", "llm"),
                digest_batch=self.config.get("digest_batch", 5)
            )
            self.skeleton_module = SkeletonInitModule(self.config.get("skeleton", {}))
        except Exception as e:
            logger.warning(f"Some modules failed to initialize: {e}")

    @FunctionTool
    async def process_survey_digests(
        self,
        survey_data: Dict[str, Any],
        digest_strategy: str = "comprehensive",
        quality_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """处理调研摘要生成
        
        Args:
            survey_data: 调研数据
            digest_strategy: 摘要策略 ("comprehensive", "focused", "brief")
            quality_threshold: 质量阈值
            
        Returns:
            Dict[str, Any]: 处理结果包含生成的摘要和质量评估
        """
        try:
            logger.info(f"Processing survey digests with strategy: {digest_strategy}")
            
            # 创建Survey对象
            survey = Survey(survey_data)
            
            # 验证输入数据
            if not hasattr(survey, 'papers') or not survey.papers:
                return {
                    "success": False,
                    "error": "No papers found in survey data",
                    "digests": {}
                }
            
            # 根据策略调整处理参数
            processing_config = await self._configure_digest_processing(
                digest_strategy, len(survey.papers)
            )
            
            # 执行摘要处理
            if hasattr(self, 'digest_module'):
                processed_survey = self.digest_module.forward(survey)
                digests = processed_survey.digests
            else:
                # 回退到简单处理
                digests = await self._simple_digest_processing(survey, processing_config)
            
            # 质量评估
            quality_scores = await self._evaluate_digests_quality(digests, quality_threshold)
            
            # 过滤低质量摘要
            filtered_digests = {
                k: v for k, v in digests.items() 
                if quality_scores.get(k, 0) >= quality_threshold
            }
            
            return {
                "success": True,
                "digests": {k: v.to_dict() if hasattr(v, 'to_dict') else str(v) 
                          for k, v in filtered_digests.items()},
                "quality_scores": quality_scores,
                "processing_strategy": digest_strategy,
                "total_digests": len(digests),
                "high_quality_digests": len(filtered_digests),
                "average_quality": sum(quality_scores.values()) / len(quality_scores) if quality_scores else 0
            }
            
        except Exception as e:
            logger.error(f"Error processing survey digests: {e}")
            return {
                "success": False,
                "error": str(e),
                "digests": {}
            }

    @FunctionTool
    async def group_papers_intelligently(
        self,
        survey_data: Dict[str, Any],
        grouping_mode: str = "llm",
        target_group_size: int = 5,
        grouping_criteria: List[str] = None
    ) -> Dict[str, Any]:
        """智能分组论文
        
        Args:
            survey_data: 调研数据
            grouping_mode: 分组模式 ("llm", "random", "semantic")
            target_group_size: 目标分组大小
            grouping_criteria: 分组标准
            
        Returns:
            Dict[str, Any]: 分组结果
        """
        try:
            logger.info(f"Grouping papers with mode: {grouping_mode}")
            
            # 创建Survey对象
            survey = Survey(survey_data)
            
            if not hasattr(survey, 'papers') or not survey.papers:
                return {
                    "success": False,
                    "error": "No papers found in survey data",
                    "groups": []
                }
            
            # 配置分组参数
            grouping_config = {
                "mode": grouping_mode,
                "batch_size": target_group_size,
                "criteria": grouping_criteria or ["topic_similarity", "methodology", "relevance"]
            }
            
            # 执行分组
            if hasattr(self, 'group_module'):
                grouped_survey = self.group_module.forward(survey)
                groups = self._extract_groups_info(grouped_survey)
            else:
                # 回退到简单分组
                groups = await self._simple_paper_grouping(survey, grouping_config)
            
            # 分组质量评估
            group_quality = await self._evaluate_grouping_quality(groups)
            
            return {
                "success": True,
                "groups": groups,
                "grouping_mode": grouping_mode,
                "target_group_size": target_group_size,
                "actual_groups": len(groups),
                "group_quality": group_quality,
                "grouping_criteria": grouping_criteria,
                "recommendations": await self._generate_grouping_recommendations(groups, group_quality)
            }
            
        except Exception as e:
            logger.error(f"Error grouping papers: {e}")
            return {
                "success": False,
                "error": str(e),
                "groups": []
            }

    @FunctionTool
    async def initialize_survey_skeleton(
        self,
        survey_data: Dict[str, Any],
        skeleton_depth: str = "medium",
        structure_type: str = "hierarchical",
        include_methodology: bool = True
    ) -> Dict[str, Any]:
        """初始化调研骨架
        
        Args:
            survey_data: 调研数据
            skeleton_depth: 骨架深度 ("shallow", "medium", "deep")
            structure_type: 结构类型 ("hierarchical", "flat", "network")
            include_methodology: 是否包含方法论部分
            
        Returns:
            Dict[str, Any]: 骨架初始化结果
        """
        try:
            logger.info(f"Initializing skeleton with depth: {skeleton_depth}")
            
            # 创建Survey对象
            survey = Survey(survey_data)
            
            # 配置骨架参数
            skeleton_config = {
                "depth": skeleton_depth,
                "structure_type": structure_type,
                "include_methodology": include_methodology,
                "auto_adapt": True
            }
            
            # 执行骨架初始化
            if hasattr(self, 'skeleton_module'):
                skeleton_survey = self.skeleton_module.forward(survey)
                skeleton = skeleton_survey.skeleton
            else:
                # 回退到简单骨架生成
                skeleton = await self._simple_skeleton_generation(survey, skeleton_config)
            
            # 骨架质量评估
            skeleton_quality = await self._evaluate_skeleton_quality(skeleton, survey.title)
            
            # 生成骨架统计信息
            skeleton_stats = await self._analyze_skeleton_structure(skeleton)
            
            return {
                "success": True,
                "skeleton": skeleton.to_dict() if hasattr(skeleton, 'to_dict') else skeleton,
                "skeleton_depth": skeleton_depth,
                "structure_type": structure_type,
                "quality_score": skeleton_quality,
                "structure_stats": skeleton_stats,
                "recommendations": await self._generate_skeleton_recommendations(skeleton, skeleton_quality)
            }
            
        except Exception as e:
            logger.error(f"Error initializing skeleton: {e}")
            return {
                "success": False,
                "error": str(e),
                "skeleton": None
            }

    @FunctionTool
    async def optimize_digest_quality(
        self,
        digest_data: Dict[str, Any],
        optimization_target: str = "comprehensiveness",
        context_info: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """优化摘要质量
        
        Args:
            digest_data: 摘要数据
            optimization_target: 优化目标 ("comprehensiveness", "conciseness", "accuracy")
            context_info: 上下文信息
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        try:
            logger.info(f"Optimizing digest quality for target: {optimization_target}")
            
            # 分析当前摘要质量
            current_quality = await self._analyze_digest_quality(digest_data)
            
            # 根据优化目标调整策略
            optimization_strategy = await self._determine_optimization_strategy(
                optimization_target, current_quality
            )
            
            # 执行优化
            optimized_digest = await self._apply_digest_optimization(
                digest_data, optimization_strategy, context_info
            )
            
            # 评估优化效果
            optimized_quality = await self._analyze_digest_quality(optimized_digest)
            improvement = optimized_quality - current_quality
            
            return {
                "success": True,
                "original_digest": digest_data,
                "optimized_digest": optimized_digest,
                "optimization_target": optimization_target,
                "quality_improvement": improvement,
                "original_quality": current_quality,
                "optimized_quality": optimized_quality,
                "optimization_strategy": optimization_strategy
            }
            
        except Exception as e:
            logger.error(f"Error optimizing digest quality: {e}")
            return {
                "success": False,
                "error": str(e),
                "original_digest": digest_data,
                "optimized_digest": digest_data
            }

    @FunctionTool
    async def merge_related_digests(
        self,
        digest_list: List[Dict[str, Any]],
        merge_strategy: str = "semantic",
        similarity_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """合并相关摘要
        
        Args:
            digest_list: 摘要列表
            merge_strategy: 合并策略 ("semantic", "topic", "structural")
            similarity_threshold: 相似度阈值
            
        Returns:
            Dict[str, Any]: 合并结果
        """
        try:
            logger.info(f"Merging digests with strategy: {merge_strategy}")
            
            if len(digest_list) < 2:
                return {
                    "success": True,
                    "merged_digests": digest_list,
                    "merge_operations": 0,
                    "message": "Insufficient digests for merging"
                }
            
            # 计算相似度矩阵
            similarity_matrix = await self._compute_digest_similarities(
                digest_list, merge_strategy
            )
            
            # 识别可合并的摘要对
            merge_candidates = await self._identify_merge_candidates(
                similarity_matrix, similarity_threshold
            )
            
            # 执行合并操作
            merged_digests = await self._execute_digest_merging(
                digest_list, merge_candidates, merge_strategy
            )
            
            # 验证合并质量
            merge_quality = await self._evaluate_merge_quality(
                digest_list, merged_digests
            )
            
            return {
                "success": True,
                "original_count": len(digest_list),
                "merged_count": len(merged_digests),
                "merged_digests": merged_digests,
                "merge_operations": len(digest_list) - len(merged_digests),
                "merge_strategy": merge_strategy,
                "similarity_threshold": similarity_threshold,
                "merge_quality": merge_quality
            }
            
        except Exception as e:
            logger.error(f"Error merging digests: {e}")
            return {
                "success": False,
                "error": str(e),
                "merged_digests": digest_list
            }

    # 私有辅助方法
    async def _configure_digest_processing(self, strategy: str, paper_count: int) -> Dict[str, Any]:
        """配置摘要处理参数"""
        base_config = {
            "max_length": 500,
            "include_citations": True,
            "focus_keywords": []
        }
        
        if strategy == "comprehensive":
            base_config.update({
                "max_length": 800,
                "detail_level": "high",
                "include_methodology": True
            })
        elif strategy == "focused":
            base_config.update({
                "max_length": 300,
                "detail_level": "medium",
                "focus_main_contributions": True
            })
        elif strategy == "brief":
            base_config.update({
                "max_length": 200,
                "detail_level": "low",
                "essence_only": True
            })
        
        # 根据论文数量调整
        if paper_count > 50:
            base_config["batch_processing"] = True
            base_config["parallel_workers"] = min(5, paper_count // 10)
        
        return base_config

    async def _simple_digest_processing(self, survey: Survey, config: Dict[str, Any]) -> Dict[str, Any]:
        """简单摘要处理"""
        digests = {}
        
        # 简单的摘要生成逻辑
        for paper_id, paper in survey.papers.items():
            content = paper.get("content", "")
            
            # 生成简单摘要
            if len(content) > config.get("max_length", 500):
                summary = content[:config.get("max_length", 500)] + "..."
            else:
                summary = content
            
            digest = Digest([paper], survey.title)
            digest.content = summary
            digests[paper_id] = digest
        
        return digests

    async def _evaluate_digests_quality(self, digests: Dict[str, Any], threshold: float) -> Dict[str, float]:
        """评估摘要质量"""
        quality_scores = {}
        
        for digest_id, digest in digests.items():
            try:
                content = digest.content if hasattr(digest, 'content') else str(digest)
                
                # 简单的质量评估指标
                length_score = min(1.0, len(content) / 300)  # 长度适中
                structure_score = 0.8 if "\n" in content else 0.5  # 有结构
                completeness_score = 0.9 if len(content) > 100 else 0.3  # 完整性
                
                overall_score = (length_score + structure_score + completeness_score) / 3
                quality_scores[digest_id] = overall_score
                
            except Exception as e:
                logger.warning(f"Error evaluating digest {digest_id}: {e}")
                quality_scores[digest_id] = 0.5
        
        return quality_scores

    def _extract_groups_info(self, grouped_survey: Survey) -> List[Dict[str, Any]]:
        """提取分组信息"""
        groups = []
        
        if hasattr(grouped_survey, 'digests'):
            for digest_id, digest in grouped_survey.digests.items():
                group_info = {
                    "group_id": digest_id,
                    "papers": digest.bibkeys if hasattr(digest, 'bibkeys') else [],
                    "paper_count": len(digest.bibkeys) if hasattr(digest, 'bibkeys') else 0,
                    "topic": digest.survey_title if hasattr(digest, 'survey_title') else ""
                }
                groups.append(group_info)
        
        return groups

    async def _simple_paper_grouping(self, survey: Survey, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """简单论文分组"""
        papers = list(survey.papers.items())
        batch_size = config.get("batch_size", 5)
        groups = []
        
        for i in range(0, len(papers), batch_size):
            batch = papers[i:i + batch_size]
            group = {
                "group_id": f"group_{i // batch_size}",
                "papers": [paper_id for paper_id, _ in batch],
                "paper_count": len(batch),
                "topic": f"Topic Group {i // batch_size + 1}"
            }
            groups.append(group)
        
        return groups

    async def _evaluate_grouping_quality(self, groups: List[Dict[str, Any]]) -> float:
        """评估分组质量"""
        if not groups:
            return 0.0
        
        # 简单的分组质量评估
        group_sizes = [group.get("paper_count", 0) for group in groups]
        
        # 计算大小均衡性
        avg_size = sum(group_sizes) / len(group_sizes)
        size_variance = sum((size - avg_size) ** 2 for size in group_sizes) / len(group_sizes)
        balance_score = 1.0 / (1.0 + size_variance / avg_size) if avg_size > 0 else 0.5
        
        # 计算覆盖完整性
        total_papers = sum(group_sizes)
        coverage_score = min(1.0, total_papers / max(1, max(group_sizes) * len(groups)))
        
        return (balance_score + coverage_score) / 2

    async def _generate_grouping_recommendations(
        self, groups: List[Dict[str, Any]], quality: float
    ) -> List[str]:
        """生成分组建议"""
        recommendations = []
        
        if quality < 0.6:
            recommendations.append("建议调整分组参数以提高分组质量")
        
        group_sizes = [group.get("paper_count", 0) for group in groups]
        if max(group_sizes) - min(group_sizes) > 3:
            recommendations.append("建议重新平衡分组大小")
        
        if len(groups) < 3:
            recommendations.append("建议增加分组数量以提高分析粒度")
        elif len(groups) > 10:
            recommendations.append("建议减少分组数量以便于管理")
        
        return recommendations

    async def _simple_skeleton_generation(self, survey: Survey, config: Dict[str, Any]) -> Dict[str, Any]:
        """简单骨架生成"""
        depth = config.get("depth", "medium")
        
        # 基础骨架结构
        skeleton = {
            "title": survey.title,
            "sections": []
        }
        
        # 根据深度生成不同详细程度的骨架
        if depth == "shallow":
            skeleton["sections"] = [
                {"id": "intro", "title": "引言", "level": 1},
                {"id": "content", "title": "主要内容", "level": 1},
                {"id": "conclusion", "title": "结论", "level": 1}
            ]
        elif depth == "medium":
            skeleton["sections"] = [
                {"id": "intro", "title": "引言", "level": 1},
                {"id": "background", "title": "背景", "level": 2},
                {"id": "methods", "title": "方法", "level": 1},
                {"id": "results", "title": "结果", "level": 1},
                {"id": "discussion", "title": "讨论", "level": 1},
                {"id": "conclusion", "title": "结论", "level": 1}
            ]
        else:  # deep
            skeleton["sections"] = [
                {"id": "intro", "title": "引言", "level": 1},
                {"id": "background", "title": "背景与相关工作", "level": 1},
                {"id": "problem", "title": "问题定义", "level": 2},
                {"id": "related", "title": "相关工作", "level": 2},
                {"id": "methods", "title": "方法论", "level": 1},
                {"id": "approach", "title": "技术方法", "level": 2},
                {"id": "implementation", "title": "实现细节", "level": 2},
                {"id": "results", "title": "实验结果", "level": 1},
                {"id": "analysis", "title": "结果分析", "level": 2},
                {"id": "discussion", "title": "讨论", "level": 1},
                {"id": "limitations", "title": "局限性", "level": 2},
                {"id": "future", "title": "未来工作", "level": 2},
                {"id": "conclusion", "title": "结论", "level": 1}
            ]
        
        return skeleton

    async def _evaluate_skeleton_quality(self, skeleton: Dict[str, Any], title: str) -> float:
        """评估骨架质量"""
        try:
            sections = skeleton.get("sections", [])
            
            if not sections:
                return 0.0
            
            # 结构完整性评估
            essential_sections = ["intro", "content", "conclusion"]
            has_essential = sum(1 for s in sections if s.get("id", "").startswith(tuple(essential_sections)))
            completeness_score = min(1.0, has_essential / len(essential_sections))
            
            # 层次合理性评估
            levels = [s.get("level", 1) for s in sections]
            level_variety = len(set(levels))
            hierarchy_score = min(1.0, level_variety / 3)  # 希望有多层次结构
            
            # 长度适中性评估
            section_count = len(sections)
            length_score = min(1.0, section_count / 8) if section_count < 15 else 0.8
            
            return (completeness_score + hierarchy_score + length_score) / 3
            
        except Exception as e:
            logger.warning(f"Error evaluating skeleton quality: {e}")
            return 0.5

    async def _analyze_skeleton_structure(self, skeleton: Dict[str, Any]) -> Dict[str, Any]:
        """分析骨架结构"""
        sections = skeleton.get("sections", [])
        
        level_counts = {}
        for section in sections:
            level = section.get("level", 1)
            level_counts[level] = level_counts.get(level, 0) + 1
        
        return {
            "total_sections": len(sections),
            "level_distribution": level_counts,
            "max_depth": max(level_counts.keys()) if level_counts else 0,
            "structure_type": "hierarchical" if len(level_counts) > 1 else "flat"
        }

    async def _generate_skeleton_recommendations(
        self, skeleton: Dict[str, Any], quality: float
    ) -> List[str]:
        """生成骨架建议"""
        recommendations = []
        
        sections = skeleton.get("sections", [])
        
        if quality < 0.6:
            recommendations.append("建议完善骨架结构以提高质量")
        
        if len(sections) < 5:
            recommendations.append("建议增加更多章节以丰富内容结构")
        elif len(sections) > 15:
            recommendations.append("建议简化骨架结构以提高可读性")
        
        levels = [s.get("level", 1) for s in sections]
        if len(set(levels)) == 1:
            recommendations.append("建议增加层次结构以提高组织性")
        
        return recommendations

    async def _analyze_digest_quality(self, digest_data: Dict[str, Any]) -> float:
        """分析摘要质量"""
        content = digest_data.get("content", "")
        
        # 简单的质量指标
        length_score = min(1.0, len(content) / 400)
        structure_score = 0.8 if content.count('\n') > 2 else 0.5
        keyword_score = 0.9 if any(word in content.lower() for word in ["方法", "结果", "结论", "研究"]) else 0.3
        
        return (length_score + structure_score + keyword_score) / 3

    async def _determine_optimization_strategy(
        self, target: str, current_quality: float
    ) -> Dict[str, Any]:
        """确定优化策略"""
        strategies = {
            "comprehensiveness": {
                "expand_content": True,
                "add_details": True,
                "include_examples": True
            },
            "conciseness": {
                "remove_redundancy": True,
                "focus_key_points": True,
                "simplify_language": True
            },
            "accuracy": {
                "verify_facts": True,
                "check_citations": True,
                "validate_claims": True
            }
        }
        
        strategy = strategies.get(target, strategies["comprehensiveness"])
        strategy["intensity"] = "high" if current_quality < 0.5 else "moderate"
        
        return strategy

    async def _apply_digest_optimization(
        self, digest_data: Dict[str, Any], strategy: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """应用摘要优化"""
        # 简化的优化逻辑
        optimized = digest_data.copy()
        content = digest_data.get("content", "")
        
        if strategy.get("expand_content"):
            optimized["content"] = content + "\n\n[扩展内容: 基于上下文的详细分析]"
        elif strategy.get("remove_redundancy"):
            # 简单的去重逻辑
            lines = content.split('\n')
            unique_lines = list(dict.fromkeys(lines))  # 保持顺序去重
            optimized["content"] = '\n'.join(unique_lines)
        
        return optimized

    async def _compute_digest_similarities(
        self, digest_list: List[Dict[str, Any]], strategy: str
    ) -> List[List[float]]:
        """计算摘要相似度矩阵"""
        n = len(digest_list)
        similarity_matrix = [[0.0] * n for _ in range(n)]
        
        for i in range(n):
            for j in range(i + 1, n):
                # 简单的相似度计算
                content1 = digest_list[i].get("content", "").lower()
                content2 = digest_list[j].get("content", "").lower()
                
                # 基于共同词汇的简单相似度
                words1 = set(content1.split())
                words2 = set(content2.split())
                
                if len(words1) + len(words2) == 0:
                    similarity = 0.0
                else:
                    similarity = len(words1 & words2) / len(words1 | words2)
                
                similarity_matrix[i][j] = similarity
                similarity_matrix[j][i] = similarity
        
        return similarity_matrix

    async def _identify_merge_candidates(
        self, similarity_matrix: List[List[float]], threshold: float
    ) -> List[Tuple[int, int, float]]:
        """识别合并候选"""
        candidates = []
        n = len(similarity_matrix)
        
        for i in range(n):
            for j in range(i + 1, n):
                if similarity_matrix[i][j] >= threshold:
                    candidates.append((i, j, similarity_matrix[i][j]))
        
        # 按相似度降序排序
        candidates.sort(key=lambda x: x[2], reverse=True)
        
        return candidates

    async def _execute_digest_merging(
        self, digest_list: List[Dict[str, Any]], 
        candidates: List[Tuple[int, int, float]], 
        strategy: str
    ) -> List[Dict[str, Any]]:
        """执行摘要合并"""
        merged = []
        used_indices = set()
        
        # 执行合并操作
        for i, j, similarity in candidates:
            if i not in used_indices and j not in used_indices:
                # 合并两个摘要
                merged_digest = {
                    "content": digest_list[i].get("content", "") + "\n\n" + digest_list[j].get("content", ""),
                    "merged_from": [i, j],
                    "similarity": similarity
                }
                merged.append(merged_digest)
                used_indices.add(i)
                used_indices.add(j)
        
        # 添加未合并的摘要
        for idx, digest in enumerate(digest_list):
            if idx not in used_indices:
                merged.append(digest)
        
        return merged

    async def _evaluate_merge_quality(
        self, original: List[Dict[str, Any]], merged: List[Dict[str, Any]]
    ) -> float:
        """评估合并质量"""
        if not original or not merged:
            return 0.0
        
        # 简单的合并质量评估
        compression_ratio = len(merged) / len(original)
        content_preservation = min(1.0, sum(len(d.get("content", "")) for d in merged) / 
                                      sum(len(d.get("content", "")) for d in original))
        
        return (compression_ratio + content_preservation) / 2


# 导出工具包
def get_basic_modules_tools() -> List[FunctionTool]:
    """获取基础模块相关的工具列表"""
    toolkit = BasicModulesToolkit({})
    return toolkit.get_tools()
