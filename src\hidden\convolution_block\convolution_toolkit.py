import logging
import random
import numpy as np
import math
import json
from typing import List, Dict, Any, Optional

from tenacity import retry, stop_after_attempt, after_log
from camel.toolkits import BaseToolkit, FunctionTool

from src.base_method.data import Dataset
from src.data_structure import Feedback, Skeleton, Survey
from src.hidden.convolution_block.neurons import (
    ModifyOutlineNeuron,
    EvalOutlineNeuron,
    ConvolutionKernelNeuron,
)

logger = logging.getLogger(__name__)


class ConvolutionLayerToolkit(BaseToolkit):
    """卷积层处理工具包，用于管理卷积层的整体流程"""
    
    def __init__(
        self,
        convolution_layer: int = 3,
        receptive_field: int = 3,
        result_num: int = 5,
        top_k: int = 5,
        config: Dict[str, Any] = None,
        timeout: Optional[float] = None
    ):
        """初始化卷积层工具包
        
        Args:
            convolution_layer: 卷积层的数量
            receptive_field: 感受野大小
            result_num: 生成结果的数量
            top_k: 保留的顶部结果数量
            config: 配置参数
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.convolution_layer = convolution_layer
        self.top_k = top_k
        self.receptive_field = receptive_field
        self.result_num = result_num
        self.config = config or {}
        
        logger.info("ConvolutionLayerToolkit initialized")
    
    async def process_convolution_layer(
        self,
        survey_dict: Dict[str, Any],
        utilise_results: List[Dict[str, Any]],
        origin_outline: str,
        convolution_kernel_url: str,
        modify_outline_url: str,
        eval_outline_url: str
    ) -> Dict[str, Any]:
        """处理完整的卷积层流程
        
        Args:
            survey_dict: 调研字典
            utilise_results: 利用阶段的结果列表
            origin_outline: 原始大纲
            convolution_kernel_url: 卷积核服务URL
            modify_outline_url: 修改大纲服务URL
            eval_outline_url: 评估大纲服务URL
            
        Returns:
            Dict[str, Any]: 处理后的调研字典
        """
        # 导入客户端工具
        from mcp_toolkit import MCPClient
        
        # 恢复对象
        survey = Survey.from_dict(survey_dict)
        feedback_results = [Feedback.from_dict(result) for result in utilise_results]
        bibkeys = list(survey.papers.keys())
        
        # 创建客户端连接
        convolution_client = MCPClient(convolution_kernel_url)
        modify_client = MCPClient(modify_outline_url)
        eval_client = MCPClient(eval_outline_url)
        
        try:
            # 连接服务
            await convolution_client.connect()
            await modify_client.connect()
            await eval_client.connect()
            
            # 获取工具
            convolution_tool = next(
                tool for tool in convolution_client.get_tools() 
                if tool.get_function_name() == "apply_convolution_kernel"
            )
            
            modify_tool = next(
                tool for tool in modify_client.get_tools() 
                if tool.get_function_name() == "modify_outline"
            )
            
            eval_tool = next(
                tool for tool in eval_client.get_tools() 
                if tool.get_function_name() == "evaluate_outline"
            )
            
            # 处理卷积层
            current_block_avg_scores = []
            
            # 评估原始大纲
            origin_outline_eval = await eval_tool.async_call(
                title=survey.title,
                outline=origin_outline
            )
            origin_outline_score = origin_outline_eval.get("score", 0)
            
            # 初始化旧的分组建议
            old_grouped_suggestions = [[suggestion] for suggestion in feedback_results]
            
            # 初始批次卷积处理
            conv_results_old = []
            for old_grouped_suggestion in old_grouped_suggestions:
                result = await self._process_single_convolution(
                    survey.title,
                    origin_outline,
                    old_grouped_suggestion,
                    bibkeys,
                    convolution_tool,
                    modify_tool,
                    eval_tool
                )
                conv_results_old.append(result)
            
            # 提取结果
            utilise_results = [result["suggestion"] for result in conv_results_old]
            outlines = [result["outline"] for result in conv_results_old]
            scores = [result["score"] for result in conv_results_old]
            
            # 计算平均分数
            avg_score = np.mean(scores)
            current_block_avg_scores.append(avg_score)
            
            logger.info(
                f"Survey {survey.title}, block cycle count {survey.block_cycle_count}, origin outline score: {origin_outline_score}\n"
                f"Current Layer Outline Scores: {scores}, Avg Score: {avg_score}, Max Score: {max(scores)}"
            )
            
            # 进行多层卷积处理
            for layer_idx in range(self.convolution_layer):
                logger.info(
                    f"Survey {survey.title}, block cycle count {survey.block_cycle_count}, Convolution Layer: {layer_idx} Start"
                )
                
                target_result_num = len(utilise_results) / self.receptive_field
                
                # 卷积前向处理
                conv_results_new = await self._convolution_forward(
                    survey,
                    origin_outline,
                    utilise_results,
                    bibkeys,
                    convolution_tool,
                    modify_tool,
                    eval_tool
                )
                
                if target_result_num > self.result_num:
                    # 执行池化操作
                    conv_results = conv_results_old = conv_results_new
                    utilise_results = [result["suggestion"] for result in conv_results]
                    outlines = [result["outline"] for result in conv_results]
                    scores = [result["score"] for result in conv_results]
                else:
                    # 合并旧结果和新结果
                    conv_results = conv_results_old + conv_results_new
                    conv_suggestions = [result["suggestion"] for result in conv_results]
                    outlines = [result["outline"] for result in conv_results]
                    scores = [result["score"] for result in conv_results]
                    
                    # 剪枝保留top-k结果
                    utilise_results = await self._prune_top_k(
                        conv_suggestions,
                        scores,
                        self.top_k
                    )
                    conv_results_old = conv_results_new
                
                # 计算新的分数
                conv_new_scores = [result["score"] for result in conv_results_new]
                avg_score = np.mean(conv_new_scores)
                current_block_avg_scores.append(avg_score)
                
                # 记录日志
                if target_result_num > self.result_num:
                    logger.info(
                        f"Survey {survey.title}, block cycle count {survey.block_cycle_count}, "
                        f"Convolution Layer: {layer_idx} Pooling Finished\n"
                        f"Current Layer Outline Scores: {conv_new_scores}, Avg Score: {avg_score}, Max Score: {max(conv_new_scores)}"
                    )
                else:
                    logger.info(
                        f"Survey {survey.title}, block cycle count {survey.block_cycle_count}, "
                        f"Convolution Layer: {layer_idx} Convolution Finished\n"
                        f"Current Layer Outline Scores: {conv_new_scores}, Avg Score: {avg_score}, Max Score: {max(conv_new_scores)}"
                    )
            
            # 获取最佳大纲
            new_outline = (await self._prune_top_k(outlines, scores, 1))[0]
            
            # 更新调研对象
            survey = survey.update_outline(
                "```markdown\n"
                + new_outline.all_skeleton(construction=True, analysis=True, with_index=True)
                + "\n```"
            )
            survey.skeleton.suggestion = new_outline.suggestion
            survey.skeleton.eval_score = new_outline.eval_score
            survey.skeleton.eval_detail = new_outline.eval_detail
            survey.block_avg_score.append(current_block_avg_scores)
            survey.conv_layer = self.convolution_layer
            survey.receptive_field = self.receptive_field
            survey.top_k = self.top_k
            survey.result_num = self.result_num
            
            return survey.to_dict()
            
        finally:
            # 关闭客户端连接
            await convolution_client.disconnect()
            await modify_client.disconnect()
            await eval_client.disconnect()
    
    async def _process_single_convolution(
        self,
        title,
        origin_outline,
        suggestions,
        bibkeys,
        convolution_tool,
        modify_tool,
        eval_tool
    ):
        """处理单个卷积操作"""
        # 调用卷积核生成新建议
        conv_result = await convolution_tool.async_call(
            title=title,
            origin_outline=origin_outline,
            suggestions=[s.to_dict() for s in suggestions],
            bibkeys=bibkeys
        )
        
        new_suggestion = Feedback.from_dict(conv_result["suggestion"])
        
        # 根据新建议修改大纲
        modify_result = await modify_tool.async_call(
            title=title,
            suggestions=[new_suggestion.to_dict()],
            origin_outline=origin_outline,
            bibkeys=bibkeys,
            mode="single_suggestion"
        )
        
        new_outline = Skeleton.from_dict(modify_result["outline"])
        
        # 评估新大纲
        eval_result = await eval_tool.async_call(
            title=title,
            outline=new_outline.all_skeleton(construction=True, analysis=True, with_index=True)
        )
        
        score = eval_result["score"]
        eval_detail = eval_result["detail"]
        
        # 更新新建议和大纲的评分信息
        new_suggestion.score = score
        new_suggestion.eval_detail = eval_detail
        new_outline.suggestion = new_suggestion.content
        new_outline.eval_score = score
        new_outline.eval_detail = eval_detail
        
        return {
            "suggestion": new_suggestion,
            "outline": new_outline,
            "score": score,
            "eval_detail": eval_detail
        }
    
    async def _convolution_forward(
        self,
        survey,
        origin_outline,
        suggestions,
        bibkeys,
        convolution_tool,
        modify_tool,
        eval_tool
    ):
        """卷积前向处理"""
        # 采样建议
        old_grouped_suggestions = await self._sample_suggestions(
            suggestions, self.receptive_field, self.result_num
        )
        
        logger.info(
            f"Survey {survey.title}, Sample Finished, suggestions count: {len(old_grouped_suggestions)}"
        )
        
        # 处理每组建议
        conv_results_new = []
        for old_grouped_suggestion in old_grouped_suggestions:
            result = await self._process_single_convolution(
                survey.title,
                origin_outline,
                old_grouped_suggestion,
                bibkeys,
                convolution_tool,
                modify_tool,
                eval_tool
            )
            conv_results_new.append(result)
        
        logger.info(
            f"Survey {survey.title}, Convolution Finished, suggestions count: {len(conv_results_new)}"
        )
        return conv_results_new
    
    async def _sample_suggestions(
        self, suggestions, receptive_field, result_num
    ) -> List[List[Feedback]]:
        """采样建议"""
        sampled_suggestions = []
        seen_combinations = set()
        all_possible_combinations = math.comb(len(suggestions), receptive_field)
        
        if result_num < len(suggestions) / receptive_field:
            logger.warning(
                f"Result num ({result_num}) is less than suggestions count ({len(suggestions)}) divided by receptive field ({receptive_field}), use no duplicate sampling"
            )
            suggestions_copy = suggestions.copy()
            random.shuffle(suggestions_copy)
            sampled_suggestions = [
                suggestions_copy[i : i + receptive_field]
                for i in range(0, len(suggestions_copy), receptive_field)
            ]
        else:
            scores = [suggestion.score for suggestion in suggestions]
            scores = np.array(scores)
            scores = scores / scores.sum() if scores.sum() > 0 else np.ones(len(scores)) / len(scores)
            
            available_indices = np.arange(len(suggestions))
            while len(sampled_suggestions) < result_num:
                if len(available_indices) < receptive_field:
                    sampled_indices = np.random.choice(
                        available_indices, size=len(available_indices), replace=True
                    )
                    logger.warning(
                        f"Sampled indices ({available_indices}) is less than receptive field({receptive_field}), use all available indices: {sampled_indices}"
                    )
                else:
                    sampled_indices = np.random.choice(
                        len(suggestions),
                        size=receptive_field,
                        replace=False,
                        p=scores,
                    )
                
                sampled_group = tuple(sorted(sampled_indices))
                if (
                    sampled_group not in seen_combinations
                    or len(seen_combinations) >= all_possible_combinations
                ):
                    seen_combinations.add(sampled_group)
                    sampled_suggestions.append(
                        [suggestions[i] for i in sampled_indices]
                    )
        
        return sampled_suggestions
    
    async def _prune_top_k(self, results, scores, top_k):
        """剪枝保留top-k结果"""
        assert len(results) == len(scores)
        # 将score和outline降序排列，获取前top_k个结果，同分项随机选择
        sorted_indices = np.argsort(scores)[::-1]
        sorted_suggestions = [results[i] for i in sorted_indices]
        sorted_scores = [scores[i] for i in sorted_indices]
        
        results_dict = {}
        for outline, score in zip(sorted_suggestions, sorted_scores):
            if score not in results_dict:
                results_dict[score] = []
            results_dict[score].append(outline)
        
        # 获取前top_k个结果
        result = []
        for score in sorted(results_dict.keys(), reverse=True):
            group = results_dict[score]
            if len(result) + len(group) <= top_k:
                result.extend(group)
            else:
                result.extend(random.sample(group, top_k - len(result)))
                break
        return result
    
    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.process_convolution_layer)
        ]


class ConvolutionKernelToolkit(BaseToolkit):
    """卷积核工具包，用于生成新的建议"""
    
    def __init__(
        self, 
        config: Dict[str, Any] = None,
        timeout: Optional[float] = None
    ):
        """初始化卷积核工具包
        
        Args:
            config: 配置参数
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.config = config or {}
        self.conv_neuron = ConvolutionKernelNeuron(self.config)
        
        logger.info("ConvolutionKernelToolkit initialized")
    
    async def apply_convolution_kernel(
        self,
        title: str,
        origin_outline: str,
        suggestions: List[Dict[str, Any]],
        bibkeys: List[str]
    ) -> Dict[str, Any]:
        """应用卷积核生成新的建议
        
        Args:
            title: 调研标题
            origin_outline: 原始大纲
            suggestions: 建议列表
            bibkeys: 文献引用键列表
            
        Returns:
            Dict[str, Any]: 包含新建议的字典
        """
        try:
            # 恢复对象
            feedback_suggestions = [Feedback.from_dict(s) for s in suggestions]
            
            # 调用卷积核神经元
            new_suggestion = self.conv_neuron(title, origin_outline, feedback_suggestions, bibkeys)
            
            logger.info(f"Survey {title}, suggestion conv finished")
            
            return {
                "success": True,
                "suggestion": new_suggestion.to_dict()
            }
            
        except Exception as e:
            logger.error(f"Failed to apply convolution kernel: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.apply_convolution_kernel)
        ]


class ModifyOutlineToolkit(BaseToolkit):
    """修改大纲工具包"""
    
    def __init__(
        self, 
        config: Dict[str, Any] = None,
        timeout: Optional[float] = None
    ):
        """初始化修改大纲工具包
        
        Args:
            config: 配置参数
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.config = config or {}
        
        logger.info("ModifyOutlineToolkit initialized")
    
    async def modify_outline(
        self,
        title: str,
        suggestions: List[Dict[str, Any]],
        origin_outline: str,
        bibkeys: List[str],
        mode: str = "single_suggestion"
    ) -> Dict[str, Any]:
        """根据建议修改大纲
        
        Args:
            title: 调研标题
            suggestions: 建议列表
            origin_outline: 原始大纲
            bibkeys: 文献引用键列表
            mode: 修改模式，"single_suggestion"或"residual"
            
        Returns:
            Dict[str, Any]: 包含修改后大纲的字典
        """
        try:
            # 恢复对象
            feedback_suggestions = [Feedback.from_dict(s) for s in suggestions]
            
            # 创建修改神经元
            modify_neuron = ModifyOutlineNeuron(self.config, mode)
            
            # 修改大纲
            new_outline = modify_neuron(
                title, feedback_suggestions, origin_outline, bibkeys
            )
            
            logger.info(f"Survey {title}, outline modify Finished")
            
            return {
                "success": True,
                "outline": new_outline.to_dict()
            }
            
        except Exception as e:
            logger.error(f"Failed to modify outline: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.modify_outline)
        ]


class EvalOutlineToolkit(BaseToolkit):
    """评估大纲工具包"""
    
    def __init__(
        self, 
        config: Dict[str, Any] = None,
        timeout: Optional[float] = None
    ):
        """初始化评估大纲工具包
        
        Args:
            config: 配置参数
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.config = config or {}
        self.eval_neuron = EvalOutlineNeuron(self.config)
        
        logger.info("EvalOutlineToolkit initialized")
    
    async def evaluate_outline(
        self,
        title: str,
        outline: str
    ) -> Dict[str, Any]:
        """评估大纲质量
        
        Args:
            title: 调研标题
            outline: 大纲内容
            
        Returns:
            Dict[str, Any]: 包含评估结果的字典
        """
        try:
            # 调用评估神经元
            scores_ret = self.eval_neuron(title, outline)
            
            logger.info(f"Survey {title}, Eval Finished")
            
            return {
                "success": True,
                "score": scores_ret[0],
                "detail": scores_ret[1]
            }
            
        except Exception as e:
            logger.error(f"Failed to evaluate outline: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "score": 0,
                "detail": str(e)
            }
    
    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.evaluate_outline)
        ]