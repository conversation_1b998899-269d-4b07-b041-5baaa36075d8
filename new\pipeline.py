"""
MapxReduceV3 核心管道模块
实现从用户任务到综述生成的完整流程
"""

import logging
import json
from pathlib import Path
from typing import Optional, Dict, Any, List

from src.args import parse_args
from src.search.analyse import analyse
from src.encode.encode_pipeline import encode_pipeline
from src.data_structure.survey import Survey

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MainPipeline:
    """
    Main    : Analyse -> Encode -> Hidden -> Decode
    Analyse : Description -> Search[origin]
    Encode  : Group[llm] -> digest initialize
    Hidden  : Parrellel( Eval -> Conv -> Refine ) 
        Eval   : eval root
        Conv   : convolution
        Refine : refine
    Decode  : 
    对齐:eval,conv,refine & paper_json, survey class
    args:
    """

    def __init__(self, args=None):
        self.args = args or parse_args()
        self.surveys = None
        self.analyse = None
        self.encode = None
        self.hidden = None
        self.decode = None
        logger.info("MainPipeline initialized")
        logger.info(f"Configuration: {self._get_config_summary()}")

    def _get_config_summary(self) -> Dict[str, Any]:
        return {
            "topic": getattr(self.args, 'topic', None),
            "top_n": getattr(self.args, 'top_n', 10),
            "digest_group_mode": getattr(self.args, 'digest_group_mode', 'random'),
            "skeleton_group_size": getattr(self.args, 'skeleton_group_size', 3),
            "output_file": getattr(self.args, 'output_file', None)
        }
    
    async def run(self, task: str, description: Optional[str] = None) -> List[Survey]:
        logger.info(f"Starting complete pipeline for task: {task}")
        self.args.update(description)
        try:
            logger.info("Step 1: Task analysis and literature retrieval")
            await self.analyse(self.args)
            
            # 会自动根据args的文件路径保存papers
            logger.info("Step 2: Literature encoding and Survey generation")
            self.surveys = await self.encode(self.args)

            logger.info("Step 3: Convolution and Refinement")
            self.surveys = await self.hidden(self.surveys,self.args) # perhaps resnet?
            
            logger.info("Step 4: Generate")
            # writting
            # return surveys
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            raise


async def run_pipeline(args=None) -> List[Survey]:
    # 多一个功能，将user给的文献先放在test/"topic"/下
    if args is None:
        args = parse_args()

    pipeline = MainPipeline(args)
    
    return pipeline.run(task, description)


if __name__ == "__main__":
    # 命令行运行
    try:
        surveys = run_pipeline()
        print(f"Pipeline completed successfully. Generated {len(surveys)} surveys:")

    except Exception as e:
        logger.error(f"Pipeline execution failed: {e}")
        raise