import asyncio
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator
from contextlib import AsyncExitStack, asynccontextmanager

from mcp_toolkit import MCPClient, MCPToolkit

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DigestClient:
    """摘要系统客户端，用于与摘要服务进行通信
    
    Args:
        config_path (Optional[str]): MCP配置文件路径
        single_digest_url (str): 单篇摘要服务URL
        merge_digest_url (str): 合并摘要服务URL
        manager_url (Optional[str]): 摘要管理服务URL
        timeout (Optional[float]): 连接超时时间（秒）
    """
    
    def __init__(
        self,
        config_path: Optional[str] = None,
        single_digest_url: str = "http://localhost:8091/mcp",
        merge_digest_url: str = "http://localhost:8092/mcp",
        manager_url: Optional[str] = None,
        timeout: Optional[float] = 120.0
    ):
        if config_path:
            self.toolkit = MCPToolkit(config_path=config_path)
            logger.info(f"Initialized DigestClient from config: {config_path}")
        else:
            servers = [
                MCPClient(single_digest_url, timeout=timeout),
                MCPClient(merge_digest_url, timeout=timeout)
            ]
            
            if manager_url:
                servers.append(MCPClient(manager_url, timeout=timeout))
                
            self.toolkit = MCPToolkit(servers=servers)
            logger.info(f"Initialized DigestClient with {len(servers)} servers")
            
        self._connected = False
        self._exit_stack = AsyncExitStack()

    async def connect(self):
        """连接到所有MCP服务器
        
        Returns:
            DigestClient: 客户端实例
        """
        if self._connected:
            logger.warning("DigestClient is already connected")
            return self
            
        try:
            await self.toolkit.connect()
            self._connected = True
            logger.info("Connected to all digest servers")
            return self
        except Exception as e:
            await self.disconnect()
            logger.error(f"Failed to connect to digest servers: {e}")
            raise e

    async def disconnect(self):
        """断开与所有MCP服务器的连接"""
        if not self._connected:
            return
            
        try:
            await self.toolkit.disconnect()
        finally:
            self._connected = False
            await self._exit_stack.aclose()
            logger.info("Disconnected from all digest servers")

    @asynccontextmanager
    async def connection(self) -> AsyncGenerator["DigestClient", None]:
        """异步上下文管理器，连接到所有服务
        
        Yields:
            DigestClient: 已连接的客户端
        """
        try:
            await self.connect()
            yield self
        finally:
            await self.disconnect()

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.disconnect()
        
    def is_connected(self) -> bool:
        """检查是否已连接
        
        Returns:
            bool: 连接状态
        """
        return self._connected

    async def generate_single_digest(
        self, 
        paper_info: Dict[str, Any], 
        outline: Any, 
        survey_title: str
    ) -> Dict[str, Any]:
        """为单篇论文生成摘要
        
        Args:
            paper_info: 包含论文信息的字典
            outline: 调研大纲
            survey_title: 调研标题
            
        Returns:
            Dict[str, Any]: 包含摘要内容的字典
        """
        if not self._connected:
            await self.connect()
            
        # 获取单篇摘要工具
        all_tools = self.toolkit.get_tools()
        single_digest_tool = next(
            (tool for tool in all_tools if tool.get_function_name() == "generate_single_digest"),
            None
        )
        
        if not single_digest_tool:
            raise RuntimeError("Single digest tool not found. Is the server connected?")
            
        # 调用工具
        try:
            result = await single_digest_tool.async_call(
                paper_info=paper_info,
                outline=outline,
                survey_title=survey_title
            )
            return result
        except Exception as e:
            logger.error(f"Error generating digest for {paper_info.get('bibkey', 'unknown')}: {e}")
            return {
                "success": False,
                "error": str(e),
                "bibkey": paper_info.get("bibkey", "unknown")
            }

    async def merge_digests(
        self, 
        digest_dicts: List[Dict[str, Any]], 
        outline: Any,
        survey_title: str
    ) -> Dict[str, Any]:
        """合并多个摘要
        
        Args:
            digest_dicts: 摘要字典列表
            outline: 调研大纲
            survey_title: 调研标题
            
        Returns:
            Dict[str, Any]: 合并后的摘要
        """
        if not self._connected:
            await self.connect()
            
        # 获取合并摘要工具
        all_tools = self.toolkit.get_tools()
        merge_digest_tool = next(
            (tool for tool in all_tools if tool.get_function_name() == "merge_digests"),
            None
        )
        
        if not merge_digest_tool:
            raise RuntimeError("Merge digest tool not found. Is the server connected?")
            
        # 调用工具
        try:
            result = await merge_digest_tool.async_call(
                digest_dicts=digest_dicts,
                outline=outline,
                survey_title=survey_title
            )
            return result
        except Exception as e:
            logger.error(f"Error merging digests: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def process_survey(
        self,
        survey_dict: Dict[str, Any],
        single_digest_url: str = "http://localhost:8091/mcp",
        merge_digest_url: str = "http://localhost:8092/mcp"
    ) -> Dict[str, Any]:
        """处理整个调研摘要
        
        Args:
            survey_dict: 调研字典
            single_digest_url: 单篇摘要服务URL
            merge_digest_url: 合并摘要服务URL
            
        Returns:
            Dict[str, Any]: 更新后的调研
        """
        if not self._connected:
            await self.connect()
            
        # 获取管理工具
        all_tools = self.toolkit.get_tools()
        process_tool = next(
            (tool for tool in all_tools if tool.get_function_name() == "process_survey_digests"),
            None
        )
        
        if not process_tool:
            raise RuntimeError("Survey process tool not found. Is the manager server connected?")
            
        # 调用工具
        try:
            result = await process_tool.async_call(
                survey_dict=survey_dict,
                single_digest_url=single_digest_url,
                merge_digest_url=merge_digest_url
            )
            return result
        except Exception as e:
            logger.error(f"Error processing survey: {e}")
            return {
                "success": False,
                "error": str(e)
            }


async def main():
    """示例用法"""
    # 简单的测试代码
    parser = argparse.ArgumentParser(description="测试摘要客户端")
    parser.add_argument("--single-url", type=str, default="http://localhost:8091/mcp",
                       help="单篇摘要服务URL")
    parser.add_argument("--merge-url", type=str, default="http://localhost:8092/mcp",
                       help="合并摘要服务URL")
    parser.add_argument("--manager-url", type=str, default="http://localhost:8093/mcp",
                       help="摘要管理服务URL")
    args = parser.parse_args()
    
    # 使用上下文管理器连接服务
    async with DigestClient(
        single_digest_url=args.single_url,
        merge_digest_url=args.merge_url,
        manager_url=args.manager_url
    ) as client:
        # 这里可以放测试代码
        print("Connected to digest servers")
        print("Available tools:", [tool.get_function_name() for tool in client.toolkit.get_tools()])


if __name__ == "__main__":
    import argparse
    asyncio.run(main())