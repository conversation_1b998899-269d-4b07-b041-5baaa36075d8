#!/usr/bin/env python3
"""
LLM搜索主机 (LLM Search Host) - 正确的MCP架构
替代原有的LLM_search模块，通过MCP协议调用搜索服务
保持与原有接口完全一致，实现无缝替换

正确的架构分层：
1. LLM Host (本文件) - 业务逻辑层，实现原有LLM_search接口
2. MCP Client - 纯粹的通信桥梁，负责与MCP Server通信  
3. MCP Server - 工具提供者，实现具体的搜索功能

职责分离：
- LLM Host: 接口适配、业务流程控制、数据格式转换
- MCP Client: 连接管理、消息传递、协议处理
- MCP Server: 工具实现、LLM调用、搜索执行
"""

import asyncio
import logging
import os
import traceback
from typing import List, Literal, Optional

from .mcp_client import MCPClientManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LLM_search:
    """
    LLM搜索主机类 - 业务逻辑层
    
    通过MCP客户端调用MCP服务器提供的搜索工具，
    但保持与原有LLM_search类完全相同的接口
    """
    
    def __init__(
        self,
        model: str = "gemini-2.0-flash-thinking-exp-01-21",
        infer_type: str = "OpenAI",
        engine: Literal["google", "baidu", "bing"] = "google",
        each_query_result: int = 10,
        filter_date: Optional[str] = None,
        max_workers: int = 10,
    ):
        """
        初始化LLM搜索主机
        
        Args:
            model: LLM模型名称
            infer_type: 推理类型 (OpenAI, Google, local)
            engine: 搜索引擎 (google, baidu, bing)
            each_query_result: 每个查询的结果数量
            filter_date: 日期过滤器
            max_workers: 最大并发工作数
        """
        self.model = model
        self.infer_type = infer_type
        self.engine = engine
        self.each_query_result = each_query_result
        self.filter_date = filter_date
        self.max_workers = max_workers
        
        # MCP服务器配置
        self.server_config = {
            "command": "python",
            "args": ["-m", "src.search.llm_search_mcp_server"],
            "env": {
                "PYTHONPATH": ".",
                "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", ""),
                "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY", ""),
                "SERP_API_KEY": os.getenv("SERP_API_KEY", ""),
                "BING_SEARCH_V7_SUBSCRIPTION_KEY": os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY", "")
            }
        }
        
        # 检查API密钥
        self._check_api_keys()
        
        logger.info(f"LLM Search Host initialized with model={model}, engine={engine}")
    
    def _check_api_keys(self):
        """检查必要的API密钥"""
        # 检查LLM API密钥
        llm_keys = ["OPENAI_API_KEY", "GOOGLE_API_KEY"]
        llm_available = any(os.getenv(key) for key in llm_keys)
        
        # 检查搜索API密钥
        search_keys = ["SERP_API_KEY", "BING_SEARCH_V7_SUBSCRIPTION_KEY"]
        search_available = any(os.getenv(key) for key in search_keys)
        
        if not llm_available:
            logger.warning("No LLM API key found. Query generation may fail.")
        
        if not search_available:
            raise ValueError(
                "No valid search engine key provided. Please set SERP_API_KEY or BING_SEARCH_V7_SUBSCRIPTION_KEY."
            )
    
    def get_queries(self, topic: str, description: str = "") -> List[str]:
        """
        获取优化的搜索查询列表
        
        Args:
            topic: 研究主题
            description: 可选的主题描述/上下文
            
        Returns:
            list: 优化的搜索查询列表
        """
        try:
            # 创建事件循环（如果不存在）
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 异步执行查询生成
            async def _async_get_queries():
                async with MCPClientManager(self.server_config) as client:
                    result = await client.call_tool_and_parse_json(
                        "generate_search_queries",
                        {
                            "topic": topic,
                            "description": description,
                            "model": self.model
                        }
                    )
                    return result["queries"]
            
            queries = loop.run_until_complete(_async_get_queries())
            logger.info(f"Final count {len(queries)}:\n{queries}")
            return queries
            
        except Exception as e:
            logger.error(f"Error generating queries: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def web_search(self, query: str):
        """
        对单个查询执行网络搜索
        
        Args:
            query: 搜索查询字符串
            
        Returns:
            dict: 搜索结果字典，格式与原有接口一致
        """
        try:
            # 创建事件循环（如果不存在）
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 异步执行单个查询搜索
            async def _async_web_search():
                async with MCPClientManager(self.server_config) as client:
                    result = await client.call_tool_and_parse_json(
                        "web_search",
                        {
                            "queries": [query],
                            "topic": query,  # 使用查询本身作为主题
                            "top_n": self.each_query_result,
                            "engine": self.engine
                        }
                    )
                    
                    # 转换为原有格式
                    urls = result["urls"]
                    web_snippets = {}
                    for idx, url in enumerate(urls):
                        web_snippets[idx] = {
                            "title": f"Result {idx + 1}",
                            "url": url,
                            "snippet": f"Search result for: {query}",
                        }
                    
                    return web_snippets
            
            return loop.run_until_complete(_async_web_search())
            
        except Exception as e:
            logger.error(f"Error in web search for query '{query}': {e}")
            raise
    
    def batch_web_search(self, queries: List[str], topic: str, top_n: int = 20) -> List[str]:
        """
        批量执行网络搜索并返回按相关性过滤的结果
        
        Args:
            queries: 搜索查询列表
            topic: 主要主题，用于相关性过滤
            top_n: 返回的最相关URL数量
            
        Returns:
            list: 过滤后的最相关URL列表
        """
        try:
            # 创建事件循环（如果不存在）
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 异步执行批量搜索
            async def _async_batch_search():
                async with MCPClientManager(self.server_config) as client:
                    result = await client.call_tool_and_parse_json(
                        "web_search",
                        {
                            "queries": queries,
                            "topic": topic,
                            "top_n": top_n,
                            "engine": self.engine
                        }
                    )
                    return result["urls"]
            
            urls = loop.run_until_complete(_async_batch_search())
            logger.info(f"Returning top {len(urls)} most relevant URLs.")
            return urls
            
        except Exception as e:
            logger.error(f"Error in batch web search: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def snippet_filter(self, topic: str, snippet: str) -> float:
        """
        计算主题和片段之间的相似度分数
        
        Args:
            topic: 搜索主题
            snippet: 要比较的文本片段
            
        Returns:
            float: 0到100之间的相似度分数
        """
        try:
            # 这里可以实现基于MCP的相似度计算
            # 目前返回一个默认分数，实际应用中可以通过MCP调用LLM进行评分
            logger.info(f"Calculating similarity for topic: {topic}")
            return 75.0  # 默认分数
            
        except Exception as e:
            logger.error(f"Error calculating similarity score: {e}")
            return 0.0

# 为了保持向后兼容性，提供一个工厂函数
def create_llm_search(
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI",
    engine: Literal["google", "baidu", "bing"] = "google",
    each_query_result: int = 10,
    filter_date: Optional[str] = None,
    max_workers: int = 10,
) -> LLM_search:
    """
    创建LLM搜索实例的工厂函数
    
    Args:
        model: LLM模型名称
        infer_type: 推理类型
        engine: 搜索引擎
        each_query_result: 每个查询的结果数量
        filter_date: 日期过滤器
        max_workers: 最大并发工作数
        
    Returns:
        LLM_search: LLM搜索实例
    """
    return LLM_search(
        model=model,
        infer_type=infer_type,
        engine=engine,
        each_query_result=each_query_result,
        filter_date=filter_date,
        max_workers=max_workers
    )
