import logging
import random
import re
from typing import List, Dict, Any, Optional, Set, FrozenSet

from tenacity import retry, stop_after_attempt, after_log, retry_if_exception_type
from camel.toolkits import BaseToolkit, FunctionTool

from request import RequestWrapper
from src.base_method.data import Dataset
from src.data_structure import Skeleton, Survey, Digest, Feedback
from src.exceptions import (
    BibkeyNotFoundError,
    StructureNotCorrespondingError,
    MdNotFoundError,
    GroupEmptyError,
)
from src.utils.process_str import str2list, list2str
from src.prompts import (
    CONCAT_OUTLINE_PROMPT,
    INIT_OUTLINE_PROMPT,
    DIGEST_BASE_PROMPT
)
from src.hidden.convolution_block.neurons import FeedbackClusterNeuron

logger = logging.getLogger(__name__)


class SkeletonInitToolkit(BaseToolkit):
    """初始化骨架的工具包"""

    def __init__(
        self,
        model: str = "gpt-4",
        infer_type: str = "OpenAI",
        timeout: Optional[float] = None
    ):
        """初始化骨架初始化工具包
        
        Args:
            model: 使用的LLM模型
            infer_type: 推理类型
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.config = {"model": model, "infer_type": infer_type}
        self.prompt_single = INIT_OUTLINE_PROMPT
        self.prompt_concat = CONCAT_OUTLINE_PROMPT
        self.request_pool = RequestWrapper(model=model, infer_type=infer_type)
        logger.info(f"SkeletonInitToolkit initialized with model: {model}")

    async def generate_initial_skeleton(
        self,
        survey_dict: Dict[str, Any],
        batch_size: int = 3
    ) -> Dict[str, Any]:
        """为调研生成初始骨架结构
        
        Args:
            survey_dict: 调研字典
            batch_size: 每批次处理的摘要数量
            
        Returns:
            Dict[str, Any]: 处理后的调研字典
        """
        try:
            # 恢复Survey对象
            survey = Survey.from_dict(survey_dict)
            title = survey.title
            
            logger.info(f"Generating initial skeleton for survey: {title} with {len(survey.digests)} digest collections")
            
            # 分批处理摘要
            digest_batches = await self._split_digests(survey, batch_size)
            
            # 为每批摘要生成单独的骨架
            outlines = []
            for batch_title, batch_digests in digest_batches:
                outline = await self._generate_single_outline(batch_title, batch_digests)
                outlines.append(outline)
            
            logger.info(f"Generated {len(outlines)} individual outlines")
            
            # 合并骨架
            if len(outlines) > 1:
                survey = await self._concat_outlines(survey, outlines)
            elif len(outlines) == 1:
                final_outline = outlines[0].raw_skeleton
                survey.skeleton.parse_raw_skeleton(survey.title, final_outline)
            
            survey.skeleton_batch_size = batch_size
            logger.info(f"Skeleton initialization finished for survey: {title}")
            
            return survey.to_dict()
            
        except Exception as e:
            logger.error(f"Failed to generate initial skeleton: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "survey": survey_dict
            }

    async def _split_digests(self, survey, step):
        """将摘要分组为批次"""
        items = list(survey.digests.items())
        random.shuffle(items)
        title = survey.title
        batches = []
        
        for i in range(0, len(items), step):
            batches.append((title, dict(items[i : i + step])))
            
        return batches

    @retry(
        stop=stop_after_attempt(5),
        after=after_log(logger, logging.WARNING),
        retry=retry_if_exception_type((BibkeyNotFoundError, StructureNotCorrespondingError, MdNotFoundError, ValueError)),
    )
    async def _generate_single_outline(self, title, digests: Dict[str, Digest]):
        """为一批摘要生成单独的骨架"""
        def format_abstract(digest_list):
            random.shuffle(digest_list)
            result = "\n---------------------\n".join([digest.abstract for digest in digest_list])
            return result

        def merge_frozensets(frozenset_list):
            result_set = set()
            for fs in frozenset_list:
                result_set.update(fs)
            return result_set

        digest_list = list(digests.values())
        format_abstracts = format_abstract(digest_list)
        bibkeys = [b for digest in digest_list for b in digest.bibkeys]
        bibkeys = list2str(bibkeys)
        prompt = self.prompt_single.format(
            title=title, abstracts=format_abstracts, bibkeys=bibkeys
        )
        new_raw_outline = self.request_pool.completion(prompt)
        new_outline = Skeleton(merge_frozensets(digests.keys()))
        new_outline.parse_raw_skeleton(title, new_raw_outline)
        logger.info(f"Single outline finished: Survey {title}.")
        return new_outline

    @retry(
        stop=stop_after_attempt(5),
        after=after_log(logger, logging.WARNING),
        retry=retry_if_exception_type((BibkeyNotFoundError, StructureNotCorrespondingError, MdNotFoundError, ValueError)),
    )
    async def _concat_outlines(self, survey, head_results):
        """合并多个骨架"""
        logger.info(f"Concat outline start: Survey {survey.title}.")
        bibkeys = list2str(survey.papers.keys())
        concat_results = await self._format_outlines_for_concat(head_results)
        prompt = self.prompt_concat.format(
            title=survey.title, outlines=concat_results, bibkeys=bibkeys
        )
        new_outline = self.request_pool.completion(prompt)
        survey.skeleton.parse_raw_skeleton(survey.title, new_outline)
        logger.info(f"Concat outline finished: Survey {survey.title}.")
        return survey

    async def _format_outlines_for_concat(self, outlines):
        """格式化骨架用于合并"""
        result = []
        random.shuffle(outlines)
        for i, outline in enumerate(outlines):
            result.append(f"```markdown\n{outline.all_skeleton(construction=True, analysis=True, with_index=False)}\n```")
        result = "\n--------------------------\n".join(result)
        return result.strip()

    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.generate_initial_skeleton)
        ]


class SkeletonRefineToolkit(BaseToolkit):
    """骨架优化工具包"""

    def __init__(
        self,
        timeout: Optional[float] = None
    ):
        """初始化骨架优化工具包
        
        Args:
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        logger.info("SkeletonRefineToolkit initialized")

    async def refine_survey_skeleton(
        self,
        survey_dict: Dict[str, Any],
        convolution_layer_url: str,
        self_refine_url: str,
        feedback_cluster_url: str,
        convolution_layer: int = 3,
        receptive_field: int = 3,
        result_num: int = 5,
        top_k: int = 5,
        self_refine_count: int = 3,
        self_refine_best_of: int = 5
    ) -> Dict[str, Any]:
        """优化调研骨架
        
        Args:
            survey_dict: 调研字典
            convolution_layer_url: 卷积层服务URL
            self_refine_url: 自我优化服务URL
            feedback_cluster_url: 反馈聚类服务URL
            convolution_layer: 卷积层数量
            receptive_field: 感受野大小
            result_num: 结果数量
            top_k: 保留顶部结果数量
            self_refine_count: 自优化迭代次数
            self_refine_best_of: 自优化保留最佳结果数量
            
        Returns:
            Dict[str, Any]: 处理后的调研字典
        """
        # 导入客户端工具
        from mcp_toolkit import MCPClient
        
        # 恢复Survey对象
        survey = Survey.from_dict(survey_dict)
        
        logger.info(
            f"Starting skeleton refinement for survey: {survey.title}, block cycle count: {survey.block_cycle_count}"
        )
        
        # 创建客户端连接
        convolution_client = MCPClient(convolution_layer_url)
        self_refine_client = MCPClient(self_refine_url)
        feedback_cluster_client = MCPClient(feedback_cluster_url)
        
        try:
            # 连接服务
            await convolution_client.connect()
            await self_refine_client.connect()
            await feedback_cluster_client.connect()
            
            # 获取工具
            convolution_tool = next(
                tool for tool in convolution_client.get_tools() 
                if tool.get_function_name() == "process_convolution_layer"
            )
            
            self_refine_tool = next(
                tool for tool in self_refine_client.get_tools() 
                if tool.get_function_name() == "self_refine_skeleton"
            )
            
            feedback_cluster_tool = next(
                tool for tool in feedback_cluster_client.get_tools() 
                if tool.get_function_name() == "cluster_feedback"
            )
            
            # 获取骨架和摘要信息
            skeleton = survey.skeleton
            title = survey.title
            digests = list(survey.digests.values())
            
            # 1. 反馈聚类
            digest_data = []
            for digest_group in digests:
                digest_data.append({
                    "title": title,
                    "digest_group": digest_group.to_dict(),
                    "skeleton": skeleton.to_dict(),
                    "prompt": DIGEST_BASE_PROMPT
                })
                
            feedback_result = await feedback_cluster_tool.async_call(
                digest_data=digest_data
            )
            
            if not feedback_result.get("success", False):
                logger.error(f"Feedback clustering failed: {feedback_result.get('error', 'Unknown error')}")
                return survey_dict
                
            utilise_results = feedback_result.get("feedback_results", [])
            logger.info(
                f"Feedback Cluster finished: Survey: {title}, Digest Group Count: {len(digests)}"
            )
            
            # 2. 卷积层处理
            convolution_result = await convolution_tool.async_call(
                survey_dict=survey_dict,
                utilise_results=utilise_results,
                origin_outline=skeleton.all_skeleton(construction=True, analysis=True, with_index=True),
                convolution_kernel_url=convolution_layer_url,
                modify_outline_url=convolution_layer_url,
                eval_outline_url=convolution_layer_url,
                convolution_layer=convolution_layer,
                receptive_field=receptive_field,
                result_num=result_num,
                top_k=top_k
            )
            
            if not isinstance(convolution_result, dict):
                logger.error(f"Convolution layer processing failed: Invalid result type")
                return survey_dict
                
            survey = Survey.from_dict(convolution_result)
            logger.info(f"Convolution layer module finished: Survey {survey.title}")
            
            # 3. 自我优化
            self_refine_result = await self_refine_tool.async_call(
                survey_dict=survey.to_dict(),
                self_refine_count=self_refine_count,
                self_refine_best_of=self_refine_best_of
            )
            
            if not isinstance(self_refine_result, dict):
                logger.error(f"Self-refine process failed: Invalid result type")
                return survey.to_dict()
                
            survey = Survey.from_dict(self_refine_result)
            logger.info(f"Self-refine module finished: Survey {survey.title}")
            
            # 更新循环计数
            survey.block_cycle_count += 1
            logger.info(
                f"Skeleton-refine module finished: Survey {survey.title} block cycle count: {survey.block_cycle_count}"
            )
            
            return survey.to_dict()
            
        finally:
            # 关闭客户端连接
            await convolution_client.disconnect()
            await self_refine_client.disconnect()
            await feedback_cluster_client.disconnect()

    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.refine_survey_skeleton)
        ]


class FeedbackClusterToolkit(BaseToolkit):
    """反馈聚类工具包"""
    
    def __init__(
        self, 
        config: Dict[str, Any] = None,
        timeout: Optional[float] = None
    ):
        """初始化反馈聚类工具包
        
        Args:
            config: 配置参数
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.config = config or {}
        self.feedback_cluster_neuron = FeedbackClusterNeuron(self.config)
        
        logger.info("FeedbackClusterToolkit initialized")
    
    async def cluster_feedback(
        self,
        digest_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """聚类摘要反馈
        
        Args:
            digest_data: 摘要数据列表，每项包含title、digest_group、skeleton和prompt
            
        Returns:
            Dict[str, Any]: 包含聚类反馈结果的字典
        """
        try:
            # 构建数据集
            dataset = Dataset([
                (
                    item["title"],
                    [Digest.from_dict(item["digest_group"])],
                    Skeleton.from_dict(item["skeleton"]),
                    item["prompt"]
                )
                for item in digest_data
            ])
            
            # 调用反馈聚类神经元
            title = digest_data[0]["title"] if digest_data else "Unknown"
            logger.info(
                f"Feedback Cluster start: Count {len(dataset)} Survey: {title}, Digest Group Count: {len(dataset)}"
            )
            
            utilise_results = self.feedback_cluster_neuron(dataset)
            
            # 转换结果为字典形式
            feedback_results = [feedback.to_dict() for feedback in utilise_results]
            
            logger.info(
                f"Feedback Cluster finished: Survey: {title}, Feedback Count: {len(feedback_results)}"
            )
            
            return {
                "success": True,
                "feedback_results": feedback_results
            }
            
        except Exception as e:
            logger.error(f"Failed to cluster feedback: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "feedback_results": []
            }
    
    async def merge_results_from_one_description(self, utilise_results):
        """将来自同一描述的结果合并"""
        result_dict = {}
        for result, description in utilise_results:
            if description not in result_dict:
                result_dict[description] = []
            result_dict[description].append(result)
        
        for description, results in result_dict.items():
            random.shuffle(results)
            result_dict[description] = "/n".join(results)
        
        result_list = [
            (result, description) for description, result in result_dict.items()
        ]
        random.shuffle(result_list)
        return result_list
    
    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.cluster_feedback)
        ]


class SelfRefineToolkit(BaseToolkit):
    """大纲自优化工具包"""
    
    def __init__(
        self, 
        config: Dict[str, Any] = None,
        timeout: Optional[float] = None
    ):
        """初始化大纲自优化工具包
        
        Args:
            config: 配置参数
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.config = config or {}
        
        logger.info("SelfRefineToolkit initialized")
    
    async def self_refine_skeleton(
        self,
        survey_dict: Dict[str, Any],
        self_refine_count: int = 3,
        self_refine_best_of: int = 5
    ) -> Dict[str, Any]:
        """自优化大纲
        
        Args:
            survey_dict: 调研字典
            self_refine_count: 自优化迭代次数
            self_refine_best_of: 自优化保留最佳结果数量
            
        Returns:
            Dict[str, Any]: 处理后的调研字典
        """
        try:
            # 导入具体模块
            from src.hidden.convolution_block.refine_module import SelfRefineModule
            
            # 恢复Survey对象
            survey = Survey.from_dict(survey_dict)
            
            # 创建自优化模块
            self_refine_module = SelfRefineModule(
                self.config, self_refine_count, self_refine_best_of
            )
            
            # 执行自优化
            refined_survey = self_refine_module(survey)
            
            logger.info(f"Self-refine completed for survey: {survey.title}")
            
            return refined_survey.to_dict()
            
        except Exception as e:
            logger.error(f"Failed to self refine skeleton: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "survey": survey_dict
            }
    
    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.self_refine_skeleton)
        ]