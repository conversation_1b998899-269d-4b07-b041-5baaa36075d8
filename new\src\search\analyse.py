"""
文献分析和查询接口模块
提供任务分析、文献检索和数据保存的核心接口
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class AnalyseInterface:
    """
    文献分析接口类
    负责任务分析、文献检索和结果保存
    """
    
    def __init__(self, base_dir: str = "new/test"):
        """
        初始化分析接口
        
        Args:
            base_dir: 文献保存的基础目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"AnalyseInterface initialized with base_dir: {self.base_dir}")
    
    def analyse(self, task: str, description: Optional[str] = None, 
                top_n: int = 10, **kwargs) -> str:
        """
        分析任务并查询相关文献
        
        Args:
            task: 用户输入的研究任务/主题
            description: 任务的详细描述
            top_n: 需要检索的文献数量
            **kwargs: 其他可选参数
            
        Returns:
            str: 保存文献的目录路径
        """
        logger.info(f"Starting analysis for task: {task}")
        
        # 创建以任务名命名的子目录
        task_dir = self._create_task_directory(task)
        
        # 执行文献检索
        papers = self._search_literature(task, description, top_n, **kwargs)
        
        # 保存检索结果
        self._save_papers_to_directory(papers, task_dir)
        
        logger.info(f"Analysis completed. Papers saved to: {task_dir}")
        return str(task_dir)
    
    def _create_task_directory(self, task: str) -> Path:
        """
        创建任务目录
        
        Args:
            task: 任务名称
            
        Returns:
            Path: 创建的目录路径
        """
        # 清理任务名称，确保可以作为目录名
        safe_task_name = "".join(c for c in task if c.isalnum() or c in (' ', '-', '_')).strip()
        safe_task_name = safe_task_name.replace(' ', '_')
        
        task_dir = self.base_dir / safe_task_name
        task_dir.mkdir(parents=True, exist_ok=True)
        
        return task_dir
    
    def _search_literature(self, task: str, description: Optional[str], 
                          top_n: int, **kwargs) -> List[Dict[str, Any]]:
        """
        执行文献检索（接口定义）
        
        Args:
            task: 研究任务
            description: 任务描述
            top_n: 检索数量
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 检索到的文献列表
        """
        logger.info(f"Searching literature for task: {task}")
        
        # 这里是接口定义，实际实现需要调用具体的搜索引擎
        # 模拟返回文献数据结构
        papers = []
        
        for i in range(min(top_n, 10)):  # 模拟检索结果
            paper = {
                "title": f"Research Paper {i+1}: {task}",
                "abstract": f"This paper discusses {task} and related methodologies. "
                           f"Abstract content for paper {i+1}...",
                "authors": [f"Author{i+1}A", f"Author{i+1}B"],
                "year": 2024 - i % 5,
                "venue": f"Conference/Journal {i+1}",
                "url": f"https://example.com/paper_{i+1}",
                "txt": f"Full text content of paper {i+1} about {task}. "
                       f"This includes detailed methodology, experiments, and conclusions...",
                "keywords": [task.lower(), "research", "methodology"],
                "doi": f"10.1000/paper{i+1}",
                "citations": 50 - i * 5
            }
            papers.append(paper)
        
        logger.info(f"Found {len(papers)} papers for task: {task}")
        return papers
    
    def _save_papers_to_directory(self, papers: List[Dict[str, Any]], 
                                 task_dir: Path) -> None:
        """
        将文献保存到指定目录
        
        Args:
            papers: 文献列表
            task_dir: 目标目录
        """
        try:
            # 保存每篇文献为单独的JSON文件
            for i, paper in enumerate(papers):
                paper_filename = f"paper_{i+1:03d}.json"
                paper_path = task_dir / paper_filename
                
                with open(paper_path, 'w', encoding='utf-8') as f:
                    json.dump(paper, f, ensure_ascii=False, indent=2)
            
            # 创建汇总文件
            summary_data = {
                "task": task_dir.name,
                "total_papers": len(papers),
                "paper_files": [f"paper_{i+1:03d}.json" for i in range(len(papers))],
                "created_at": str(Path().cwd())
            }
            
            summary_path = task_dir / "summary.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Saved {len(papers)} papers to {task_dir}")
            
        except Exception as e:
            logger.error(f"Error saving papers to directory: {e}")
            raise


def analyse(task: str, description: Optional[str] = None, 
           top_n: int = 10, **kwargs) -> str:
    """
    便捷函数：分析任务并查询文献
    
    Args:
        task: 用户输入的研究任务
        description: 任务描述
        top_n: 检索文献数量
        **kwargs: 其他参数
        
    Returns:
        str: 保存文献的目录路径
    """
    analyser = AnalyseInterface()
    return analyser.analyse(task, description, top_n, **kwargs)


if __name__ == "__main__":
    # 测试代码
    test_task = "machine learning optimization"
    result_dir = analyse(test_task, "Research on ML optimization techniques", top_n=5)
    print(f"Analysis completed. Results saved to: {result_dir}")
