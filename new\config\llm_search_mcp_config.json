{"servers": {"llm_search_server": {"command": "python", "args": ["-m", "src.search.llm_search_mcp_server"], "env": {"PYTHONPATH": ".", "OPENAI_API_KEY": "${OPENAI_API_KEY}", "GOOGLE_API_KEY": "${GOOGLE_API_KEY}", "SERP_API_KEY": "${SERP_API_KEY}", "BING_SEARCH_V7_SUBSCRIPTION_KEY": "${BING_SEARCH_V7_SUBSCRIPTION_KEY}"}}}, "mcpServers": {"llm_search_server": {"command": "python", "args": ["-m", "src.search.llm_search_mcp_server"], "env": {"PYTHONPATH": ".", "OPENAI_API_KEY": "${OPENAI_API_KEY}", "GOOGLE_API_KEY": "${GOOGLE_API_KEY}", "SERP_API_KEY": "${SERP_API_KEY}", "BING_SEARCH_V7_SUBSCRIPTION_KEY": "${BING_SEARCH_V7_SUBSCRIPTION_KEY}"}}}, "tools": {"generate_search_queries": {"description": "Generate optimized search queries for a given topic using LLM", "parameters": {"topic": {"type": "string", "description": "The research topic to generate queries for"}, "description": {"type": "string", "description": "Optional description or context for the topic", "required": false}, "model": {"type": "string", "description": "LLM model to use for query generation", "default": "gemini-2.0-flash-thinking-exp-01-21"}}}, "web_search": {"description": "Perform web search using generated queries", "parameters": {"queries": {"type": "array", "items": {"type": "string"}, "description": "List of search queries to execute"}, "topic": {"type": "string", "description": "Main topic for relevance filtering"}, "top_n": {"type": "integer", "description": "Number of most relevant URLs to return", "default": 20}, "engine": {"type": "string", "description": "Search engine to use (google, bing, baidu)", "default": "google"}}}, "analyze_search_results": {"description": "Analyze and filter search results for relevance", "parameters": {"urls": {"type": "array", "items": {"type": "string"}, "description": "List of URLs to analyze"}, "topic": {"type": "string", "description": "Topic to analyze relevance against"}, "max_results": {"type": "integer", "description": "Maximum number of results to return", "default": 10}}}, "full_search_pipeline": {"description": "Execute complete search pipeline: query generation -> web search -> analysis", "parameters": {"topic": {"type": "string", "description": "Research topic"}, "description": {"type": "string", "description": "Optional topic description", "required": false}, "top_n": {"type": "integer", "description": "Number of final results to return", "default": 10}, "model": {"type": "string", "description": "LLM model for query generation", "default": "gemini-2.0-flash-thinking-exp-01-21"}, "engine": {"type": "string", "description": "Search engine to use", "default": "google"}}}}}