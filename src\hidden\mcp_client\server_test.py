#!/usr/bin/env python3
"""
Intelligent Pipeline MCP Client
智能流水线 MCP 客户端，协调各个处理器服务器
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from contextlib import AsyncExitStack

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from src.data_structure import Survey
import traceback

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentPipelineClient:
    """智能流水线客户端"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化客户端
        
        Args:
            config_path: MCP 配置文件路径
        """
        self.config_path = config_path or "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2/config/mcp_config.json"
        self.orchestrator_session = None
        self.digest_session = None
        self.skeleton_session = None
        self.group_session = None
        self.encode_session = None
        # 新增的MCP服务器会话
        self.convolution_session = None
        self.basic_modules_session = None
        self.neurons_session = None
        self._exit_stack = AsyncExitStack()
        
    async def connect(self):
        """连接到所有 MCP 服务器"""
        try:
            # 创建服务器参数
            orchestrator_params = StdioServerParameters(
                command="uv",
                args=["run", "python", "-m", "src.hidden.mcp_server.pipeline_orchestrator_server"],
                env={"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}
            )
            
            digest_params = StdioServerParameters(
                command="uv",
                args=["run", "python", "-m", "src.hidden.mcp_server.digest_processor_server"],
                env={"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}
            )
            
            skeleton_params = StdioServerParameters(
                command="uv",
                args=["run", "python", "-m", "src.hidden.mcp_server.skeleton_processor_server"],
                env={"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}
            )
            
            group_params = StdioServerParameters(
                command="uv",
                # args=["run", "python", "-m", "src.hidden.mcp_server.group_processor_server"],
                args=["run", "python", "-m", "src.hidden.mcp_server.group_module_server"],
                env={"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}
            )
            
            encode_params = StdioServerParameters(
                command="uv",
                args=["run", "python", "-m", "src.encode.encode_processor_server"],
                env={"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}
            )
            
            # 新增的MCP服务器参数
            convolution_params = StdioServerParameters(
                command="uv",
                args=["run", "python", "-m", "src.hidden.mcp_server.convolution_processor_server"],
                env={"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}
            )
            
            basic_modules_params = StdioServerParameters(
                command="uv",
                args=["run", "python", "-m", "src.hidden.mcp_server.basic_modules_processor_server"],
                env={"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}
            )
            
            neurons_params = StdioServerParameters(
                command="uv",
                args=["run", "python", "-m", "src.hidden.mcp_server.neurons_processor_server"],
                env={"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}
            )
            
            # 连接到各个服务器
            self.orchestrator_session = await self._exit_stack.enter_async_context(
                stdio_client(orchestrator_params)
            )
            
            self.digest_session = await self._exit_stack.enter_async_context(
                stdio_client(digest_params)
            )
            
            self.skeleton_session = await self._exit_stack.enter_async_context(
                stdio_client(skeleton_params)
            )
            
            self.group_session = await self._exit_stack.enter_async_context(
                stdio_client(group_params)
            )
            
            self.encode_session = await self._exit_stack.enter_async_context(
                stdio_client(encode_params)
            )
            
            # 连接到新增的MCP服务器
            self.convolution_session = await self._exit_stack.enter_async_context(
                stdio_client(convolution_params)
            )
            
            self.basic_modules_session = await self._exit_stack.enter_async_context(
                stdio_client(basic_modules_params)
            )
            
            self.neurons_session = await self._exit_stack.enter_async_context(
                stdio_client(neurons_params)
            )
            
            logger.info("Successfully connected to all MCP servers")
            
        except Exception as e:
            logger.error(f"Failed to connect to MCP servers: {e}")
            await self.disconnect()
            raise
    
    async def disconnect(self):
        """断开所有连接"""
        await self._exit_stack.aclose()
        logger.info("Disconnected from all MCP servers")
    
    async def process_survey_intelligently(self, survey: Dict, config: Dict[str, Any]) -> Survey:
        """智能处理调研数据
        
        Args:
            survey: 输入的调研对象
            config: 处理配置
            
        Returns:
            Survey: 处理后的调研对象
        """
        if not self.orchestrator_session:
            raise RuntimeError("Client not connected. Call connect() first.")
        
        logger.info(f"Starting intelligent processing for survey: {survey.get('title', 'Untitled Survey')}")
        
        # 准备当前状态
        current_state = {
            "cycle": 0,
            "timestamp": asyncio.get_event_loop().time(),
            "max_cycles": config.get("max_cycles", 3)
        }
        
        # 开始处理循环
        # 两套方案：方案一是严格遵循原先的pipeline，方案二是加入orchetrator，对每一步进行规划，根据当前步的生成结果判断next action
        for cycle in range(current_state["max_cycles"]):
            current_state["cycle"] = cycle
            logger.info(f"Processing cycle {cycle + 1}/{current_state['max_cycles']}")
            
            # 调用编排器分析下一步操作

            survey_data = {
                "title": survey.get("title", "Untitled Survey"),
                "content_items": survey,
                "content_count": len(survey.get("papers")) # 此处content应为List[Dict[str, Any]]类型，但是实际到_call_orchestrator时应该是单个dict，这个字段没什么用且有错误，建议去除
            }
            # orchestration_result = await self._call_orchestrator(
            #     # survey_data,
            #     survey,
            #     current_state,
            #     config
            # )

            orchestration_result = await self._call_orchestrator(survey, current_state)
            
            next_action = orchestration_result.get("next_action", "complete")
            logger.info(f"Orchestrator recommends action: {next_action}")
            print(f"Orchestration result: {orchestration_result}")
            print(f"Next action: {next_action}")
            
            # 根据编排器建议执行相应操作
            # 这一部分整体的循环逻辑有问题
            survey = await self._execute_grouping(survey, orchestration_result, config)
            survey = await self._execute_skeleton_processing(survey, orchestration_result)
            survey = await self._execute_digest_processing(survey, orchestration_result)
            survey = await self._execute_refinement(survey, orchestration_result)
            # if next_action == "group":
            #     survey = await self._execute_grouping(survey, orchestration_result)
            # elif next_action == "skeleton":
            #     survey = await self._execute_skeleton_processing(survey, orchestration_result)
            # elif next_action == "digest":
            #     survey = await self._execute_digest_processing(survey, orchestration_result)
            # elif next_action == "refine":
            #     survey = await self._execute_refinement(survey, orchestration_result)
            # elif next_action == "complete":
            #     logger.info("Orchestrator indicates processing is complete")
            #     break
            # else:
            #     logger.warning(f"Unknown action: {next_action}, continuing...")
            
            # 检查是否需要继续迭代
            # orchestration_result需要更新
            # orchestrator的逻辑到底是什么？
            if not orchestration_result.get("parameters", {}).get("iteration_needed", True):
                logger.info("No further iteration needed")
                break
        
        logger.info(f"Completed intelligent processing for survey: {survey.title}")
        return survey

    async def test_servers(self, json_data: Dict, config_file='/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2/config/model_config.json') -> Dict[str, Any]:
        """
        测试各个服务器的功能
        Args:
            json_data: dict
        """
        if not self.orchestrator_session:
            await self.connect()

        current_state = {
            "cycle": 0,
            "timestamp": asyncio.get_event_loop().time(),
            "max_cycles": 3
        }

        survey = Survey(json_data) if isinstance(json_data, dict) else json_data

        # 读取原先算法中的配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        try:
            # 测试编排器
            # orchestration_result = await self.orchestrate_pipeline(json_data)
            orchestration_result = await self._call_orchestrator(survey, current_state)
            logger.info(f"Orchestration result: {orchestration_result}")

            next_action = orchestration_result.get("next_action", "complete")
            logger.info(f"Orchestrator recommends action: {next_action}")
            
            # 测试分组处理器，输入survey类
            group_result = await self._execute_grouping(survey, config)
            logger.info(f"Grouping result: {group_result}")
            
            breakpoint()
            # 测试骨架处理器
            skeleton_result = await self.generate_skeleton(group_result)
            logger.info(f"Skeleton result: {skeleton_result}")
            
            # 测试摘要处理器
            digest_result = await self.process_digest(json_data, skeleton_result)
            logger.info(f"Digest result: {digest_result}")
        
        except Exception as e:
            logger.error(f"Error during server tests: {e}\n\nTraceback:\n{traceback.format_exc()}")
            return {"error": str(e)}
        


    async def _call_orchestrator(self, survey: Survey, current_state: Dict[str, Any]) -> Dict[str, Any]:

        read, write = self.orchestrator_session
        breakpoint()
        async with ClientSession(read, write) as session:
            await session.initialize()

            result = await session.call_tool(
                "orchestrate_pipeline",
                {
                    "survey": survey,
                    "current_state": current_state,
                }
            )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"next_action": "complete", "confidence": 0.5}
    
    async def _execute_grouping(self, survey: Survey, config: Dict) -> Survey:
        """执行数据分组"""
        logger.info("Executing intelligent grouping...")
        try:

            read, write = self.group_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "group_papers",
                    {
                        # "dataset": dataset,
                        # "dataset": survey['papers'], 
                        "papers": list(survey.papers.values()),
                        "config": config,
                    }
                )
            
            if result.content:
                grouping_result = json.loads(result.content[0].text)
                # 这里可以根据分组结果更新 survey 对象
                logger.info(f"Grouping completed with {grouping_result.get('grouping_metrics', {}).get('total_groups', 0)} groups")
            
        except Exception as e:
            error_msg = traceback.format_exc()
            logger.error(f"Error in grouping: {e}\n\ntraceback: \n{error_msg}")
        
        return survey
    
    async def _execute_skeleton_processing(self, survey: Dict, orchestration_result: Dict[str, Any]) -> Survey:
        """执行骨架处理"""
        logger.info("Executing intelligent skeleton processing...")
        
        try:
            # if hasattr(survey, 'skeleton') and survey.skeleton:
            #     # 优化现有骨架
            #     result = await self.skeleton_session.call_tool(
            #         "refine_skeleton_intelligently",
            #         {
            #             "current_skeleton": survey.skeleton.to_dict() if hasattr(survey.skeleton, 'to_dict') else survey.skeleton,
            #             "new_information": "基于当前处理状态的新信息",
            #             "optimization_goals": orchestration_result.get("parameters", {}).get("skeleton_modifications", [])
            #         }
            #     )

            read, write = self.skeleton_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                if isinstance(survey, Survey) and hasattr(survey, 'skeleton') and survey.skeleton:
                # if hasattr(survey, 'skeleton') and survey.skeleton:
                    # 优化现有骨架
                    result = await session.call_tool(
                        "refine_skeleton_intelligently",
                        {
                            "current_skeleton": survey.skeleton.to_dict() if hasattr(survey.skeleton, 'to_dict') else survey.skeleton,
                            "new_information": "基于当前处理状态的新信息",
                            "optimization_goals": orchestration_result.get("parameters", {}).get("skeleton_modifications", [])
                        }
                    )
                else:
                    # 生成新骨架
                    result = await session.call_tool(
                        "generate_intelligent_skeleton",
                        {
                            # "topic": survey.title,
                            "topic": survey.get("title", "untitled survey"),
                            "description": getattr(survey, 'description', ''),
                            "expected_depth": "medium"
                        }
                    )
            
            if result.content:
                skeleton_result = json.loads(result.content[0].text)
                print(f'----------skeleton--------\n{skeleton_result}')
                # 更新survey对象
                if isinstance(survey, Survey):
                    survey.skeleton.parse_raw_skeleton(survey.get("title", "Untitled Survey"), skeleton_result)
                # 这里可以根据骨架结果更新 survey 对象
                logger.info("Skeleton processing completed")
            
        except Exception as e:
            logger.error(f"Error in skeleton processing: {e}\n\n traceback:\n{traceback.format_exc()}")
        
        # return survey
        return skeleton_result
    
    async def _execute_digest_processing(self, survey: Survey, orchestration_result: Dict[str, Any]) -> Survey:
        """执行摘要处理"""
        logger.info("Executing intelligent digest processing...")
        
        try:
            if hasattr(survey, 'digests') and survey.digests:

                for digest_key, digest in survey.digests.items():
                    read, write = self.digest_session
                    async with ClientSession(read, write) as session:
                        await session.initialize()

                        result = await session.call_tool(
                            "generate_intelligent_digest",
                            {
                                "content": str(digest),
                                "outline": survey.skeleton.to_dict() if hasattr(survey, 'skeleton') and survey.skeleton else {},
                                "topic": survey.title,
                                "digest_type": "summary"
                            }
                        )

                    if result.content:
                        print(f'----------digest--------\n{result.content[0].text}')
                        digest_result = json.loads(result.content[0].text)
                        # 这里可以根据摘要结果更新 survey 对象
                        logger.info(f"Digest processing completed for {digest_key}")
            
        except Exception as e:
            logger.error(f"Error in digest processing: {e}")
        
        # return survey
        return digest_result
    
    async def _execute_refinement(self, survey: Survey, orchestration_result: Dict[str, Any]) -> Survey:
        """执行精化处理"""
        logger.info("Executing intelligent refinement...")
        
        try:
            # 验证当前处理质量
            if hasattr(survey, 'digests') and survey.digests:
                for digest_key, digest in survey.digests.items():
                    result = await self.digest_session.call_tool(
                        "assess_digest_quality",
                        {
                            "digest": {"content": str(digest)},
                            "quality_criteria": ["准确性", "完整性", "连贯性"]
                        }
                    )
                    
                    if result.content:
                        quality_result = json.loads(result.content[0].text)
                        logger.info(f"Quality assessment for {digest_key}: {quality_result.get('overall_score', 'N/A')}")
            
        except Exception as e:
            logger.error(f"Error in refinement: {e}")
        
        return survey

    # 添加测试支持的便捷方法
    # async def orchestrate_pipeline(self, content: List[Dict[str, Any]]) -> Dict[str, Any]:
    async def orchestrate_pipeline(self, content: Dict) -> Dict[str, Any]:
        """编排流水线处理策略"""
        if not self.orchestrator_session:
            await self.connect()
        
        current_state = {
            "cycle": 0,
            "timestamp": asyncio.get_event_loop().time(),
            "max_cycles": 3
        }
        
        config = {
            # "processing_mode": "intelligent",
            "optimization_target": "quality"
        }
        
        survey_data = {
            "title": content.get("title", "Untitled Survey"),
            "content_items": content,
            # "content_count": len(content) # 此处content应为List[Dict[str, Any]]类型，但是实际到_call_orchestrator时应该是单个dict，这个字段没什么用且有错误，建议去除
        }
        
        return await self._call_orchestrator(survey_data, current_state, config)
    
    async def process_groups(self, content: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理数据分组"""
        if not self.group_session:
            await self.connect()
        
        try:

            read, write = self.group_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "group_data_intelligently",
                    {
                        # "dataset": content,
                        "dataset": content["papers"],
                        "grouping_objective": "根据内容语义和主题相关性进行智能分组",
                        "expected_groups": 3,
                        "grouping_strategy": "semantic"
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"groups": [], "confidence": 0.0}
                
        except Exception as e:
            logger.error(f"Error in process_groups: {e}")
            return {"groups": [], "confidence": 0.0, "error": str(e)}
    
    async def generate_skeleton(self, groups: Dict[str, Any]) -> Dict[str, Any]:
        """生成研究骨架"""
        if not self.skeleton_session:
            await self.connect()
        
        try:

            read, write = self.skeleton_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "generate_intelligent_skeleton",
                    {
                        "topic": "基于分组数据的智能研究大纲",
                        "description": f"基于 {len(groups.get('groups', []))} 个数据分组生成结构化大纲",
                        "expected_depth": "medium"
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"skeleton": {}, "confidence": 0.0}
                
        except Exception as e:
            logger.error(f"Error in generate_skeleton: {e}")
            return {"skeleton": {}, "confidence": 0.0, "error": str(e)}
    
    async def refine_skeleton(self, skeleton: Dict[str, Any]) -> Dict[str, Any]:
        if not self.skeleton_session:
            await self.connect()

        try:
            read, write = self.skeleton_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "refine_skeleton_intelligently",
                    {
                        "skeleton": skeleton,
                    }
                )

                if result.content:
                    return json.loads(result.content[0].text)
                else:
                    return {"skeleton": {}, "confidence": 0.0}
        except Exception as e:
            logger.error(f"Error in refine_skeleton: {e}")
            return {"skeleton": {}, "confidence": 0.0, "error": str(e)}

    async def process_digest(self, content: List[Dict[str, Any]], skeleton: Dict[str, Any]) -> Dict[str, Any]:
        """处理摘要生成"""
        if not self.digest_session:
            await self.connect()
        
        try:
            # 合并所有内容
            # 这里的合并方式需要再核实一下，以及content["papers"]字段中只有['title', 'authors', 'bibkey', 'bibitem', 'url', 'latex_url', 'latex_path', 'pdf_url', 'pdf_path', 'md_url', 'latex_length', 'latex', 'abstract', 'abstract_length', 'abstract_token', 'introduction', 'introduction_length', 'introduction_token', 'reference', 'reference_length', 'reference_token', 'txt_length', 'txt_token', 'txt']这些字段，是用abstract、introduction还是txt需要再确认，小体量时暂且用txt
            combined_content = " ".join([item.get("txt", "") for item in content["papers"]])

            read, write = self.digest_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "generate_intelligent_digest",
                    {
                        "content": combined_content,
                        "outline": skeleton.get("skeleton", {}),
                        "topic": "智能数据处理摘要",
                        "digest_type": "comprehensive"
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"digest": {}, "confidence": 0.0}
                
        except Exception as e:
            logger.error(f"Error in process_digest: {e}")
            return {"digest": {}, "confidence": 0.0, "error": str(e)}

    # ==================== 新增的 MCP 工具方法 ====================
    
    async def apply_convolution_layer(self, input_data: Dict[str, Any], strategy: str = "adaptive", parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """应用卷积层处理"""
        if not self.convolution_session:
            await self.connect()
        
        try:
            read, write = self.convolution_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "apply_convolution_layer",
                    {
                        "input_data": input_data,
                        "processing_strategy": strategy,
                        "layer_parameters": parameters or {}
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"output": {}, "confidence": 0.0}
                
        except Exception as e:
            logger.error(f"Error in apply_convolution_layer: {e}")
            return {"output": {}, "confidence": 0.0, "error": str(e)}
    
    async def evaluate_outline_quality(self, outline: Dict[str, Any], criteria: str = "comprehensive") -> Dict[str, Any]:
        """评估大纲质量"""
        if not self.convolution_session:
            await self.connect()
        
        try:
            result = await self.convolution_session.call_tool(
                "evaluate_outline_quality",
                {
                    "outline": outline,
                    "evaluation_criteria": criteria,
                    "focus_areas": ["structure", "completeness", "coherence"]
                }
            )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"score": 0.0, "feedback": []}
                
        except Exception as e:
            logger.error(f"Error in evaluate_outline_quality: {e}")
            return {"score": 0.0, "feedback": [], "error": str(e)}
    
    async def modify_outline_intelligently(self, outline: Dict[str, Any], instructions: str, strategy: str = "moderate") -> Dict[str, Any]:
        """智能修改大纲"""
        if not self.convolution_session:
            await self.connect()
        
        try:
            read, write = self.convolution_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "modify_outline_intelligently",
                    {
                        "outline": outline,
                        "modification_instructions": instructions,
                        "modification_strategy": strategy
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"modified_outline": outline, "confidence": 0.0}
                
        except Exception as e:
            logger.error(f"Error in modify_outline_intelligently: {e}")
            return {"modified_outline": outline, "confidence": 0.0, "error": str(e)}
    
    async def cluster_feedback_intelligently(self, feedback_list: List[Dict[str, Any]], strategy: str = "semantic") -> Dict[str, Any]:
        """智能聚类反馈"""
        if not self.convolution_session:
            await self.connect()
        
        try:

            read, write = self.convolution_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "cluster_feedback_intelligently",
                    {
                        "feedback_list": feedback_list,
                        "clustering_strategy": strategy,
                        "cluster_count": 5
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"clusters": [], "summary": {}}
                
        except Exception as e:
            logger.error(f"Error in cluster_feedback_intelligently: {e}")
            return {"clusters": [], "summary": {}, "error": str(e)}
    
    async def process_survey_digests(self, surveys: List[Dict[str, Any]], strategy: str = "comprehensive") -> Dict[str, Any]:
        """处理调研摘要"""
        if not self.basic_modules_session:
            await self.connect()
        
        try:
            # result = await self.basic_modules_session.call_tool(
            #     "process_survey_digests",
            #     {
            #         "surveys": surveys,
            #         "processing_strategy": strategy,
            #         "quality_threshold": 0.7
            #     }
            # )

            read, write = self.basic_modules_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "process_survey_digests",
                    {
                        "surveys": surveys,
                        "processing_strategy": strategy,
                        "quality_threshold": 0.7
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"processed_digests": [], "summary": {}}
                
        except Exception as e:
            logger.error(f"Error in process_survey_digests: {e}")
            return {"processed_digests": [], "summary": {}, "error": str(e)}
    
    async def group_papers_intelligently(self, papers: List[Dict[str, Any]], criteria: str = "semantic_similarity") -> Dict[str, Any]:
        """智能分组论文"""
        if not self.basic_modules_session:
            await self.connect()
        
        try:
            read, write = self.basic_modules_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "group_papers_intelligently",
                    {
                        "papers": papers,
                        "grouping_criteria": criteria,
                        "target_group_count": 5
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"groups": [], "summary": {}}
                
        except Exception as e:
            logger.error(f"Error in group_papers_intelligently: {e}")
            return {"groups": [], "summary": {}, "error": str(e)}
    
    async def initialize_survey_skeleton(self, topic: str, structure_requirements: Dict[str, Any] = None) -> Dict[str, Any]:
        """初始化调研骨架"""
        if not self.basic_modules_session:
            await self.connect()
        
        try:
            read, write = self.basic_modules_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "initialize_survey_skeleton",
                    {
                        "survey_topic": topic,
                        "structure_requirements": structure_requirements or {},
                        "skeleton_type": "comprehensive"
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"skeleton": {}, "confidence": 0.0}
                
        except Exception as e:
            logger.error(f"Error in initialize_survey_skeleton: {e}")
            return {"skeleton": {}, "confidence": 0.0, "error": str(e)}
    
    async def evaluate_content_intelligently(self, content: Dict[str, Any], criteria: str = "comprehensive") -> Dict[str, Any]:
        """智能评估内容"""
        if not self.neurons_session:
            await self.connect()
        
        try:
            # result = await self.neurons_session.call_tool(
            #     "evaluate_content_intelligently",
            #     {
            #         "content": content,
            #         "criteria": criteria,
            #         "focus_areas": ["quality", "coherence", "completeness"]
            #     }
            # )

            read, write = self.neurons_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "evaluate_content_intelligently",
                    {
                        "content": content,
                        "criteria": criteria,
                        "focus_areas": ["quality", "coherence", "completeness"]
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"score": 0.0, "analysis": {}}
                
        except Exception as e:
            logger.error(f"Error in evaluate_content_intelligently: {e}")
            return {"score": 0.0, "analysis": {}, "error": str(e)}
    
    async def modify_content_intelligently(self, content: Dict[str, Any], instructions: str, strategy: str = "moderate") -> Dict[str, Any]:
        """智能修改内容"""
        if not self.neurons_session:
            await self.connect()
        
        try:
            # result = await self.neurons_session.call_tool(
            #     "modify_content_intelligently",
            #     {
            #         "content": content,
            #         "modification_instructions": instructions,
            #         "modification_strategy": strategy
            #     }
            # )

            read, write = self.neurons_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "modify_content_intelligently",
                    {
                        "content": content,
                        "modification_instructions": instructions,
                        "modification_strategy": strategy
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"modified_content": content, "confidence": 0.0}
                
        except Exception as e:
            logger.error(f"Error in modify_content_intelligently: {e}")
            return {"modified_content": content, "confidence": 0.0, "error": str(e)}
    
    async def apply_convolution_kernel(self, input_data: Dict[str, Any], kernel_type: str = "feature_extraction") -> Dict[str, Any]:
        """应用卷积核"""
        if not self.neurons_session:
            await self.connect()
        
        try:
            # result = await self.neurons_session.call_tool(
            #     "apply_convolution_kernel",
            #     {
            #         "input_data": input_data,
            #         "kernel_type": kernel_type,
            #         "kernel_size": 3
            #     }
            # )

            read, write = self.neurons_session
            async with ClientSession(read, write) as session:
                await session.initialize()

                result = await session.call_tool(
                    "apply_convolution_kernel",
                    {
                        "input_data": input_data,
                        "kernel_type": kernel_type,
                        "kernel_size": 3
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"output": {}, "features": []}
                
        except Exception as e:
            logger.error(f"Error in apply_convolution_kernel: {e}")
            return {"output": {}, "features": [], "error": str(e)}
    
    async def cluster_feedback_neural(self, feedback_list: List[Dict[str, Any]], strategy: str = "semantic") -> Dict[str, Any]:
        """神经网络方法聚类反馈"""
        if not self.neurons_session:
            await self.connect()
        
        try:
            # result = await self.neurons_session.call_tool(
            #     "cluster_feedback_neural",
            #     {
            #         "feedback_list": feedback_list,
            #         "clustering_strategy": strategy,
            #         "cluster_count": 5
            #     }
            # )

            read, write = self.neurons_session
            async with ClientSession(read, write) as session:   
                await session.initialize()

                result = await session.call_tool(
                    "cluster_feedback_neural",
                    {
                        "feedback_list": feedback_list,
                        "clustering_strategy": strategy,
                        "cluster_count": 5
                    }
                )
            
            if result.content:
                return json.loads(result.content[0].text)
            else:
                return {"clusters": [], "analysis": {}}
                
        except Exception as e:
            logger.error(f"Error in cluster_feedback_neural: {e}")
            return {"clusters": [], "analysis": {}, "error": str(e)}
    
    # ==================== 高级智能流水线方法 ====================
    
    async def process_survey_with_mcp_pipeline(self, survey: Survey, config: Dict[str, Any]) -> Survey:
        """使用新的MCP组件处理整个调研流水线"""
        logger.info("Starting intelligent survey processing with enhanced MCP pipeline...")
        
        try:
            # 步骤1: 使用基础模块初始化骨架
            skeleton_result = await self.initialize_survey_skeleton(
                topic=survey.topic,
                structure_requirements=config.get("skeleton_requirements", {})
            )
            
            # 步骤2: 使用基础模块分组论文
            papers_data = [{"title": paper.title, "content": paper.content} for paper in survey.papers]
            grouping_result = await self.group_papers_intelligently(
                papers=papers_data,
                criteria=config.get("grouping_criteria", "semantic_similarity")
            )
            
            # 步骤3: 使用基础模块处理摘要
            digest_result = await self.process_survey_digests(
                surveys=[{"papers": papers_data, "topic": survey.topic}],
                strategy=config.get("digest_strategy", "comprehensive")
            )
            
            # 步骤4: 使用卷积层优化结构
            convolution_result = await self.apply_convolution_layer(
                input_data={
                    "skeleton": skeleton_result.get("skeleton", {}),
                    "groups": grouping_result.get("groups", []),
                    "digests": digest_result.get("processed_digests", [])
                },
                strategy=config.get("convolution_strategy", "adaptive")
            )
            
            # 步骤5: 使用神经元评估和优化
            evaluation_result = await self.evaluate_content_intelligently(
                content=convolution_result.get("output", {}),
                criteria=config.get("evaluation_criteria", "comprehensive")
            )
            
            # 如果质量不够，进行智能修改
            if evaluation_result.get("score", 0) < config.get("quality_threshold", 0.8):
                modification_result = await self.modify_content_intelligently(
                    content=convolution_result.get("output", {}),
                    instructions="根据评估反馈改进内容质量和结构",
                    strategy=config.get("modification_strategy", "moderate")
                )
                final_content = modification_result.get("modified_content", {})
            else:
                final_content = convolution_result.get("output", {})
            
            # 更新Survey对象
            survey.skeleton = final_content.get("skeleton", {})
            survey.groups = final_content.get("groups", [])
            survey.digests = final_content.get("digests", [])
            
            logger.info("Enhanced MCP pipeline processing completed successfully")
            return survey
            
        except Exception as e:
            logger.error(f"Error in enhanced MCP pipeline processing: {e}")
            return survey

async def main():
    """测试客户端"""
    client = IntelligentPipelineClient()
    
    try:
        await client.connect()

        with open("/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2/survey_data_full_1231_one_line.jsonl", "r", encoding="utf-8") as f:
            json_data = json.load(f)
        
        # 创建测试调研
        from src.data_structure import Survey
        # test_survey = Survey(title="人工智能在医疗领域的应用")
        # test_survey = Survey(json_data)
        
        # 处理调研
        config = {
            "max_cycles": 2,
            "expected_groups": 3
        }
        
        # processed_survey = await client.process_survey_intelligently(test_survey, config)
        # processed_survey = await client.process_survey_intelligently(json_data, config)
        # print(f"Processed survey: {processed_survey.title}")
        await client.test_servers(json_data)

    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
