#!/usr/bin/env python3
"""
Neurons MCP Toolkit - 神经元处理工具包
将各种神经元算法转换为可通过自然语言控制的智能工具
"""

import json
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple, Union
from camel.toolkits import BaseToolkit, FunctionTool

from src.data_structure import Survey, Feedback, Skeleton, Digest
from src.hidden.convolution_block.neurons import (
    ModifyOutlineNeuron,
    EvalOutlineNeuron,
    ConvolutionKernelNeuron,
    FeedbackClusterNeuron
)

logger = logging.getLogger(__name__)


class NeuronsToolkit(BaseToolkit):
    """神经元工具包 - 提供各种神经元算法的智能处理能力"""

    def __init__(self, config: Dict[str, Any], timeout: Optional[float] = None):
        """初始化神经元工具包
        
        Args:
            config: 神经元配置
            timeout: 操作超时时间
        """
        super().__init__(timeout=timeout)
        self.config = config
        
        # 初始化神经元
        self._init_neurons()
        
        logger.info("NeuronsToolkit initialized")

    def _init_neurons(self):
        """初始化各种神经元"""
        try:
            self.modify_neuron = ModifyOutlineNeuron(
                self.config.get("modify", {}), 
                mode=self.config.get("modify_mode", "residual")
            )
            self.eval_neuron = EvalOutlineNeuron(self.config.get("eval", {}))
            self.convolution_kernel = ConvolutionKernelNeuron(self.config.get("kernel", {}))
            self.feedback_cluster = FeedbackClusterNeuron(self.config.get("cluster", {}))
        except Exception as e:
            logger.warning(f"Some neurons failed to initialize: {e}")

    @FunctionTool
    async def evaluate_content_intelligently(
        self,
        content: str,
        evaluation_context: Dict[str, Any],
        evaluation_criteria: List[str] = None,
        scoring_method: str = "comprehensive"
    ) -> Dict[str, Any]:
        """智能评估内容质量
        
        Args:
            content: 待评估的内容
            evaluation_context: 评估上下文（如标题、领域等）
            evaluation_criteria: 评估标准列表
            scoring_method: 评分方法 ("comprehensive", "focused", "comparative")
            
        Returns:
            Dict[str, Any]: 评估结果包含分数和详细分析
        """
        try:
            logger.info(f"Evaluating content with method: {scoring_method}")
            
            title = evaluation_context.get("title", "")
            domain = evaluation_context.get("domain", "general")
            
            # 使用评估神经元
            if hasattr(self, 'eval_neuron'):
                primary_score = self.eval_neuron(title, content)
            else:
                primary_score = await self._simple_content_evaluation(content, evaluation_context)
            
            # 详细评估各个维度
            criteria = evaluation_criteria or [
                "内容质量", "结构清晰度", "逻辑连贯性", "完整性", "相关性"
            ]
            
            detailed_scores = {}
            for criterion in criteria:
                score = await self._evaluate_specific_dimension(
                    content, criterion, evaluation_context
                )
                detailed_scores[criterion] = score
            
            # 生成评估洞察
            insights = await self._generate_evaluation_insights(
                content, primary_score, detailed_scores, scoring_method
            )
            
            # 提供改进建议
            improvement_suggestions = await self._generate_improvement_suggestions(
                content, detailed_scores, evaluation_context
            )
            
            return {
                "success": True,
                "primary_score": primary_score,
                "detailed_scores": detailed_scores,
                "overall_rating": self._calculate_overall_rating(primary_score, detailed_scores),
                "scoring_method": scoring_method,
                "evaluation_criteria": criteria,
                "insights": insights,
                "improvement_suggestions": improvement_suggestions,
                "confidence": self._calculate_confidence(detailed_scores)
            }
            
        except Exception as e:
            logger.error(f"Error evaluating content: {e}")
            return {
                "success": False,
                "error": str(e),
                "primary_score": 0.5,
                "detailed_scores": {}
            }

    @FunctionTool
    async def modify_content_intelligently(
        self,
        original_content: str,
        modification_instructions: str,
        modification_context: Dict[str, Any],
        modification_strategy: str = "residual"
    ) -> Dict[str, Any]:
        """智能修改内容
        
        Args:
            original_content: 原始内容
            modification_instructions: 修改指令
            modification_context: 修改上下文
            modification_strategy: 修改策略 ("residual", "complete", "incremental", "targeted")
            
        Returns:
            Dict[str, Any]: 修改结果
        """
        try:
            logger.info(f"Modifying content with strategy: {modification_strategy}")
            
            # 使用修改神经元
            if hasattr(self, 'modify_neuron'):
                modified_content = await self._neural_content_modification(
                    original_content, modification_instructions, modification_context, modification_strategy
                )
            else:
                modified_content = await self._simple_content_modification(
                    original_content, modification_instructions, modification_context, modification_strategy
                )
            
            # 评估修改质量
            original_quality = await self._simple_content_evaluation(original_content, modification_context)
            modified_quality = await self._simple_content_evaluation(modified_content, modification_context)
            
            # 分析修改效果
            modification_analysis = await self._analyze_modification_effect(
                original_content, modified_content, modification_instructions
            )
            
            return {
                "success": True,
                "original_content": original_content,
                "modified_content": modified_content,
                "modification_instructions": modification_instructions,
                "modification_strategy": modification_strategy,
                "quality_improvement": modified_quality - original_quality,
                "original_quality": original_quality,
                "modified_quality": modified_quality,
                "modification_analysis": modification_analysis
            }
            
        except Exception as e:
            logger.error(f"Error modifying content: {e}")
            return {
                "success": False,
                "error": str(e),
                "original_content": original_content,
                "modified_content": original_content
            }

    @FunctionTool
    async def apply_convolution_kernel(
        self,
        input_data: Union[str, List[str]],
        kernel_parameters: Dict[str, Any],
        processing_mode: str = "adaptive"
    ) -> Dict[str, Any]:
        """应用卷积核处理
        
        Args:
            input_data: 输入数据（文本或文本列表）
            kernel_parameters: 卷积核参数
            processing_mode: 处理模式 ("adaptive", "fixed", "dynamic")
            
        Returns:
            Dict[str, Any]: 卷积处理结果
        """
        try:
            logger.info(f"Applying convolution kernel in mode: {processing_mode}")
            
            # 标准化输入数据
            if isinstance(input_data, str):
                data_list = [input_data]
            else:
                data_list = input_data
            
            # 配置卷积参数
            kernel_size = kernel_parameters.get("kernel_size", 3)
            stride = kernel_parameters.get("stride", 1)
            padding = kernel_parameters.get("padding", "same")
            
            # 应用卷积核
            if hasattr(self, 'convolution_kernel'):
                convolution_results = await self._neural_convolution_processing(
                    data_list, kernel_parameters, processing_mode
                )
            else:
                convolution_results = await self._simple_convolution_processing(
                    data_list, kernel_parameters, processing_mode
                )
            
            # 分析卷积效果
            processing_analysis = await self._analyze_convolution_effect(
                data_list, convolution_results, kernel_parameters
            )
            
            return {
                "success": True,
                "input_data": input_data,
                "convolution_results": convolution_results,
                "kernel_parameters": kernel_parameters,
                "processing_mode": processing_mode,
                "processing_analysis": processing_analysis,
                "output_dimension": len(convolution_results)
            }
            
        except Exception as e:
            logger.error(f"Error applying convolution kernel: {e}")
            return {
                "success": False,
                "error": str(e),
                "input_data": input_data,
                "convolution_results": []
            }

    @FunctionTool
    async def cluster_feedback_neural(
        self,
        feedback_data: List[Dict[str, Any]],
        clustering_parameters: Dict[str, Any],
        neural_approach: str = "semantic"
    ) -> Dict[str, Any]:
        """使用神经方法聚类反馈
        
        Args:
            feedback_data: 反馈数据列表
            clustering_parameters: 聚类参数
            neural_approach: 神经方法 ("semantic", "hierarchical", "adaptive")
            
        Returns:
            Dict[str, Any]: 聚类结果
        """
        try:
            logger.info(f"Neural clustering with approach: {neural_approach}")
            
            if not feedback_data:
                return {
                    "success": True,
                    "clusters": [],
                    "cluster_count": 0,
                    "message": "No feedback data to cluster"
                }
            
            # 配置聚类参数
            target_clusters = clustering_parameters.get("target_clusters")
            similarity_threshold = clustering_parameters.get("similarity_threshold", 0.7)
            
            # 使用神经聚类
            if hasattr(self, 'feedback_cluster'):
                clusters = await self._neural_feedback_clustering(
                    feedback_data, clustering_parameters, neural_approach
                )
            else:
                clusters = await self._simple_feedback_clustering(
                    feedback_data, clustering_parameters, neural_approach
                )
            
            # 评估聚类质量
            clustering_quality = await self._evaluate_clustering_quality(clusters, feedback_data)
            
            # 生成聚类洞察
            clustering_insights = await self._generate_clustering_insights(
                clusters, feedback_data, neural_approach
            )
            
            return {
                "success": True,
                "clusters": clusters,
                "cluster_count": len(clusters),
                "neural_approach": neural_approach,
                "clustering_parameters": clustering_parameters,
                "clustering_quality": clustering_quality,
                "insights": clustering_insights
            }
            
        except Exception as e:
            logger.error(f"Error in neural feedback clustering: {e}")
            return {
                "success": False,
                "error": str(e),
                "clusters": [],
                "cluster_count": 0
            }

    @FunctionTool
    async def optimize_neural_parameters(
        self,
        task_type: str,
        performance_history: List[Dict[str, Any]],
        optimization_target: str = "quality"
    ) -> Dict[str, Any]:
        """优化神经网络参数
        
        Args:
            task_type: 任务类型 ("evaluation", "modification", "convolution", "clustering")
            performance_history: 性能历史记录
            optimization_target: 优化目标 ("quality", "speed", "balance")
            
        Returns:
            Dict[str, Any]: 优化后的参数配置
        """
        try:
            logger.info(f"Optimizing neural parameters for {task_type}")
            
            # 分析历史性能
            performance_analysis = await self._analyze_neural_performance(
                performance_history, task_type
            )
            
            # 生成优化策略
            optimization_strategy = await self._generate_optimization_strategy(
                task_type, performance_analysis, optimization_target
            )
            
            # 计算优化参数
            optimized_parameters = await self._calculate_optimized_parameters(
                task_type, optimization_strategy, performance_analysis
            )
            
            # 预测优化效果
            predicted_improvement = await self._predict_optimization_effect(
                optimized_parameters, performance_analysis
            )
            
            return {
                "success": True,
                "task_type": task_type,
                "optimization_target": optimization_target,
                "optimized_parameters": optimized_parameters,
                "optimization_strategy": optimization_strategy,
                "performance_analysis": performance_analysis,
                "predicted_improvement": predicted_improvement
            }
            
        except Exception as e:
            logger.error(f"Error optimizing neural parameters: {e}")
            return {
                "success": False,
                "error": str(e),
                "task_type": task_type,
                "optimized_parameters": {}
            }

    # 私有辅助方法
    async def _simple_content_evaluation(self, content: str, context: Dict[str, Any]) -> float:
        """简单内容评估"""
        # 基础评估指标
        length_score = min(1.0, len(content) / 500)  # 长度适中性
        structure_score = 0.8 if content.count('\n') > 2 else 0.5  # 结构化程度
        
        # 关键词相关性
        title = context.get("title", "").lower()
        content_lower = content.lower()
        title_words = set(title.split())
        content_words = set(content_lower.split())
        
        if title_words and content_words:
            relevance_score = len(title_words & content_words) / len(title_words)
        else:
            relevance_score = 0.5
        
        return (length_score + structure_score + relevance_score) / 3

    async def _evaluate_specific_dimension(
        self, content: str, criterion: str, context: Dict[str, Any]
    ) -> float:
        """评估特定维度"""
        base_score = await self._simple_content_evaluation(content, context)
        
        # 根据不同标准调整分数
        adjustments = {
            "内容质量": 0.1,
            "结构清晰度": 0.05 if '\n' in content else -0.1,
            "逻辑连贯性": 0.08,
            "完整性": 0.1 if len(content) > 200 else -0.05,
            "相关性": 0.15
        }
        
        adjustment = adjustments.get(criterion, 0.0)
        return max(0.0, min(1.0, base_score + adjustment))

    async def _generate_evaluation_insights(
        self, content: str, primary_score: float, detailed_scores: Dict[str, float], method: str
    ) -> List[str]:
        """生成评估洞察"""
        insights = []
        
        if primary_score > 0.8:
            insights.append("内容整体质量优秀，表现出色")
        elif primary_score > 0.6:
            insights.append("内容质量良好，但仍有改进空间")
        else:
            insights.append("内容质量需要显著提升")
        
        # 分析最强和最弱的维度
        if detailed_scores:
            best_dimension = max(detailed_scores.items(), key=lambda x: x[1])
            worst_dimension = min(detailed_scores.items(), key=lambda x: x[1])
            
            insights.append(f"最强维度: {best_dimension[0]} (得分: {best_dimension[1]:.2f})")
            insights.append(f"最弱维度: {worst_dimension[0]} (得分: {worst_dimension[1]:.2f})")
        
        return insights

    async def _generate_improvement_suggestions(
        self, content: str, detailed_scores: Dict[str, float], context: Dict[str, Any]
    ) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        for dimension, score in detailed_scores.items():
            if score < 0.6:
                if dimension == "内容质量":
                    suggestions.append("建议丰富内容细节，提供更多有价值的信息")
                elif dimension == "结构清晰度":
                    suggestions.append("建议优化内容结构，使用标题和段落组织内容")
                elif dimension == "逻辑连贯性":
                    suggestions.append("建议改善内容的逻辑流程，确保前后连贯")
                elif dimension == "完整性":
                    suggestions.append("建议补充缺失的内容部分，提高完整性")
                elif dimension == "相关性":
                    suggestions.append("建议增强内容与主题的相关性")
        
        return suggestions

    def _calculate_overall_rating(self, primary_score: float, detailed_scores: Dict[str, float]) -> str:
        """计算整体评级"""
        if detailed_scores:
            avg_detailed = sum(detailed_scores.values()) / len(detailed_scores)
            combined_score = (primary_score + avg_detailed) / 2
        else:
            combined_score = primary_score
        
        if combined_score >= 0.9:
            return "优秀"
        elif combined_score >= 0.7:
            return "良好"
        elif combined_score >= 0.5:
            return "一般"
        else:
            return "需改进"

    def _calculate_confidence(self, detailed_scores: Dict[str, float]) -> float:
        """计算评估置信度"""
        if not detailed_scores:
            return 0.5
        
        # 基于分数分布计算置信度
        scores = list(detailed_scores.values())
        score_variance = sum((s - sum(scores)/len(scores))**2 for s in scores) / len(scores)
        
        # 方差越小，置信度越高
        confidence = 1.0 / (1.0 + score_variance * 2)
        return max(0.3, min(1.0, confidence))

    async def _neural_content_modification(
        self, content: str, instructions: str, context: Dict[str, Any], strategy: str
    ) -> str:
        """使用神经网络修改内容"""
        try:
            if strategy == "residual":
                return self.modify_neuron.forward(content, instructions, context)
            else:
                # 其他策略的回退处理
                return await self._simple_content_modification(content, instructions, context, strategy)
        except Exception as e:
            logger.warning(f"Neural modification failed: {e}")
            return await self._simple_content_modification(content, instructions, context, strategy)

    async def _simple_content_modification(
        self, content: str, instructions: str, context: Dict[str, Any], strategy: str
    ) -> str:
        """简单内容修改"""
        if strategy == "residual":
            return content + f"\n\n[修改说明: {instructions}]"
        elif strategy == "complete":
            return f"基于指令重写: {instructions}\n\n{content}"
        elif strategy == "incremental":
            lines = content.split('\n')
            modified_lines = []
            for i, line in enumerate(lines):
                modified_lines.append(line)
                if i % 3 == 0:  # 每3行插入一次
                    modified_lines.append(f"  [增量修改: {instructions}]")
            return '\n'.join(modified_lines)
        elif strategy == "targeted":
            # 在特定位置插入修改
            mid_point = len(content) // 2
            return content[:mid_point] + f"\n[定向修改: {instructions}]\n" + content[mid_point:]
        else:
            return content

    async def _analyze_modification_effect(
        self, original: str, modified: str, instructions: str
    ) -> Dict[str, Any]:
        """分析修改效果"""
        return {
            "length_change": len(modified) - len(original),
            "modification_ratio": len(modified) / len(original) if len(original) > 0 else 1.0,
            "instruction_relevance": "high" if instructions.lower() in modified.lower() else "medium",
            "structural_changes": abs(modified.count('\n') - original.count('\n'))
        }

    async def _neural_convolution_processing(
        self, data_list: List[str], parameters: Dict[str, Any], mode: str
    ) -> List[Dict[str, Any]]:
        """神经卷积处理"""
        try:
            # 使用卷积核神经元
            results = []
            for data in data_list:
                result = self.convolution_kernel.forward(data, parameters)
                results.append({"input": data, "output": result, "parameters": parameters})
            return results
        except Exception as e:
            logger.warning(f"Neural convolution failed: {e}")
            return await self._simple_convolution_processing(data_list, parameters, mode)

    async def _simple_convolution_processing(
        self, data_list: List[str], parameters: Dict[str, Any], mode: str
    ) -> List[Dict[str, Any]]:
        """简单卷积处理"""
        kernel_size = parameters.get("kernel_size", 3)
        results = []
        
        for data in data_list:
            # 简单的滑动窗口处理
            words = data.split()
            if len(words) >= kernel_size:
                windows = [' '.join(words[i:i+kernel_size]) 
                          for i in range(len(words) - kernel_size + 1)]
                processed = ' | '.join(windows[:5])  # 取前5个窗口
            else:
                processed = data
            
            results.append({
                "input": data,
                "output": processed,
                "windows_count": len(words) - kernel_size + 1 if len(words) >= kernel_size else 0
            })
        
        return results

    async def _analyze_convolution_effect(
        self, inputs: List[str], outputs: List[Dict[str, Any]], parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析卷积效果"""
        total_input_length = sum(len(inp) for inp in inputs)
        total_output_length = sum(len(str(out.get("output", ""))) for out in outputs)
        
        return {
            "compression_ratio": total_output_length / total_input_length if total_input_length > 0 else 1.0,
            "processing_efficiency": len(outputs) / len(inputs) if inputs else 0,
            "average_windows": sum(out.get("windows_count", 0) for out in outputs) / len(outputs) if outputs else 0
        }

    async def _neural_feedback_clustering(
        self, feedback_data: List[Dict[str, Any]], parameters: Dict[str, Any], approach: str
    ) -> List[List[Dict[str, Any]]]:
        """神经反馈聚类"""
        try:
            # 使用聚类神经元
            # 这里需要适配实际的聚类神经元接口
            clusters = await self._simple_feedback_clustering(feedback_data, parameters, approach)
            return clusters
        except Exception as e:
            logger.warning(f"Neural clustering failed: {e}")
            return await self._simple_feedback_clustering(feedback_data, parameters, approach)

    async def _simple_feedback_clustering(
        self, feedback_data: List[Dict[str, Any]], parameters: Dict[str, Any], approach: str
    ) -> List[List[Dict[str, Any]]]:
        """简单反馈聚类"""
        target_clusters = parameters.get("target_clusters", min(3, len(feedback_data)))
        
        if len(feedback_data) <= target_clusters:
            return [[fb] for fb in feedback_data]
        
        # 简单的轮转分配
        clusters = [[] for _ in range(target_clusters)]
        for i, feedback in enumerate(feedback_data):
            cluster_idx = i % target_clusters
            clusters[cluster_idx].append(feedback)
        
        return [cluster for cluster in clusters if cluster]

    async def _evaluate_clustering_quality(
        self, clusters: List[List[Dict[str, Any]]], original_data: List[Dict[str, Any]]
    ) -> float:
        """评估聚类质量"""
        if not clusters or not original_data:
            return 0.0
        
        # 简单的聚类质量评估
        cluster_sizes = [len(cluster) for cluster in clusters]
        
        # 平衡性评估
        avg_size = sum(cluster_sizes) / len(cluster_sizes)
        size_variance = sum((size - avg_size)**2 for size in cluster_sizes) / len(cluster_sizes)
        balance_score = 1.0 / (1.0 + size_variance / avg_size) if avg_size > 0 else 0.5
        
        # 覆盖完整性
        total_clustered = sum(cluster_sizes)
        coverage_score = total_clustered / len(original_data)
        
        return (balance_score + coverage_score) / 2

    async def _generate_clustering_insights(
        self, clusters: List[List[Dict[str, Any]]], original_data: List[Dict[str, Any]], approach: str
    ) -> List[str]:
        """生成聚类洞察"""
        insights = []
        
        if clusters:
            insights.append(f"共生成 {len(clusters)} 个聚类")
            
            cluster_sizes = [len(cluster) for cluster in clusters]
            insights.append(f"聚类大小范围: {min(cluster_sizes)} - {max(cluster_sizes)}")
            
            if len(set(cluster_sizes)) == 1:
                insights.append("聚类大小分布均匀")
            else:
                insights.append("聚类大小分布不均匀")
        
        return insights

    async def _analyze_neural_performance(
        self, history: List[Dict[str, Any]], task_type: str
    ) -> Dict[str, Any]:
        """分析神经网络性能"""
        if not history:
            return {"analysis": "no_data"}
        
        # 性能指标统计
        processing_times = [h.get("processing_time", 0) for h in history]
        quality_scores = [h.get("quality_score", 0) for h in history]
        success_rates = [1 if h.get("success", False) else 0 for h in history]
        
        return {
            "avg_processing_time": sum(processing_times) / len(processing_times),
            "avg_quality_score": sum(quality_scores) / len(quality_scores),
            "success_rate": sum(success_rates) / len(success_rates),
            "total_operations": len(history),
            "task_type": task_type
        }

    async def _generate_optimization_strategy(
        self, task_type: str, performance: Dict[str, Any], target: str
    ) -> Dict[str, Any]:
        """生成优化策略"""
        strategy = {"optimization_focus": target}
        
        if target == "quality":
            strategy["adjustments"] = {
                "increase_model_complexity": True,
                "enhance_preprocessing": True,
                "add_validation_steps": True
            }
        elif target == "speed":
            strategy["adjustments"] = {
                "reduce_model_complexity": True,
                "enable_caching": True,
                "parallel_processing": True
            }
        else:  # balance
            strategy["adjustments"] = {
                "moderate_complexity": True,
                "selective_caching": True,
                "adaptive_processing": True
            }
        
        return strategy

    async def _calculate_optimized_parameters(
        self, task_type: str, strategy: Dict[str, Any], performance: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算优化后的参数"""
        base_params = {
            "learning_rate": 0.001,
            "batch_size": 32,
            "hidden_units": 128,
            "dropout_rate": 0.1
        }
        
        adjustments = strategy.get("adjustments", {})
        
        if adjustments.get("increase_model_complexity"):
            base_params["hidden_units"] *= 2
            base_params["dropout_rate"] *= 0.8
        elif adjustments.get("reduce_model_complexity"):
            base_params["hidden_units"] //= 2
            base_params["batch_size"] *= 2
        
        if adjustments.get("parallel_processing"):
            base_params["parallel_workers"] = 4
        
        return base_params

    async def _predict_optimization_effect(
        self, optimized_params: Dict[str, Any], performance: Dict[str, Any]
    ) -> Dict[str, Any]:
        """预测优化效果"""
        current_quality = performance.get("avg_quality_score", 0.5)
        current_speed = performance.get("avg_processing_time", 10.0)
        
        # 简单的预测逻辑
        if optimized_params.get("hidden_units", 0) > 128:
            predicted_quality = min(1.0, current_quality * 1.2)
            predicted_speed = current_speed * 1.3
        else:
            predicted_quality = max(0.0, current_quality * 0.9)
            predicted_speed = current_speed * 0.7
        
        return {
            "predicted_quality_improvement": predicted_quality - current_quality,
            "predicted_speed_change": predicted_speed - current_speed,
            "overall_improvement": (predicted_quality - current_quality) / (predicted_speed / current_speed)
        }


# 导出工具包
def get_neurons_tools() -> List[FunctionTool]:
    """获取神经元相关的工具列表"""
    toolkit = NeuronsToolkit({})
    return toolkit.get_tools()
