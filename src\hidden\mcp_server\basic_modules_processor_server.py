#!/usr/bin/env python3
"""
Basic Modules MCP Server - 基础模块处理服务器
提供digest、group、skeleton等基础算法的智能处理能力
"""

import json
import logging
import asyncio
from typing import Dict, Any, List
from mcp.server.fastmcp import FastMCP

from src.hidden.basic_modules.basic_modules_mcp_toolkit import BasicModulesToolkit

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastMCP服务器实例
mcp = FastMCP("Basic Modules Processor")

# 全局工具包实例
toolkit = None

@mcp.tool()
async def process_survey_digests(
    survey_data: dict,
    digest_strategy: str = "comprehensive",
    quality_threshold: float = 0.7
) -> dict:
    """处理调研摘要生成
    
    Args:
        survey_data: 调研数据字典
        digest_strategy: 摘要策略 (comprehensive/focused/brief)
        quality_threshold: 质量阈值 (0.0-1.0)
    
    Returns:
        dict: 包含生成摘要和质量评估的结果
    """
    global toolkit
    if toolkit is None:
        # 默认配置
        config = {
            "digest": {
                "single": {"model": "gpt-3.5-turbo", "infer_type": "chat"},
                "merge": {"model": "gpt-3.5-turbo", "infer_type": "chat"}
            }
        }
        toolkit = BasicModulesToolkit(config)
    
    return await toolkit.process_survey_digests(
        survey_data, digest_strategy, quality_threshold
    )

@mcp.tool()
async def group_papers_intelligently(
    survey_data: dict,
    grouping_mode: str = "llm",
    target_group_size: int = 5,
    grouping_criteria: list = None
) -> dict:
    """智能分组论文
    
    Args:
        survey_data: 调研数据字典
        grouping_mode: 分组模式 (llm/random/semantic)
        target_group_size: 目标分组大小
        grouping_criteria: 分组标准列表
    
    Returns:
        dict: 分组结果
    """
    global toolkit
    if toolkit is None:
        config = {
            "group": {
                "neuron": {"model": "gpt-3.5-turbo", "infer_type": "chat"}
            },
            "group_mode": grouping_mode,
            "digest_batch": target_group_size
        }
        toolkit = BasicModulesToolkit(config)
    
    return await toolkit.group_papers_intelligently(
        survey_data, grouping_mode, target_group_size, grouping_criteria
    )

@mcp.tool()
async def initialize_survey_skeleton(
    survey_data: dict,
    skeleton_depth: str = "medium",
    structure_type: str = "hierarchical",
    include_methodology: bool = True
) -> dict:
    """初始化调研骨架
    
    Args:
        survey_data: 调研数据字典
        skeleton_depth: 骨架深度 (shallow/medium/deep)
        structure_type: 结构类型 (hierarchical/flat/network)
        include_methodology: 是否包含方法论部分
    
    Returns:
        dict: 骨架初始化结果
    """
    global toolkit
    if toolkit is None:
        config = {
            "skeleton": {"model": "gpt-3.5-turbo", "infer_type": "chat"}
        }
        toolkit = BasicModulesToolkit(config)
    
    return await toolkit.initialize_survey_skeleton(
        survey_data, skeleton_depth, structure_type, include_methodology
    )

@mcp.tool()
async def optimize_digest_quality(
    digest_data: dict,
    optimization_target: str = "comprehensiveness",
    context_info: dict = None
) -> dict:
    """优化摘要质量
    
    Args:
        digest_data: 摘要数据字典
        optimization_target: 优化目标 (comprehensiveness/conciseness/accuracy)
        context_info: 上下文信息字典
    
    Returns:
        dict: 优化结果
    """
    global toolkit
    if toolkit is None:
        config = {}
        toolkit = BasicModulesToolkit(config)
    
    return await toolkit.optimize_digest_quality(
        digest_data, optimization_target, context_info
    )

@mcp.tool()
async def merge_related_digests(
    digest_list: list,
    merge_strategy: str = "semantic",
    similarity_threshold: float = 0.7
) -> dict:
    """合并相关摘要
    
    Args:
        digest_list: 摘要列表
        merge_strategy: 合并策略 (semantic/topic/structural)
        similarity_threshold: 相似度阈值 (0.0-1.0)
    
    Returns:
        dict: 合并结果
    """
    global toolkit
    if toolkit is None:
        config = {}
        toolkit = BasicModulesToolkit(config)
    
    return await toolkit.merge_related_digests(
        digest_list, merge_strategy, similarity_threshold
    )

@mcp.tool()
async def analyze_processing_workflow(
    survey_data: dict,
    current_state: dict,
    processing_goals: list
) -> dict:
    """分析处理工作流
    
    Args:
        survey_data: 调研数据字典
        current_state: 当前处理状态
        processing_goals: 处理目标列表
    
    Returns:
        dict: 工作流分析结果和建议
    """
    try:
        # 分析调研数据特征
        paper_count = len(survey_data.get("papers", {}))
        has_skeleton = "skeleton" in current_state
        has_digests = "digests" in current_state
        has_groups = "groups" in current_state
        
        # 数据复杂度评估
        complexity = "low"
        if paper_count > 20:
            complexity = "medium"
        if paper_count > 50:
            complexity = "high"
        
        # 生成处理建议
        recommendations = []
        next_steps = []
        
        # 分组建议
        if not has_groups and paper_count > 5:
            recommendations.append("建议先进行论文分组以便于后续处理")
            next_steps.append({
                "action": "group_papers",
                "priority": "high",
                "parameters": {
                    "grouping_mode": "llm" if complexity != "low" else "random",
                    "target_group_size": min(8, max(3, paper_count // 5))
                }
            })
        
        # 骨架建议
        if not has_skeleton:
            recommendations.append("建议初始化调研骨架以建立结构框架")
            next_steps.append({
                "action": "initialize_skeleton",
                "priority": "high",
                "parameters": {
                    "skeleton_depth": complexity,
                    "structure_type": "hierarchical"
                }
            })
        
        # 摘要建议
        if not has_digests or (has_groups and not has_digests):
            recommendations.append("建议生成论文摘要以提取关键信息")
            next_steps.append({
                "action": "process_digests",
                "priority": "medium",
                "parameters": {
                    "digest_strategy": "comprehensive" if complexity == "high" else "focused"
                }
            })
        
        # 质量优化建议
        if has_digests:
            recommendations.append("考虑优化现有摘要质量")
            next_steps.append({
                "action": "optimize_digests",
                "priority": "low",
                "parameters": {
                    "optimization_target": "comprehensiveness"
                }
            })
        
        # 处理顺序建议
        processing_order = []
        if not has_groups and paper_count > 5:
            processing_order.append("分组处理")
        if not has_skeleton:
            processing_order.append("骨架初始化")
        if not has_digests:
            processing_order.append("摘要生成")
        if has_digests:
            processing_order.append("质量优化")
        
        return {
            "survey_analysis": {
                "paper_count": paper_count,
                "complexity": complexity,
                "current_components": {
                    "has_skeleton": has_skeleton,
                    "has_digests": has_digests,
                    "has_groups": has_groups
                }
            },
            "recommendations": recommendations,
            "next_steps": next_steps,
            "processing_order": processing_order,
            "estimated_processing_time": {
                "grouping": f"{paper_count // 10 + 1} minutes",
                "skeleton": "2-5 minutes",
                "digests": f"{paper_count // 5 + 2} minutes",
                "optimization": "3-8 minutes"
            },
            "resource_requirements": {
                "memory_usage": "low" if paper_count < 20 else "medium",
                "compute_intensity": complexity,
                "parallel_processing": paper_count > 20
            }
        }
        
    except Exception as e:
        logger.error(f"Error analyzing processing workflow: {e}")
        return {
            "error": str(e),
            "recommendations": ["建议检查输入数据格式"],
            "next_steps": []
        }

@mcp.tool()
async def validate_module_integration(
    module_outputs: dict,
    integration_requirements: dict
) -> dict:
    """验证模块集成
    
    Args:
        module_outputs: 各模块输出结果
        integration_requirements: 集成要求
    
    Returns:
        dict: 集成验证结果
    """
    try:
        validation_results = {
            "overall_status": "valid",
            "module_compatibility": {},
            "integration_issues": [],
            "recommendations": []
        }
        
        # 检查模块输出完整性
        required_modules = integration_requirements.get("required_modules", [])
        for module in required_modules:
            if module not in module_outputs:
                validation_results["integration_issues"].append(f"Missing output from {module}")
                validation_results["overall_status"] = "incomplete"
            else:
                # 检查模块输出格式
                output = module_outputs[module]
                if not isinstance(output, dict) or not output.get("success"):
                    validation_results["module_compatibility"][module] = "error"
                    validation_results["integration_issues"].append(f"Invalid output format from {module}")
                else:
                    validation_results["module_compatibility"][module] = "compatible"
        
        # 检查数据一致性
        if "groups" in module_outputs and "digests" in module_outputs:
            groups_data = module_outputs["groups"]
            digests_data = module_outputs["digests"]
            
            if groups_data.get("success") and digests_data.get("success"):
                # 检查分组和摘要的一致性
                group_count = groups_data.get("actual_groups", 0)
                digest_count = len(digests_data.get("digests", {}))
                
                if abs(group_count - digest_count) > 2:  # 允许小的差异
                    validation_results["integration_issues"].append(
                        "Group count and digest count mismatch"
                    )
        
        # 检查骨架和摘要的一致性
        if "skeleton" in module_outputs and "digests" in module_outputs:
            skeleton_data = module_outputs["skeleton"]
            digests_data = module_outputs["digests"]
            
            if skeleton_data.get("success") and digests_data.get("success"):
                skeleton_sections = len(skeleton_data.get("skeleton", {}).get("sections", []))
                if skeleton_sections == 0:
                    validation_results["integration_issues"].append(
                        "Empty skeleton structure"
                    )
        
        # 生成集成建议
        if validation_results["integration_issues"]:
            validation_results["recommendations"].extend([
                "建议检查各模块的输入输出格式",
                "建议重新运行失败的模块",
                "建议调整模块间的参数配置"
            ])
        else:
            validation_results["recommendations"].append("所有模块集成正常，可以继续后续处理")
        
        # 计算集成质量分数
        total_modules = len(required_modules)
        compatible_modules = sum(1 for status in validation_results["module_compatibility"].values() 
                               if status == "compatible")
        integration_score = compatible_modules / total_modules if total_modules > 0 else 0
        
        validation_results["integration_score"] = integration_score
        validation_results["quality_level"] = (
            "excellent" if integration_score > 0.9 else
            "good" if integration_score > 0.7 else
            "acceptable" if integration_score > 0.5 else "poor"
        )
        
        return validation_results
        
    except Exception as e:
        logger.error(f"Error validating module integration: {e}")
        return {
            "overall_status": "error",
            "error": str(e),
            "recommendations": ["建议检查集成配置和模块输出格式"]
        }

@mcp.tool()
async def suggest_processing_optimizations(
    processing_history: list,
    performance_metrics: dict,
    resource_constraints: dict
) -> dict:
    """建议处理优化
    
    Args:
        processing_history: 处理历史记录
        performance_metrics: 性能指标
        resource_constraints: 资源约束
    
    Returns:
        dict: 优化建议
    """
    try:
        optimization_suggestions = {
            "parameter_adjustments": {},
            "workflow_optimizations": [],
            "resource_optimizations": [],
            "quality_improvements": []
        }
        
        # 分析历史性能
        if processing_history:
            avg_processing_time = sum(h.get("processing_time", 0) for h in processing_history) / len(processing_history)
            avg_quality_score = sum(h.get("quality_score", 0) for h in processing_history) / len(processing_history)
            
            # 速度优化建议
            if avg_processing_time > 60:  # 如果平均处理时间超过1分钟
                optimization_suggestions["workflow_optimizations"].extend([
                    "建议启用并行处理以提高速度",
                    "建议减少处理深度以平衡速度和质量",
                    "建议使用缓存机制避免重复计算"
                ])
                
                optimization_suggestions["parameter_adjustments"]["parallel_processing"] = True
                optimization_suggestions["parameter_adjustments"]["batch_size"] = "increased"
            
            # 质量优化建议
            if avg_quality_score < 0.7:
                optimization_suggestions["quality_improvements"].extend([
                    "建议增加处理深度以提高质量",
                    "建议使用更复杂的算法模型",
                    "建议增加迭代次数"
                ])
                
                optimization_suggestions["parameter_adjustments"]["quality_threshold"] = 0.8
                optimization_suggestions["parameter_adjustments"]["processing_depth"] = "increased"
        
        # 资源约束优化
        memory_limit = resource_constraints.get("memory_limit", "unlimited")
        time_limit = resource_constraints.get("time_limit", "unlimited")
        
        if memory_limit != "unlimited":
            optimization_suggestions["resource_optimizations"].extend([
                "建议使用流式处理减少内存占用",
                "建议分批处理大型数据集",
                "建议清理中间结果释放内存"
            ])
        
        if time_limit != "unlimited":
            optimization_suggestions["resource_optimizations"].extend([
                "建议优先处理关键模块",
                "建议使用快速算法替代精确算法",
                "建议设置处理超时机制"
            ])
        
        # 模块特定优化
        module_performance = performance_metrics.get("module_performance", {})
        
        for module, metrics in module_performance.items():
            if metrics.get("avg_time", 0) > 30:
                optimization_suggestions["parameter_adjustments"][f"{module}_optimization"] = {
                    "reduce_complexity": True,
                    "enable_caching": True
                }
            
            if metrics.get("success_rate", 1.0) < 0.8:
                optimization_suggestions["parameter_adjustments"][f"{module}_reliability"] = {
                    "increase_retries": True,
                    "add_fallback_methods": True
                }
        
        return optimization_suggestions
        
    except Exception as e:
        logger.error(f"Error suggesting processing optimizations: {e}")
        return {
            "error": str(e),
            "default_suggestions": [
                "建议检查系统配置",
                "建议监控资源使用情况",
                "建议记录详细的性能指标"
            ]
        }

if __name__ == "__main__":
    # 运行MCP服务器
    mcp.run()
