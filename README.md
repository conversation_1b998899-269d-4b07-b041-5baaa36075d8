# LLMxMapReduce-V2 代码库文档摘要

## 项目概述

LLMxMapReduce-V2 是一个基于大语言模型的长文本生成框架，专门用于从大量参考文献中生成高质量的综述文章。该框架采用了类似MapReduce的分布式处理思想，结合了深度学习中的卷积、残差等概念（概念借用，非张量操作），实现了骨架引导的摘要生成、熵驱动的结构优化和拓扑感知的内容生成。

## 核心架构

项目采用三阶段处理流程：
1. **编码阶段 (Encode)**: 数据加载和预处理
2. **隐藏层处理 (Hidden)**: 骨架生成、摘要处理、卷积优化
3. **解码阶段 (Decode)**: 内容生成和输出

## 主要文件摘要

### 1. 项目入口文件

#### `main.py`
**功能**: 项目的简单入口点
**主要函数**:
- `main()`: 打印欢迎信息的占位函数

#### `src/start_pipeline.py`
**功能**: 真正的项目启动文件，负责整个处理流水线的初始化和执行
**主要类**:
- `EntirePipeline`: 整体流水线类，继承自Pipeline
**主要函数**:
- `__init__()`: 初始化编码、隐藏层、解码三个子流水线
- `process_with_mcp()`: 使用MCP协调器处理调研数据
- `start_pipeline()`: 启动流水线并注册监控
- `main()`: 主函数，支持主题搜索或文件输入两种模式

**特色功能**:
- 支持三种处理模式：传统隐藏层、智能隐藏层、MCP协调器模式
- 集成了自动网络搜索和爬虫功能
- 提供实时监控和分析功能

### 2. 数据结构模块 (`src/data_structure/`)

#### `treenode.py`
**功能**: 树形数据结构的基础类
**主要类**:
- `TreeNode`: 树节点基类
**主要方法**:
- `add_son()`: 添加子节点
- `delete_son()`: 删除子节点
- `update_section()`: 更新节点的前序和子节点信息
- `is_leaf`: 判断是否为叶子节点
- `all_section`: 获取所有子节点
- `number_index`: 获取节点的数字索引

#### `skeleton.py`
**功能**: 骨架（文章结构模板）的数据结构和处理逻辑
**主要类**:
- `SkeletonNode`: 骨架节点，继承自TreeNode
- `Skeleton`: 骨架管理类
**主要方法**:
- `get_skeleton()`: 获取骨架内容，支持多种格式选项
- `parse_raw_skeleton()`: 解析原始骨架文本
- `parse_description()`: 解析节点描述，分离construction和analysis部分
- `update()`: 更新骨架内容
- `all_skeleton()`: 获取完整骨架文本

**核心概念**:
- **Construction**: 指定从原文中提取哪些内容的指令
- **Analysis**: 指导如何使用摘要生成该部分内容的策略

#### `digest.py`
**功能**: 摘要数据结构和处理逻辑
**主要类**:
- `DigestNode`: 摘要节点，继承自TreeNode
- `Digest`: 摘要管理类
**主要方法**:
- `content()`: 获取摘要内容
- `get_digest_from_str()`: 从字符串解析摘要
- `pre_proc_paper()`: 预处理论文文本
- `parse_digest()`: 解析摘要并与骨架对应
- `all_content()`: 获取所有摘要内容

#### `content.py`
**功能**: 最终生成内容的数据结构
**主要类**:
- `ContentNode`: 内容节点，继承自TreeNode
- `Content`: 内容管理类
**主要方法**:
- `init_content()`: 初始化内容结构
- `add_content()`: 添加生成的内容
- `all_content()`: 获取完整内容

#### `survey.py`
**功能**: 调研项目的顶层数据结构
**主要类**:
- `Survey`: 调研管理类
**主要属性**:
- `title`: 调研标题
- `papers`: 参考论文字典
- `skeleton`: 骨架对象
- `digests`: 摘要字典
- `content`: 内容对象
- `block_cycle_count`: 处理轮次计数
**主要方法**:
- `init_content()`: 初始化内容生成
- `update_outline()`: 更新骨架
- `to_dict()`: 转换为字典格式用于保存

#### `multi_key_dict.py`
**功能**: 支持多键索引的字典数据结构
**主要类**:
- `MultiKeyDict`: 多键字典类，支持用frozenset作为键

#### `feedback.py`
**功能**: 反馈数据结构，用于卷积层的优化过程

### 3. 编码模块 (`src/encode/`)

#### `encode_pipeline.py`
**功能**: 数据加载和预处理流水线
**主要类**:
- `EncodePipeline`: 编码流水线，继承自Sequential
**主要方法**:
- `load_survey()`: 从JSONL文件加载调研数据
- `unpack()`: 解包调研对象

**处理流程**:
1. 从输入文件逐行读取JSON数据
2. 创建Survey对象
3. 过滤无效数据（无论文的调研）
4. 支持数据数量限制

### 4. 隐藏层处理模块 (`src/hidden/`)

#### `hidden_pipeline.py`
**功能**: 传统隐藏层处理流水线
**主要功能**: 实现骨架生成、分组、摘要处理、卷积优化等核心算法

#### `intelligent_hidden_pipeline.py`
**功能**: 基于LLM提示词的智能隐藏层流水线
**主要类**:
- `IntelligentModule`: 智能模块基类
- `IntelligentGroupModule`: 智能分组模块
- `IntelligentSkeletonModule`: 智能骨架模块
- `IntelligentDigestModule`: 智能摘要模块
- `IntelligentRefinementModule`: 智能精化模块
- `IntelligentHiddenPipeline`: 智能隐藏层流水线

**核心特性**:
- 使用MCP客户端进行智能处理
- 支持异步处理模式
- 基于LLM的自适应优化策略

#### `mcp_pipeline_coordinator.py`
**功能**: MCP流水线协调器，实现完全基于自然语言指令的处理
**主要类**:
- `MCPPipelineCoordinator`: MCP流水线协调器
**主要方法**:
- `process_survey_with_natural_language()`: 使用自然语言指令处理调研
- `_analyze_user_instructions()`: 分析用户指令并制定处理计划
- `_execute_intelligent_workflow()`: 执行智能工作流程
- `_quality_assurance_and_optimization()`: 质量保证和最终优化
- `batch_process_surveys_with_nl()`: 批量处理调研

**处理步骤**:
1. 分析用户指令，制定处理计划
2. 执行智能工作流程（结构初始化、论文分组、摘要处理、卷积优化、神经元优化）
3. 质量评估和最终优化
4. 记录处理历史和性能指标

### 5. 基础模块 (`src/hidden/basic_modules/`)

#### `skeleton_init_module.py`
**功能**: 骨架初始化模块
**主要类**:
- `SkeletonInitModule`: 骨架初始化模块
- `SingleSkeletonNeuron`: 单个骨架生成神经元
- `ConcatSkeletonNeuron`: 骨架合并神经元

**处理流程**:
1. 将摘要分批处理
2. 为每批摘要生成单独的骨架
3. 合并多个骨架为最终骨架

#### `group_module.py`
**功能**: 论文分组模块，根据语义相似性对论文进行分组

#### `digest_module.py`
**功能**: 摘要生成模块，基于骨架指导生成结构化摘要

### 6. 卷积模块 (`src/hidden/convolution_block/`)

#### `convolution_module.py`
**功能**: 实现"卷积"优化算法（概念借用，实际为结构聚合）
**主要类**:
- `ConvolutionLayerModule`: 卷积层模块
- `ConvolutionModule`: 卷积模块

**核心算法**:
- `_sample_suggestions()`: 采样建议组合
- `_prune_top_k()`: 选择最优结果
- `_convolution_forward()`: 卷积前向传播

**"卷积"概念说明**:
- 非传统CNN卷积，而是多轮反馈聚合
- 使用信息熵评分选择最优修改方案
- 实现类似残差连接的结构优化

#### `neurons.py`
**功能**: 神经元级别的处理单元
**主要类**:
- `ModifyOutlineNeuron`: 骨架修改神经元
- `EvalOutlineNeuron`: 骨架评估神经元
- `ConvolutionKernelNeuron`: 卷积核神经元

#### `refine_module.py`
**功能**: Best-of-N自我优化模块，实现多版本并行生成和择优

### 7. 解码模块 (`src/decode/`)

#### `decode_pipeline.py`
**功能**: 内容生成和输出流水线
**主要类**:
- `DecodePipeline`: 解码流水线，继承自Sequential

**处理流程**:
1. 注册调研并初始化内容结构
2. 解包内容节点进行并行处理
3. 使用Orchestra模块生成具体内容
4. 组装完整调研内容
5. 处理引用格式转换
6. 生成图表和可视化
7. 保存最终结果

**主要方法**:
- `register_survey()`: 注册调研并初始化内容生成
- `unpack_survey()`: 解包内容节点
- `assemble_survey()`: 组装完整调研
- `change_bibkey_to_index()`: 将引用键转换为数字索引
- `save_survey()`: 保存调研结果

#### `orchestra_module.py`
**功能**: 内容编排模块，实现拓扑感知的内容生成
- 叶子节点使用`gleaf()`生成：专注于比较多个摘要内容
- 非叶子节点使用`gnon_leaf()`生成：聚合子节内容

#### `figure_module.py`
**功能**: 图表生成模块，为调研生成相关的图表和可视化内容

### 8. 工具模块

#### `src/utils/process_str.py`
**功能**: 字符串处理工具集
**主要函数**:
- `parse_md_content()`: 解析Markdown内容
- `get_section_title()`: 提取章节标题和层级
- `str2list()` / `list2str()`: 字符串和列表转换
- `remove_illegal_bibkeys()`: 移除非法引用键
- `proc_title_to_str()`: 标题标准化处理

**特色功能**:
- 支持数学公式保护（处理过程中不破坏LaTeX公式）
- 智能引用键匹配和修正
- 相似度匹配的引用键替换

#### `src/logger.py`
**功能**: 日志配置和管理

#### `src/exceptions.py`
**功能**: 自定义异常类定义
- `BibkeyNotFoundError`: 引用键未找到错误
- `StructureNotCorrespondingError`: 结构不对应错误
- `MdNotFoundError`: Markdown内容未找到错误

### 9. 请求模块 (`request/`)

#### `wrapper.py`
**功能**: LLM请求的统一包装器
**主要类**:
- `RequestWrapper`: 请求包装器类

**支持的推理类型**:
- OpenAI API兼容接口
- Google API
- 本地模型接口

**特色功能**:
- 连接池管理和并发控制
- API调用统计和token使用追踪
- 统一的消息格式处理

#### `openai.py` / `google.py` / `local.py`
**功能**: 不同LLM服务提供商的具体实现

### 10. 提示词模块 (`src/prompts/`)

#### `__init__.py`
**功能**: 提示词管理和导出
**主要提示词类别**:
- 分组相关：`GROUP_PROMPT`
- 骨架相关：`INIT_OUTLINE_PROMPT`, `CONCAT_OUTLINE_PROMPT`, `MODIFY_OUTLINE_PROMPT`
- 摘要相关：`SINGLE_DIGEST_PROMPT`, `DIGEST_BASE_PROMPT`
- 内容生成：`ORCHESTRA_PROMPT`, `SUMMARY_PROMPT`
- 搜索相关：`QUERY_EXPAND_PROMPT`, `LLM_CHECK_PROMPT`

#### `base.py`
**功能**: 提示词协议和管理器基类

#### `prompts_en.py` / `prompts_zh.py`
**功能**: 英文和中文提示词实现

#### `intelligent_prompts.py`
**功能**: 智能提示词，用于LLM驱动的处理流程

### 11. 搜索和爬虫模块

#### `src/LLM_search.py`
**功能**: 基于LLM的智能搜索模块
**主要功能**:
- 查询扩展和优化
- 批量网络搜索
- 搜索结果过滤和排序

#### `src/async_crawl.py`
**功能**: 异步网页爬虫模块
**主要功能**:
- 并发网页内容抓取
- 内容质量评估和过滤
- 结构化数据提取

### 12. MCP服务器和客户端

#### `src/hidden/mcp_server/`
**功能**: MCP (Model Context Protocol) 服务器实现
**主要服务器**:
- `basic_modules_processor_server.py`: 基础模块处理服务器
- `convolution_processor_server.py`: 卷积处理服务器
- `digest_processor_server.py`: 摘要处理服务器
- `skeleton_processor_server.py`: 骨架处理服务器
- `pipeline_orchestrator_server.py`: 流水线编排服务器

#### `src/hidden/mcp_client/`
**功能**: MCP客户端实现
- `intelligent_pipeline_client.py`: 智能流水线客户端

### 13. 配置文件

#### `config/model_config.json`
**功能**: 模型配置文件，定义各个模块使用的LLM模型和参数

#### `config/mcp_config.json`
**功能**: MCP协议配置文件，定义服务器连接和工具配置

### 14. 评估模块 (`evaluation/`)

#### `all_eval.py`
**功能**: 综合评估脚本

#### `args.py`
**功能**: 评估参数配置

## 核心算法流程

### 1. 骨架引导的摘要生成
1. 基于主题和文献聚类自动生成骨架结构
2. 骨架包含Construction（提取指令）和Analysis（使用策略）
3. 根据骨架指导生成结构化摘要

### 2. 熵驱动卷积优化
1. 多轮反馈采样与聚合
2. 使用信息熵评分选择最优骨架修改方案
3. 多轮迭代融合不同修改建议（"卷积"操作）
4. Best-of-N自我优化选择最佳版本

### 3. 拓扑感知内容生成
1. 叶子节点：比较多个摘要内容，生成详细描述
2. 非叶子节点：聚合子节点内容，实现章节总结
3. 保持树形结构的逻辑一致性

## 使用方式

### 命令行启动
```bash
# 基于主题自动搜索和处理
python src/start_pipeline.py --topic "机器学习" --description "深度学习综述"

# 基于输入文件处理
python src/start_pipeline.py --input_file "survey_data.jsonl"
```

### 配置选项
- `--use_intelligent_pipeline`: 使用智能隐藏层流水线
- `--use_mcp_pipeline`: 使用MCP协调器模式
- `--parallel_num`: 并行处理数量
- `--block_count`: 处理轮次
- `--conv_layer`: 卷积层数
- `--top_k`: Top-K选择参数

## 技术特色

1. **模块化设计**: 清晰的三阶段流水线架构
2. **异步处理**: 支持大规模并发处理
3. **智能优化**: 基于LLM的自适应优化策略
4. **质量控制**: 多层次的质量评估和优化机制
5. **可扩展性**: 支持多种LLM后端和处理模式
6. **监控分析**: 实时性能监控和处理分析

## 依赖关系

- **异步框架**: gevent用于并发处理
- **LLM接口**: 支持OpenAI、Google、本地模型
- **数据处理**: json、re等标准库
- **数值计算**: numpy用于评分和优化算法
- **重试机制**: tenacity用于错误恢复
- **MCP协议**: 用于模块间通信和协调

这个框架实现了从原始文献到高质量综述文章的端到端自动化生成，特别适用于需要处理大量参考文献的学术写作场景。