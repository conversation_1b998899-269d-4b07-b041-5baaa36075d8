#!/usr/bin/env python3
"""
LLM搜索MCP服务器实现
提供基于LLM的智能搜索功能，包括查询生成、网络搜索和结果分析
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional, Sequence

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    TextContent,
    Tool,
)

# 导入项目中的LLM搜索模块
try:
    from ..LLM_search import LLM_search
    from src.exceptions import AnalyseError
except ImportError:
    # 如果在新版本目录结构中
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'LLMxMapReduce_V2', 'src'))
    from LLM_search import LLM_search
    from exceptions import AnalyseError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LLMSearchMCPServer:
    """LLM搜索MCP服务器"""
    
    def __init__(self):
        self.server = Server("llm-search-server")
        self.llm_search_instances = {}  # 缓存不同配置的LLM搜索实例
        
        # 注册工具
        self._register_tools()
        
    def _register_tools(self):
        """注册MCP工具"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """列出所有可用工具"""
            return [
                Tool(
                    name="generate_search_queries",
                    description="Generate optimized search queries for a given topic using LLM",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "topic": {
                                "type": "string",
                                "description": "The research topic to generate queries for"
                            },
                            "description": {
                                "type": "string",
                                "description": "Optional description or context for the topic"
                            },
                            "model": {
                                "type": "string",
                                "description": "LLM model to use for query generation",
                                "default": "gemini-2.0-flash-thinking-exp-01-21"
                            }
                        },
                        "required": ["topic"]
                    }
                ),
                Tool(
                    name="web_search",
                    description="Perform web search using provided queries",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "queries": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "List of search queries to execute"
                            },
                            "topic": {
                                "type": "string",
                                "description": "Main topic for relevance filtering"
                            },
                            "top_n": {
                                "type": "integer",
                                "description": "Number of most relevant URLs to return",
                                "default": 20
                            },
                            "engine": {
                                "type": "string",
                                "description": "Search engine to use (google, bing, baidu)",
                                "default": "google"
                            }
                        },
                        "required": ["queries", "topic"]
                    }
                ),
                Tool(
                    name="analyze_search_results",
                    description="Analyze and filter search results for relevance",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "urls": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "List of URLs to analyze"
                            },
                            "topic": {
                                "type": "string",
                                "description": "Topic to analyze relevance against"
                            },
                            "max_results": {
                                "type": "integer",
                                "description": "Maximum number of results to return",
                                "default": 10
                            }
                        },
                        "required": ["urls", "topic"]
                    }
                ),
                Tool(
                    name="full_search_pipeline",
                    description="Execute complete search pipeline: query generation -> web search -> analysis",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "topic": {
                                "type": "string",
                                "description": "Research topic"
                            },
                            "description": {
                                "type": "string",
                                "description": "Optional topic description"
                            },
                            "top_n": {
                                "type": "integer",
                                "description": "Number of final results to return",
                                "default": 10
                            },
                            "model": {
                                "type": "string",
                                "description": "LLM model for query generation",
                                "default": "gemini-2.0-flash-thinking-exp-01-21"
                            },
                            "engine": {
                                "type": "string",
                                "description": "Search engine to use",
                                "default": "google"
                            }
                        },
                        "required": ["topic"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """处理工具调用"""
            try:
                if name == "generate_search_queries":
                    return await self._generate_search_queries(arguments)
                elif name == "web_search":
                    return await self._web_search(arguments)
                elif name == "analyze_search_results":
                    return await self._analyze_search_results(arguments)
                elif name == "full_search_pipeline":
                    return await self._full_search_pipeline(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
                    
            except Exception as e:
                logger.error(f"Error in tool {name}: {e}")
                return [TextContent(
                    type="text",
                    text=f"Error executing {name}: {str(e)}"
                )]
    
    def _get_llm_search_instance(self, model: str = "gemini-2.0-flash-thinking-exp-01-21", 
                                engine: str = "google") -> LLM_search:
        """获取或创建LLM搜索实例"""
        key = f"{model}_{engine}"
        if key not in self.llm_search_instances:
            self.llm_search_instances[key] = LLM_search(
                model=model,
                infer_type="OpenAI",
                engine=engine,
                each_query_result=10
            )
        return self.llm_search_instances[key]
    
    async def _generate_search_queries(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """生成搜索查询"""
        topic = arguments["topic"]
        description = arguments.get("description", "")
        model = arguments.get("model", "gemini-2.0-flash-thinking-exp-01-21")
        
        logger.info(f"Generating search queries for topic: {topic}")
        
        try:
            llm_search = self._get_llm_search_instance(model=model)
            queries = llm_search.get_queries(topic=topic, description=description)
            
            result = {
                "topic": topic,
                "description": description,
                "model": model,
                "queries": queries,
                "query_count": len(queries)
            }
            
            return [TextContent(
                type="text",
                text=json.dumps(result, ensure_ascii=False, indent=2)
            )]
            
        except Exception as e:
            logger.error(f"Error generating queries: {e}")
            raise AnalyseError(f"Failed to generate search queries: {e}")
    
    async def _web_search(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """执行网络搜索"""
        queries = arguments["queries"]
        topic = arguments["topic"]
        top_n = arguments.get("top_n", 20)
        engine = arguments.get("engine", "google")
        
        logger.info(f"Performing web search for {len(queries)} queries")
        
        try:
            llm_search = self._get_llm_search_instance(engine=engine)
            urls = llm_search.batch_web_search(queries=queries, topic=topic, top_n=top_n)
            
            result = {
                "topic": topic,
                "queries": queries,
                "engine": engine,
                "urls": urls,
                "url_count": len(urls),
                "top_n": top_n
            }
            
            return [TextContent(
                type="text",
                text=json.dumps(result, ensure_ascii=False, indent=2)
            )]
            
        except Exception as e:
            logger.error(f"Error in web search: {e}")
            raise AnalyseError(f"Failed to perform web search: {e}")
    
    async def _analyze_search_results(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """分析搜索结果"""
        urls = arguments["urls"]
        topic = arguments["topic"]
        max_results = arguments.get("max_results", 10)
        
        logger.info(f"Analyzing {len(urls)} URLs for relevance to topic: {topic}")
        
        try:
            # 这里可以实现更复杂的分析逻辑
            # 目前简单返回前max_results个URL
            analyzed_urls = urls[:max_results]
            
            result = {
                "topic": topic,
                "original_count": len(urls),
                "analyzed_urls": analyzed_urls,
                "final_count": len(analyzed_urls),
                "analysis_method": "simple_truncation"
            }
            
            return [TextContent(
                type="text",
                text=json.dumps(result, ensure_ascii=False, indent=2)
            )]
            
        except Exception as e:
            logger.error(f"Error analyzing search results: {e}")
            raise AnalyseError(f"Failed to analyze search results: {e}")
    
    async def _full_search_pipeline(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """执行完整搜索流水线"""
        topic = arguments["topic"]
        description = arguments.get("description", "")
        top_n = arguments.get("top_n", 10)
        model = arguments.get("model", "gemini-2.0-flash-thinking-exp-01-21")
        engine = arguments.get("engine", "google")
        
        logger.info(f"Executing full search pipeline for topic: {topic}")
        
        try:
            llm_search = self._get_llm_search_instance(model=model, engine=engine)
            
            # 步骤1: 生成查询
            queries = llm_search.get_queries(topic=topic, description=description)
            
            # 步骤2: 执行搜索
            urls = llm_search.batch_web_search(queries=queries, topic=topic, top_n=top_n)
            
            # 步骤3: 分析结果（这里简化处理）
            final_urls = urls[:top_n]
            
            result = {
                "topic": topic,
                "description": description,
                "model": model,
                "engine": engine,
                "pipeline_steps": {
                    "1_query_generation": {
                        "queries": queries,
                        "query_count": len(queries)
                    },
                    "2_web_search": {
                        "search_results": len(urls),
                        "engine": engine
                    },
                    "3_analysis": {
                        "final_urls": final_urls,
                        "final_count": len(final_urls)
                    }
                },
                "final_results": final_urls
            }
            
            return [TextContent(
                type="text",
                text=json.dumps(result, ensure_ascii=False, indent=2)
            )]
            
        except Exception as e:
            logger.error(f"Error in full search pipeline: {e}")
            raise AnalyseError(f"Failed to execute search pipeline: {e}")

async def main():
    """启动MCP服务器"""
    logger.info("Starting LLM Search MCP Server...")
    
    # 创建服务器实例
    mcp_server = LLMSearchMCPServer()
    
    # 使用stdio传输启动服务器
    async with stdio_server() as (read_stream, write_stream):
        await mcp_server.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="llm-search-server",
                server_version="1.0.0",
                capabilities=mcp_server.server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())
