#!/usr/bin/env python3
"""
Encode Toolkit - 数据编码和预处理工具包
负责将输入数据转换为 pipeline 可处理的格式
"""

import json
import logging
from typing import List, Dict, Any, Optional, Iterator
from pathlib import Path

from tenacity import retry, stop_after_attempt, after_log, retry_if_exception_type
from camel.toolkits import BaseToolkit, FunctionTool

from src.data_structure import Survey
from src.exceptions import (
    BibkeyNotFoundError,
    StructureNotCorrespondingError,
    MdNotFoundError,
)

logger = logging.getLogger(__name__)


class EncodePipelineToolkit(BaseToolkit):
    """编码流水线工具包，负责数据的加载、验证和预处理"""

    def __init__(
        self,
        data_limit: Optional[int] = None,
        validation_enabled: bool = True,
        timeout: Optional[float] = None
    ):
        """初始化编码工具包
        
        Args:
            data_limit: 处理数据的最大数量限制
            validation_enabled: 是否启用数据验证
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.data_limit = data_limit
        self.validation_enabled = validation_enabled
        self.processed_count = 0
        logger.info(f"EncodePipelineToolkit initialized with data_limit: {data_limit}")

    @retry(
        stop=stop_after_attempt(3),
        after=after_log(logger, logging.WARNING),
        retry=retry_if_exception_type((FileNotFoundError, json.JSONDecodeError))
    )
    async def load_surveys_from_file(
        self,
        file_path: str,
        format_type: str = "jsonl"
    ) -> List[Dict[str, Any]]:
        """从文件加载调研数据
        
        Args:
            file_path: 输入文件路径
            format_type: 文件格式类型 ("jsonl", "json")
            
        Returns:
            List[Dict[str, Any]]: 调研数据列表
        """
        surveys = []
        file_path_obj = Path(file_path)
        
        if not file_path_obj.exists():
            raise FileNotFoundError(f"Input file not found: {file_path}")
            
        logger.info(f"Loading surveys from: {file_path}")
        
        try:
            if format_type == "jsonl":
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        if self.data_limit and len(surveys) >= self.data_limit:
                            logger.info(f"Reached data limit: {self.data_limit}")
                            break
                            
                        line = line.strip()
                        if not line:
                            continue
                            
                        try:
                            survey_data = json.loads(line)
                            surveys.append(survey_data)
                        except json.JSONDecodeError as e:
                            logger.warning(f"Invalid JSON at line {line_num}: {e}")
                            continue
                            
            elif format_type == "json":
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        surveys = data[:self.data_limit] if self.data_limit else data
                    else:
                        surveys = [data]
            else:
                raise ValueError(f"Unsupported format type: {format_type}")
                
            logger.info(f"Successfully loaded {len(surveys)} surveys")
            return surveys
            
        except Exception as e:
            logger.error(f"Error loading surveys from {file_path}: {e}")
            raise

    async def validate_survey_data(
        self,
        survey_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证调研数据的完整性和格式
        
        Args:
            survey_data: 原始调研数据
            
        Returns:
            Dict[str, Any]: 验证结果和清理后的数据
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "cleaned_data": survey_data.copy()
        }
        
        try:
            # 基本字段检查
            required_fields = ["title", "papers"]
            for field in required_fields:
                if field not in survey_data:
                    validation_result["errors"].append(f"Missing required field: {field}")
                    validation_result["valid"] = False
            
            # 检查论文数据
            if "papers" in survey_data:
                papers = survey_data["papers"]
                if isinstance(papers, dict):
                    if len(papers) == 0:
                        validation_result["warnings"].append("No papers found in survey")
                    
                    # 检查每篇论文的完整性
                    valid_papers = {}
                    for paper_id, paper_data in papers.items():
                        if isinstance(paper_data, dict) and "content" in paper_data:
                            valid_papers[paper_id] = paper_data
                        else:
                            validation_result["warnings"].append(f"Invalid paper data for ID: {paper_id}")
                    
                    validation_result["cleaned_data"]["papers"] = valid_papers
                    
                elif isinstance(papers, list):
                    # 转换列表格式到字典格式
                    paper_dict = {}
                    for i, paper in enumerate(papers):
                        if isinstance(paper, dict):
                            paper_id = paper.get("id", f"paper_{i}")
                            paper_dict[paper_id] = paper
                    validation_result["cleaned_data"]["papers"] = paper_dict
                    validation_result["warnings"].append("Converted papers from list to dict format")
            
            # 数据质量检查
            cleaned_papers = validation_result["cleaned_data"].get("papers", {})
            total_content_length = sum(
                len(paper.get("content", "")) for paper in cleaned_papers.values()
            )
            
            if total_content_length < 100:
                validation_result["warnings"].append("Survey content seems too short")
            elif total_content_length > 1000000:  # 1MB
                validation_result["warnings"].append("Survey content is very large")
            
            logger.debug(f"Validation completed for survey: {survey_data.get('title', 'Unknown')}")
            
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"Validation error: {str(e)}")
            logger.error(f"Error during survey validation: {e}")
        
        return validation_result

    async def create_survey_object(
        self,
        survey_data: Dict[str, Any]
    ) -> Survey:
        """创建 Survey 对象
        
        Args:
            survey_data: 验证后的调研数据
            
        Returns:
            Survey: 调研对象
        """
        try:
            survey = Survey(survey_data)
            logger.debug(f"Created Survey object: {survey.title}")
            return survey
        except Exception as e:
            logger.error(f"Error creating Survey object: {e}")
            raise

    async def preprocess_survey(
        self,
        survey: Survey,
        preprocessing_options: Dict[str, Any] = None
    ) -> Survey:
        """预处理调研数据
        
        Args:
            survey: 输入的调研对象
            preprocessing_options: 预处理选项
            
        Returns:
            Survey: 预处理后的调研对象
        """
        options = preprocessing_options or {}
        
        try:
            # 文本清理
            if options.get("clean_text", True):
                for paper_id, paper in survey.papers.items():
                    if "content" in paper and isinstance(paper["content"], str):
                        # 基本文本清理
                        content = paper["content"]
                        content = content.strip()
                        content = " ".join(content.split())  # 规范化空白字符
                        paper["content"] = content
            
            # 元数据增强
            if options.get("add_metadata", True):
                if not hasattr(survey, "metadata"):
                    survey.metadata = {}
                
                survey.metadata.update({
                    "paper_count": len(survey.papers),
                    "total_content_length": sum(
                        len(paper.get("content", "")) for paper in survey.papers.values()
                    ),
                    "processing_timestamp": None,  # 将在实际处理时设置
                    "preprocessed": True
                })
            
            # 数据结构标准化
            if options.get("standardize_structure", True):
                # 确保必要的属性存在
                if not hasattr(survey, "digests"):
                    survey.digests = {}
                if not hasattr(survey, "skeleton"):
                    survey.skeleton = None
                if not hasattr(survey, "block_cycle_count"):
                    survey.block_cycle_count = 0
            
            logger.info(f"Preprocessed survey: {survey.title}")
            return survey
            
        except Exception as e:
            logger.error(f"Error during survey preprocessing: {e}")
            raise

    async def batch_process_surveys(
        self,
        file_path: str,
        format_type: str = "jsonl",
        preprocessing_options: Dict[str, Any] = None
    ) -> Iterator[Survey]:
        """批量处理调研数据
        
        Args:
            file_path: 输入文件路径
            format_type: 文件格式类型
            preprocessing_options: 预处理选项
            
        Yields:
            Survey: 处理后的调研对象
        """
        logger.info(f"Starting batch processing from: {file_path}")
        
        try:
            # 加载原始数据
            raw_surveys = await self.load_surveys_from_file(file_path, format_type)
            
            for i, survey_data in enumerate(raw_surveys):
                try:
                    # 验证数据
                    if self.validation_enabled:
                        validation_result = await self.validate_survey_data(survey_data)
                        
                        if not validation_result["valid"]:
                            logger.error(f"Survey {i} validation failed: {validation_result['errors']}")
                            continue
                        
                        if validation_result["warnings"]:
                            logger.warning(f"Survey {i} warnings: {validation_result['warnings']}")
                        
                        survey_data = validation_result["cleaned_data"]
                    
                    # 创建 Survey 对象
                    survey = await self.create_survey_object(survey_data)
                    
                    # 预处理
                    survey = await self.preprocess_survey(survey, preprocessing_options)
                    
                    # 更新计数器
                    self.processed_count += 1
                    
                    yield survey
                    
                except Exception as e:
                    logger.error(f"Error processing survey {i}: {e}")
                    continue
            
            logger.info(f"Batch processing completed. Processed {self.processed_count} surveys")
            
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            raise

    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息
        
        Returns:
            Dict[str, Any]: 处理统计信息
        """
        return {
            "processed_count": self.processed_count,
            "data_limit": self.data_limit,
            "validation_enabled": self.validation_enabled
        }


class EncodeManagerToolkit(BaseToolkit):
    """编码管理工具包，协调整个编码流程"""

    def __init__(self, timeout: Optional[float] = None):
        """初始化编码管理工具包
        
        Args:
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        logger.info("EncodeManagerToolkit initialized")

    async def process_input_data(
        self,
        input_file: str,
        config: Dict[str, Any] = None,
        encode_pipeline_url: str = None
    ) -> List[Dict[str, Any]]:
        """处理输入数据并返回编码后的调研列表
        
        Args:
            input_file: 输入文件路径
            config: 处理配置
            encode_pipeline_url: EncodePipeline 服务URL
            
        Returns:
            List[Dict[str, Any]]: 编码后的调研数据列表
        """
        config = config or {}
        
        # 使用本地处理逻辑
        encode_toolkit = EncodePipelineToolkit(
            data_limit=config.get("data_num"),
            validation_enabled=config.get("validation_enabled", True)
        )
        
        processed_surveys = []
        
        async for survey in encode_toolkit.batch_process_surveys(
            input_file,
            format_type=config.get("format_type", "jsonl"),
            preprocessing_options=config.get("preprocessing_options", {})
        ):
            processed_surveys.append(survey.to_dict())
        
        logger.info(f"Encoded {len(processed_surveys)} surveys from {input_file}")
        return processed_surveys
