#!/usr/bin/env python3
"""
正确MCP架构测试脚本
测试职责分离后的MCP实现
"""

import asyncio
import json
import logging
import os
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_mcp_client_only():
    """测试纯粹的MCP客户端功能"""
    print("=" * 60)
    print("测试1: 纯粹MCP客户端测试")
    print("=" * 60)
    
    try:
        from src.search.mcp_client import MCPClient, MCPClientManager
        print("✅ 成功导入纯粹的MCP客户端")
        
        # 测试客户端配置
        server_config = {
            "command": "python",
            "args": ["-m", "src.search.llm_search_mcp_server"],
            "env": {
                "PYTHONPATH": ".",
                "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", ""),
                "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY", ""),
                "SERP_API_KEY": os.getenv("SERP_API_KEY", ""),
                "BING_SEARCH_V7_SUBSCRIPTION_KEY": os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY", "")
            }
        }
        
        async def test_client():
            async with MCPClientManager(server_config) as client:
                # 测试工具列表
                tools = await client.list_tools()
                print(f"✅ 发现 {len(tools)} 个工具:")
                for tool in tools:
                    print(f"  - {tool.name}: {tool.description}")
                
                # 测试原始工具调用
                if os.getenv("OPENAI_API_KEY") or os.getenv("GOOGLE_API_KEY"):
                    result = await client.call_tool_and_parse_json(
                        "generate_search_queries",
                        {"topic": "test topic", "description": "test description"}
                    )
                    print(f"✅ 工具调用成功，返回 {len(result.get('queries', []))} 个查询")
                else:
                    print("⚠️  跳过工具调用测试（无LLM API密钥）")
        
        # 运行异步测试
        asyncio.run(test_client())
        return True
        
    except Exception as e:
        print(f"❌ MCP客户端测试失败: {e}")
        return False

def test_llm_host_interface():
    """测试LLM主机接口兼容性"""
    print("\n" + "=" * 60)
    print("测试2: LLM主机接口兼容性测试")
    print("=" * 60)
    
    try:
        from src.search.llm_search_host_v2 import LLM_search
        print("✅ 成功导入正确架构的LLM_search")
        
        # 测试初始化（与原有接口相同）
        retriever = LLM_search(
            model='gemini-2.0-flash-thinking-exp-01-21',
            infer_type="OpenAI",
            engine='google',
            each_query_result=10
        )
        print("✅ 成功初始化LLM_search实例")
        
        # 检查方法是否存在
        required_methods = ['get_queries', 'web_search', 'batch_web_search', 'snippet_filter']
        for method in required_methods:
            if hasattr(retriever, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ LLM主机接口测试失败: {e}")
        return False

def test_architecture_separation():
    """测试架构分离"""
    print("\n" + "=" * 60)
    print("测试3: 架构分离验证")
    print("=" * 60)
    
    try:
        # 验证MCP客户端不包含业务逻辑
        from src.search.mcp_client import MCPClient
        client_methods = [method for method in dir(MCPClient) if not method.startswith('_')]
        expected_client_methods = ['connect', 'disconnect', 'list_tools', 'call_tool', 'call_tool_and_parse_json']
        
        print("MCP客户端方法:")
        for method in client_methods:
            print(f"  - {method}")
        
        # 检查是否只包含通信相关方法
        business_methods = ['get_queries', 'web_search', 'batch_web_search', 'snippet_filter']
        has_business_logic = any(method in client_methods for method in business_methods)
        
        if not has_business_logic:
            print("✅ MCP客户端职责分离正确，不包含业务逻辑")
        else:
            print("❌ MCP客户端包含业务逻辑，职责分离不正确")
            return False
        
        # 验证LLM主机包含业务逻辑
        from src.search.llm_search_host_v2 import LLM_search
        host_methods = [method for method in dir(LLM_search) if not method.startswith('_')]
        
        print("\nLLM主机方法:")
        for method in host_methods:
            print(f"  - {method}")
        
        # 检查是否包含所有必要的业务方法
        has_all_business_methods = all(method in host_methods for method in business_methods)
        
        if has_all_business_methods:
            print("✅ LLM主机包含所有必要的业务逻辑")
        else:
            print("❌ LLM主机缺少必要的业务逻辑")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 架构分离验证失败: {e}")
        return False

def test_end_to_end_flow():
    """测试端到端流程"""
    print("\n" + "=" * 60)
    print("测试4: 端到端流程测试")
    print("=" * 60)
    
    try:
        # 检查API密钥
        if not os.getenv("OPENAI_API_KEY") and not os.getenv("GOOGLE_API_KEY"):
            print("⚠️  警告: 未设置LLM API密钥，跳过端到端测试")
            return False
        
        from src.search.llm_search_host_v2 import LLM_search
        
        retriever = LLM_search(
            model='gemini-2.0-flash-thinking-exp-01-21',
            infer_type="OpenAI",
            engine='google',
            each_query_result=5
        )
        
        # 测试查询生成（通过LLM Host -> MCP Client -> MCP Server）
        start_time = time.time()
        queries = retriever.get_queries(
            topic="人工智能伦理",
            description="研究人工智能发展中的伦理问题"
        )
        end_time = time.time()
        
        print(f"✅ 端到端查询生成成功，耗时: {end_time - start_time:.2f}秒")
        print(f"  生成查询数量: {len(queries)}")
        print("  生成的查询:")
        for i, query in enumerate(queries[:3], 1):  # 只显示前3个
            print(f"    {i}. {query}")
        if len(queries) > 3:
            print(f"    ... 还有 {len(queries) - 3} 个查询")
        
        # 验证数据流：LLM Host -> MCP Client -> MCP Server -> 外部API -> 返回
        if isinstance(queries, list) and all(isinstance(q, str) for q in queries):
            print("✅ 数据流正确，返回格式符合预期")
        else:
            print("❌ 数据流错误，返回格式不符合预期")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 端到端流程测试失败: {e}")
        return False

def test_original_interface_compatibility():
    """测试与原有接口的兼容性"""
    print("\n" + "=" * 60)
    print("测试5: 原有接口兼容性测试")
    print("=" * 60)
    
    try:
        from src.search.llm_search_host_v2 import LLM_search
        
        # 模拟原有的使用方式（如start_pipeline.py中的使用）
        retriever = LLM_search(
            model='gemini-2.0-flash-thinking-exp-01-21', 
            infer_type="OpenAI", 
            engine='google', 
            each_query_result=10
        )
        
        print("✅ 原有初始化方式兼容")
        
        # 测试方法签名兼容性
        import inspect
        
        # 检查get_queries方法签名
        get_queries_sig = inspect.signature(retriever.get_queries)
        expected_params = ['topic', 'description']
        actual_params = list(get_queries_sig.parameters.keys())
        
        if all(param in actual_params for param in expected_params):
            print("✅ get_queries方法签名兼容")
        else:
            print(f"❌ get_queries方法签名不兼容: 期望{expected_params}, 实际{actual_params}")
            return False
        
        # 检查batch_web_search方法签名
        batch_search_sig = inspect.signature(retriever.batch_web_search)
        expected_params = ['queries', 'topic', 'top_n']
        actual_params = list(batch_search_sig.parameters.keys())
        
        if all(param in actual_params for param in expected_params):
            print("✅ batch_web_search方法签名兼容")
        else:
            print(f"❌ batch_web_search方法签名不兼容: 期望{expected_params}, 实际{actual_params}")
            return False
        
        print("✅ 所有方法签名与原有接口兼容")
        return True
        
    except Exception as e:
        print(f"❌ 原有接口兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始正确MCP架构测试")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查环境变量
    print("\n📋 环境变量检查:")
    env_vars = ["OPENAI_API_KEY", "GOOGLE_API_KEY", "SERP_API_KEY", "BING_SEARCH_V7_SUBSCRIPTION_KEY"]
    for var in env_vars:
        value = os.getenv(var)
        status = "✅ 已设置" if value else "❌ 未设置"
        print(f"  {var}: {status}")
    
    # 运行测试
    tests = [
        ("纯粹MCP客户端", test_mcp_client_only),
        ("LLM主机接口", test_llm_host_interface),
        ("架构分离验证", test_architecture_separation),
        ("端到端流程", test_end_to_end_flow),
        ("原有接口兼容性", test_original_interface_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 发生异常: {e}")
            results.append((test_name, False))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！正确的MCP架构实现成功。")
        print("\n📝 架构优势:")
        print("✅ 职责分离清晰：LLM Host(业务) + MCP Client(通信) + MCP Server(工具)")
        print("✅ 接口完全兼容：可无缝替代原有LLM_search模块")
        print("✅ 可维护性强：各层职责明确，易于扩展和调试")
        print("✅ 标准化协议：遵循MCP标准，易于集成其他系统")
    else:
        print("⚠️  部分测试失败，请检查实现。")

if __name__ == "__main__":
    main()
