import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from mcp import ClientSession
from mcp.client.stdio import stdio_client
from mcp.types import CallToolRequest, TextContent

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LLMSearchMCPClient:

    
    def __init__(self, server_config: Dict[str, Any]):

        self.server_config = server_config
        self.session = None
        self.read_stream = None
        self.write_stream = None
        
    async def connect(self):
        try:
            logger.info("Connecting to LLM Search MCP Server...")

            params = {
                "command": self.server_config["command"],
                "args": self.server_config.get("args", []),
                "env": self.server_config.get("env", None)
            }
            
            self.read_stream, self.write_stream = await stdio_client(**params).__aenter__()
            self.session = await ClientSession(self.read_stream, self.write_stream).__aenter__()
        
            await self.session.initialize()
            
            logger.info("Successfully connected to MCP server")
            
        except Exception as e:
            logger.error(f"Failed to connect to MCP server: {e}")
            raise
    
    async def disconnect(self):
        try:
            if self.session:
                await self.session.__aexit__(None, None, None)
            if self.read_stream and self.write_stream:
                await stdio_client(
                    command=self.server_config["command"],
                    args=self.server_config.get("args", []),
                    env=self.server_config.get("env", None)
                ).__aexit__(None, None, None)
            logger.info("Disconnected from MCP server")
        except Exception as e:
            logger.error(f"Error disconnecting from MCP server: {e}")
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        try:
            if not self.session:
                raise RuntimeError("Not connected to MCP server")
            
            tools = await self.session.list_tools()
            logger.info(f"Available tools: {[tool.name for tool in tools.tools]}")
            
            return [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.inputSchema
                }
                for tool in tools.tools
            ]
            
        except Exception as e:
            logger.error(f"Error listing tools: {e}")
            raise
    
    async def generate_search_queries(self, topic: str, description: str = "", 
                                    model: str = "gemini-2.0-flash-thinking-exp-01-21") -> Dict[str, Any]:

        try:
            if not self.session:
                raise RuntimeError("Not connected to MCP server")
            
            arguments = {
                "topic": topic,
                "description": description,
                "model": model
            }
            
            logger.info(f"Generating search queries for topic: {topic}")
            
            result = await self.session.call_tool(
                CallToolRequest(
                    name="generate_search_queries",
                    arguments=arguments
                )
            )

            if result.content and len(result.content) > 0:
                content = result.content[0]
                if isinstance(content, TextContent):
                    return json.loads(content.text)
            
            raise ValueError("No valid response received")
            
        except Exception as e:
            logger.error(f"Error generating search queries: {e}")
            raise
    
    async def web_search(self, queries: List[str], topic: str, top_n: int = 20, 
                        engine: str = "google") -> Dict[str, Any]:
        try:
            if not self.session:
                raise RuntimeError("Not connected to MCP server")
            
            arguments = {
                "queries": queries,
                "topic": topic,
                "top_n": top_n,
                "engine": engine
            }
            
            logger.info(f"Performing web search with {len(queries)} queries")
            
            result = await self.session.call_tool(
                CallToolRequest(
                    name="web_search",
                    arguments=arguments
                )
            )
            
            if result.content and len(result.content) > 0:
                content = result.content[0]
                if isinstance(content, TextContent):
                    return json.loads(content.text)
            
            raise ValueError("No valid response received")
            
        except Exception as e:
            logger.error(f"Error in web search: {e}")
            raise
    
    async def analyze_search_results(self, urls: List[str], topic: str, 
                                   max_results: int = 10) -> Dict[str, Any]:
        try:
            if not self.session:
                raise RuntimeError("Not connected to MCP server")
            
            arguments = {
                "urls": urls,
                "topic": topic,
                "max_results": max_results
            }
            
            logger.info(f"Analyzing {len(urls)} search results")
            
            result = await self.session.call_tool(
                CallToolRequest(
                    name="analyze_search_results",
                    arguments=arguments
                )
            )
            
            if result.content and len(result.content) > 0:
                content = result.content[0]
                if isinstance(content, TextContent):
                    return json.loads(content.text)
            
            raise ValueError("No valid response received")
            
        except Exception as e:
            logger.error(f"Error analyzing search results: {e}")
            raise
    
    async def full_search_pipeline(self, topic: str, description: str = "", 
                                 top_n: int = 10, model: str = "gemini-2.0-flash-thinking-exp-01-21",
                                 engine: str = "google") -> Dict[str, Any]:

        try:
            if not self.session:
                raise RuntimeError("Not connected to MCP server")
            
            arguments = {
                "topic": topic,
                "description": description,
                "top_n": top_n,
                "model": model,
                "engine": engine
            }
            
            logger.info(f"Executing full search pipeline for topic: {topic}")
            
            result = await self.session.call_tool(
                CallToolRequest(
                    name="full_search_pipeline",
                    arguments=arguments
                )
            )
            
            if result.content and len(result.content) > 0:
                content = result.content[0]
                if isinstance(content, TextContent):
                    return json.loads(content.text)
            
            raise ValueError("No valid response received")
            
        except Exception as e:
            logger.error(f"Error in full search pipeline: {e}")
            raise

async def create_llm_search_client(config_path: str = "config/llm_search_mcp_config.json") -> LLMSearchMCPClient:

    try:

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        server_config = config["servers"]["llm_search_server"]

        client = LLMSearchMCPClient(server_config)
        await client.connect()
        
        return client
        
    except Exception as e:
        logger.error(f"Failed to create LLM search client: {e}")
        raise


async def example_usage():
    """示例用法"""
    client = None
    try:

        client = await create_llm_search_client()
        
        tools = await client.list_tools()
        print("Available tools:", [tool["name"] for tool in tools])
        
        result = await client.full_search_pipeline(
            topic="machine learning optimization",
            description="Research on optimization techniques in machine learning",
            top_n=5
        )
        
        print("Search results:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
    except Exception as e:
        logger.error(f"Example usage failed: {e}")
    finally:
        if client:
            await client.disconnect()

if __name__ == "__main__":
    asyncio.run(example_usage())
