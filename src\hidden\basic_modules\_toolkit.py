import re
import logging
from typing import List, Dict, Any, Optional

from tenacity import retry, stop_after_attempt, after_log, retry_if_exception_type
from camel.toolkits import BaseToolkit, FunctionTool

from request import RequestWrapper
from src.data_structure import Digest, Survey
from src.exceptions import (
    BibkeyNotFoundError,
    StructureNotCorrespondingError,
    MdNotFoundError,
)
from src.utils.process_str import (
    str2list,
    list2str,
    remove_illegal_bibkeys,
    parse_md_content,
)
from src.prompts import SINGLE_DIGEST_PROMPT

logger = logging.getLogger(__name__)


class SingleDigestToolkit(BaseToolkit):

    def __init__(
        self, 
        model: str = "gpt-4",
        infer_type: str = "OpenAI",
        timeout: Optional[float] = None
    ):
        super().__init__(timeout=timeout)
        self.request_pool = RequestWrapper(model=model, infer_type=infer_type)
        self.prompt = SINGLE_DIGEST_PROMPT

    @retry(
        stop=stop_after_attempt(10),
        after=after_log(logger, logging.WARNING),
        retry=retry_if_exception_type(
            (
                BibkeyNotFoundError,
                StructureNotCorrespondingError,
                MdNotFoundError,
                IndexError,
                ValueError,
            )
        ),
    )
    async def generate_single_digest(
        self, 
        paper_info: Dict[str, Any], 
        outline: Any, 
        survey_title: str
    ) -> Dict[str, Any]:
        """为单篇论文生成摘要
        
        Args:
            paper_info: 包含论文信息的字典
            outline: 调研大纲
            survey_title: 调研标题
            
        Returns:
            Dict[str, Any]: 包含摘要内容的字典
        """
        new_digest = Digest([], survey_title)
        
        outline_content = outline.all_skeleton(construction=True, with_index=True)
        outline_content = remove_illegal_bibkeys(
            outline_content, [paper_info["bibkey"]], raise_warning=False
        )
        outline_example = outline.all_skeleton(
            with_digest_placeholder=True, with_index=True
        )

        paper_bibkey = paper_info["bibkey"]
        paper_content = paper_info["content"].replace("#", "")

        prompt = self.prompt.format(
            survey_title=survey_title,
            paper_bibkey=f"{paper_bibkey}",
            paper_content=paper_content,
            survey_outline=outline_content,
            outline_example=outline_example,
        )
        
        failure_count = 0
        result = ""
        try:
            result = self.request_pool.completion(prompt)
            result = result.replace("['BIBKEY']", f"['{paper_bibkey}']")
            result = result.replace("[BIBKEY]", f"['{paper_bibkey}']")
            logger.info(f"Single Digest Generate Finished: {paper_bibkey}")
            
            new_digest.paper_infos = [paper_info]
            new_digest.parse_suggestion(result, paper_bibkey)
            new_digest = new_digest.parse_raw_digest(result, outline)
            
            return {
                "digest": new_digest.to_dict(),
                "bibkey": paper_bibkey,
                "success": True
            }
            
        except Exception as e:
            failure_count += 1
            content_len = int(len(paper_content) * 0.5)
            paper_info["content"] = paper_content[:int(content_len)]
            
            if failure_count >= 5:
                logger.warning(
                    f"Single Digest Generate Failed: {paper_bibkey}, Error: {e}, \nprompt: {prompt}, \nresult: {result}"
                )
                
            if failure_count >= 9:
                empty_digest = new_digest.parse_raw_digest(
                    f"```markdown\n{outline.all_skeleton(with_index=True)}\n```", 
                    outline
                )
                logger.warning(
                    f"Single Digest Generate Failed, return empty: {paper_bibkey}"
                )
                return {
                    "digest": empty_digest.to_dict(),
                    "bibkey": paper_bibkey,
                    "success": False,
                    "error": str(e)
                }
                
            raise e

    def get_tools(self) -> List[FunctionTool]:
        return [
            FunctionTool(self.generate_single_digest)
        ]


class MergeDigestToolkit(BaseToolkit):

    def __init__(
        self, 
        model: str = "gpt-4",
        infer_type: str = "OpenAI",
        timeout: Optional[float] = None
    ):
        super().__init__(timeout=timeout)
        self.request_pool = RequestWrapper(model=model, infer_type=infer_type)

    @retry(
        stop=stop_after_attempt(15),
        after=after_log(logger, logging.WARNING),
        retry=retry_if_exception_type(
            (
                BibkeyNotFoundError,
                StructureNotCorrespondingError,
                MdNotFoundError,
                IndexError,
                ValueError,
            )
        ),
    )
    async def merge_digests(
        self, 
        digest_dicts: List[Dict[str, Any]], 
        outline: Any,
        survey_title: str
    ) -> Dict[str, Any]:
        """合并多个摘要生成一个完整的摘要
        
        Args:
            digest_dicts: 摘要字典列表
            outline: 调研大纲
            survey_title: 调研标题
            
        Returns:
            Dict[str, Any]: 合并后的摘要字典
        """
        digests = [Digest.from_dict(d["digest"]) for d in digest_dicts if d.get("success", False)]
        
        if not digests:
            logger.warning("No valid digests to merge")
            return {"error": "No valid digests to merge", "success": False}
    
        new_digest = Digest.from_multiple_digests(digests, outline)
        
        for i, section in enumerate(new_digest.root.all_section):
            section.description = ""
            descriptions = []
            for digest in digests:
                if i < len(digest.root.all_section):
                    d_section = digest.root.all_section[i]
                    if d_section.description:
                        descriptions.append(
                            f"Paper bibkey: [{''.join(digest.bibkeys)}]\nDigest: \n{d_section.description}"
                        )
            section.description = "--------------------\n".join(descriptions)
        
        bibkeys = []
        for digest in digests:
            bibkeys.extend(digest.bibkeys)
            
        logger.info(f"Merged {len(digests)} digests successfully")
        return {
            "digest": new_digest.to_dict(),
            "bibkeys": bibkeys,
            "success": True
        }

    def get_tools(self) -> List[FunctionTool]:
        return [
            FunctionTool(self.merge_digests)
        ]


class DigestManagerToolkit(BaseToolkit):

    def __init__(self, timeout: Optional[float] = None):
        super().__init__(timeout=timeout)

    async def process_survey_digests(
        self, 
        survey_dict: Dict[str, Any],
        single_digest_url: str,
        merge_digest_url: str
    ) -> Dict[str, Any]:
        """处理整个调研的摘要生成流程
        
        Args:
            survey_dict: 调研字典
            single_digest_url: 单篇摘要服务URL
            merge_digest_url: 合并摘要服务URL
            
        Returns:
            Dict[str, Any]: 更新后的调研字典
        """
        from mcp_client import DigestClient
        
        survey = Survey.from_dict(survey_dict)
        outline = survey.skeleton
        
        async with DigestClient(
            single_digest_url=single_digest_url,
            merge_digest_url=merge_digest_url
        ) as client:
            for digest_key, digest in survey.digests.items():
                paper_infos = digest.get_paper_infos()
                
                for paper_info in paper_infos:
                    paper_info["content"] = paper_info.get("origin_content", "")
                
                single_digest_results = []
                for paper_info in paper_infos:
                    result = await client.generate_single_digest(
                        paper_info=paper_info,
                        outline=outline,
                        survey_title=survey.title
                    )
                    single_digest_results.append(result)
                
                merged_result = await client.merge_digests(
                    digest_dicts=single_digest_results,
                    outline=outline,
                    survey_title=survey.title
                )
                
                if merged_result.get("success", False):
                    new_digest = Digest.from_dict(merged_result["digest"])
                    survey.digests[digest_key] = new_digest
        
        logger.info(f"Processed all digests for survey: {survey.title}")
        return survey.to_dict()

    def get_tools(self) -> List[FunctionTool]:
        return [
            FunctionTool(self.process_survey_digests)
        ]