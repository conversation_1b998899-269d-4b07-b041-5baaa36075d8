import re
import random
import logging
from typing import List, Dict, Any, Optional

from tenacity import retry, stop_after_attempt, after_log, retry_if_exception_type
from camel.toolkits import BaseToolkit, FunctionTool

from request import RequestWrapper
from src.base_method.data import Dataset
from src.data_structure import Digest, Survey
from src.exceptions import (
    BibkeyNotFoundError,
    StructureNotCorrespondingError,
    MdNotFoundError,
    GroupEmptyError,
)
from src.utils.process_str import list2str, str2list
from src.prompts import GROUP_PROMPT

logger = logging.getLogger(__name__)


class GroupManagerToolkit(BaseToolkit):
    """管理论文分组的工具包"""

    def __init__(self, timeout: Optional[float] = None):
        """初始化分组管理工具包
        
        Args:
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        logger.info("GroupManagerToolkit initialized")

    async def process_survey_groups(
        self, 
        survey_dict: Dict[str, Any],
        mode: str = "llm",
        digest_batch: int = 3,
        group_neuron_url: str = None
    ) -> Dict[str, Any]:
        """处理整个调研的论文分组流程
        
        Args:
            survey_dict: 调研字典
            mode: 分组模式，"llm"或"random"
            digest_batch: 每组论文的数量
            group_neuron_url: GroupNeuron服务URL
            
        Returns:
            Dict[str, Any]: 处理后的调研字典
        """
        # 导入客户端工具
        from mcp_toolkit import MCPClient
        
        # 恢复Survey对象
        survey = Survey.from_dict(survey_dict)
        papers = list(survey.papers.values())
        
        logger.info(f"Processing groups for survey: {survey.title} with {len(papers)} papers")
        
        # 创建客户端连接
        neuron_client = None
        if mode == "llm" and group_neuron_url:
            neuron_client = MCPClient(group_neuron_url)
            await neuron_client.connect()
        
        try:
            paper_groups = []
            
            if mode == "random":
                # 随机分组
                random.shuffle(papers)
                for i in range(0, len(papers), digest_batch):
                    paper_groups.append(papers[i : i + digest_batch])
                
                logger.info(f"Created {len(paper_groups)} random paper groups")
                
            elif mode == "llm" and neuron_client:
                # 使用LLM进行分组
                group_tool = next(
                    tool for tool in neuron_client.get_tools() 
                    if tool.get_function_name() == "group_papers"
                )
                
                result = await group_tool.async_call(
                    papers=papers,
                    batch_size=digest_batch,
                    survey_title=survey.title
                )
                
                if result.get("success", False):
                    paper_groups = result.get("paper_groups", [])
                    logger.info(f"Created {len(paper_groups)} LLM-based paper groups")
                else:
                    logger.warning("LLM grouping failed, falling back to random grouping")
                    # 失败时回退到随机分组
                    random.shuffle(papers)
                    for i in range(0, len(papers), digest_batch):
                        paper_groups.append(papers[i : i + digest_batch])
            
            # 将分组结果转换为摘要对象
            for paper_batch in paper_groups:
                digest = Digest(paper_batch, survey.title)
                bibkeys = digest.bibkeys
                survey.digests[bibkeys] = digest

            survey.digest_batch_size = digest_batch
            logger.info(
                f"Group Reference Finished: {survey.title}, Digest Count: {len(survey.digests)}"
            )
            
            return survey.to_dict()
            
        finally:
            # 关闭客户端连接
            if neuron_client:
                await neuron_client.disconnect()

    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.process_survey_groups)
        ]


class GroupNeuronToolkit(BaseToolkit):
    """使用LLM进行论文分组的工具包"""

    def __init__(
        self,
        model: str = "gpt-4",
        infer_type: str = "OpenAI",
        timeout: Optional[float] = None
    ):
        """初始化LLM分组工具包
        
        Args:
            model: 使用的LLM模型
            infer_type: 推理类型
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.prompt = GROUP_PROMPT
        self.request_pool = RequestWrapper(model=model, infer_type=infer_type)
        logger.info(f"GroupNeuronToolkit initialized with model: {model}")

    @retry(
        stop=stop_after_attempt(5),
        after=after_log(logger, logging.WARNING),
        retry=retry_if_exception_type(GroupEmptyError),
    )
    async def group_papers(
        self, 
        papers: List[Dict[str, Any]], 
        batch_size: int, 
        survey_title: str
    ) -> Dict[str, Any]:
        """使用LLM对论文进行分组
        
        Args:
            papers: 论文列表
            batch_size: 每组论文的目标数量
            survey_title: 调研标题
            
        Returns:
            Dict[str, Any]: 包含分组结果的字典
        """
        try:
            # 获取论文信息
            papers_info = [(paper["title"], paper["bibkey"]) for paper in papers]
            
            # 调用LLM获取分组结果
            raw_result = await self._forward_to_llm(papers_info, survey_title)
            
            # 解析LLM返回的分组结果
            parsed_result, rest_bibkeys = await self._parse_group_result(
                raw_result, [bibkey for _, bibkey in papers_info]
            )
            
            # 重新组织分组，确保每组大小合适
            grouped_result = await self._regroup_result(parsed_result, rest_bibkeys, batch_size)
            
            # 验证结果非空
            if len(grouped_result) == 0:
                raise GroupEmptyError(
                    f"Group Reference Error: {survey_title} grouped_result is empty"
                )
            
            # 将bibkey分组转换为论文分组
            paper_groups = []
            for group in grouped_result:
                paper_group = [paper for paper in papers if paper["bibkey"] in group]
                paper_groups.append(paper_group)
            
            return {
                "success": True,
                "paper_groups": paper_groups,
                "group_count": len(paper_groups)
            }
            
        except Exception as e:
            logger.error(f"Failed to group papers: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    @retry(
        stop=stop_after_attempt(5),
        after=after_log(logger, logging.WARNING),
        retry=retry_if_exception_type(
            (
                BibkeyNotFoundError,
                StructureNotCorrespondingError,
                MdNotFoundError,
                ValueError,
            )
        ),
    )
    async def _forward_to_llm(self, papers_info, survey_title):
        """向LLM发送请求获取分组结果"""
        def format_papers_info(papers_info):
            random.shuffle(papers_info)
            return "\n".join(
                [
                    f"Title: \"{title}\" Bibkey: '{bibkey}'"
                    for title, bibkey in papers_info
                ]
            )

        bibkeys = [bibkey for _, bibkey in papers_info]
        bibkeys = list2str(bibkeys)
        papers_info_str = format_papers_info(papers_info)
        prompt = self.prompt.format(
            survey_title=survey_title,
            titles=papers_info_str,
            bibkeys=bibkeys,
        )
        result = self.request_pool.completion(prompt)
        logger.info(f"Group Generate Finished: {survey_title}")
        return result

    async def _parse_group_result(self, raw_result, bibkeys):
        """解析LLM返回的分组结果"""
        result = []
        references_reg = re.compile(r"\[(.*?)\]")
        paper_groups = references_reg.findall(raw_result)
        for group in paper_groups:
            group = str2list(group)
            result.append(group)

        splited_bibkeys = [b for group in result for b in group]
        if hallucinate_bibkeys := set(splited_bibkeys) - set(bibkeys):
            result = [
                [b for b in group if b not in hallucinate_bibkeys] for group in result
            ]
        rest_bibkeys = set(bibkeys) - set(splited_bibkeys)

        return result, rest_bibkeys

    async def _regroup_result(self, result, rest_bibkeys, batch_size):
        """重新组织分组结果，确保每组大小合适"""
        final_result = []
        remaining_groups = []

        # Step 1: Split groups larger than batch_size
        for group in result:
            while len(group) >= batch_size:
                final_result.append(group[:batch_size])
                group = group[batch_size:]
            if group:
                remaining_groups.append(group)

        # Step 2: Sort remaining_groups by length in descending order
        remaining_groups.sort(key=len, reverse=True)

        # Step 3: Combine groups to make their length equal to batch_size
        combined_groups = []
        while len(remaining_groups) > 0:
            group1 = remaining_groups.pop(0)
            for i in range(len(remaining_groups)):
                if i < len(remaining_groups):
                    group2 = remaining_groups[i]
                    if len(group1) + len(group2) <= batch_size:
                        final_result.append(group1 + group2)
                        remaining_groups.pop(i)
                        break
            else:
                combined_groups.append(group1)

        # Step 4: Add remaining groups from rest_bibkeys
        for group in combined_groups:
            rest_bibkeys_list = list(rest_bibkeys)
            while len(group) < batch_size and rest_bibkeys_list:
                bibkey = rest_bibkeys_list.pop()
                group.append(bibkey)
                rest_bibkeys.remove(bibkey)
            final_result.append(group)

        # Step 5: Combine remaining rest_bibkeys into groups of batch_size
        rest_bibkeys_list = list(rest_bibkeys)
        random.shuffle(rest_bibkeys_list)
        for i in range(0, len(rest_bibkeys_list), batch_size):
            final_result.append(rest_bibkeys_list[i : i + batch_size])
            
        return final_result

    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.group_papers)
        ]