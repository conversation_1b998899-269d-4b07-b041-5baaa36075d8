#!/usr/bin/env python3
"""
Encode Processor MCP Server
数据编码处理 MCP 服务器
"""

import asyncio
import json
import logging
from typing import Dict, Any, List

from mcp.server import Server
from mcp.types import Resource, Tool, TextContent
import mcp.server.stdio

from src.encode.encode_toolkit import EncodePipelineToolkit, EncodeManagerToolkit

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Server("encode-processor")

# 全局工具包实例
encode_toolkit = None
manager_toolkit = None

@app.list_resources()
async def list_resources() -> List[Resource]:
    """列出可用的资源"""
    return [
        Resource(
            uri="encode://processor/config",
            name="Encode Processing Configuration",
            description="编码处理的配置和参数说明",
            mimeType="application/json"
        ),
        Resource(
            uri="encode://processor/stats",
            name="Processing Statistics",
            description="编码处理的统计信息",
            mimeType="application/json"
        )
    ]

@app.read_resource()
async def read_resource(uri: str) -> str:
    """读取资源内容"""
    if uri == "encode://processor/config":
        config_info = {
            "supported_formats": ["jsonl", "json"],
            "validation_options": {
                "clean_text": "清理和标准化文本内容",
                "add_metadata": "添加处理元数据",
                "standardize_structure": "标准化数据结构"
            },
            "processing_limits": {
                "max_file_size": "1GB",
                "max_surveys_per_batch": 10000,
                "timeout": "300s"
            }
        }
        return json.dumps(config_info, ensure_ascii=False, indent=2)
    
    elif uri == "encode://processor/stats":
        global encode_toolkit
        if encode_toolkit:
            stats = encode_toolkit.get_processing_stats()
        else:
            stats = {"status": "not_initialized"}
        return json.dumps(stats, ensure_ascii=False, indent=2)
    
    else:
        raise ValueError(f"Unknown resource: {uri}")

@app.list_tools()
async def list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="load_surveys_from_file",
            description="从文件加载调研数据",
            inputSchema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "输入文件路径"
                    },
                    "format_type": {
                        "type": "string",
                        "enum": ["jsonl", "json"],
                        "default": "jsonl",
                        "description": "文件格式类型"
                    },
                    "data_limit": {
                        "type": "integer",
                        "description": "处理数据的最大数量限制"
                    }
                },
                "required": ["file_path"]
            }
        ),
        Tool(
            name="validate_survey_data",
            description="验证调研数据的完整性和格式",
            inputSchema={
                "type": "object",
                "properties": {
                    "survey_data": {
                        "type": "object",
                        "description": "需要验证的调研数据"
                    },
                    "strict_validation": {
                        "type": "boolean",
                        "default": False,
                        "description": "是否启用严格验证模式"
                    }
                },
                "required": ["survey_data"]
            }
        ),
        Tool(
            name="preprocess_survey",
            description="预处理调研数据",
            inputSchema={
                "type": "object",
                "properties": {
                    "survey_data": {
                        "type": "object",
                        "description": "需要预处理的调研数据"
                    },
                    "preprocessing_options": {
                        "type": "object",
                        "properties": {
                            "clean_text": {"type": "boolean", "default": True},
                            "add_metadata": {"type": "boolean", "default": True},
                            "standardize_structure": {"type": "boolean", "default": True}
                        },
                        "description": "预处理选项"
                    }
                },
                "required": ["survey_data"]
            }
        ),
        Tool(
            name="batch_process_file",
            description="批量处理文件中的调研数据",
            inputSchema={
                "type": "object",
                "properties": {
                    "input_file": {
                        "type": "string",
                        "description": "输入文件路径"
                    },
                    "config": {
                        "type": "object",
                        "properties": {
                            "data_num": {"type": "integer"},
                            "format_type": {"type": "string", "default": "jsonl"},
                            "validation_enabled": {"type": "boolean", "default": True},
                            "preprocessing_options": {"type": "object"}
                        },
                        "description": "处理配置"
                    }
                },
                "required": ["input_file"]
            }
        ),
        Tool(
            name="get_processing_statistics",
            description="获取当前处理统计信息",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    ]

@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """调用工具"""
    global encode_toolkit, manager_toolkit
    
    try:
        # 初始化工具包
        if not encode_toolkit:
            encode_toolkit = EncodePipelineToolkit(
                data_limit=arguments.get("data_limit"),
                validation_enabled=arguments.get("validation_enabled", True)
            )
        
        if not manager_toolkit:
            manager_toolkit = EncodeManagerToolkit()
        
        if name == "load_surveys_from_file":
            result = await encode_toolkit.load_surveys_from_file(
                arguments["file_path"],
                arguments.get("format_type", "jsonl")
            )
            
        elif name == "validate_survey_data":
            result = await encode_toolkit.validate_survey_data(
                arguments["survey_data"]
            )
            
        elif name == "preprocess_survey":
            from src.data_structure import Survey
            survey = Survey(arguments["survey_data"])
            processed_survey = await encode_toolkit.preprocess_survey(
                survey,
                arguments.get("preprocessing_options", {})
            )
            result = processed_survey.to_dict()
            
        elif name == "batch_process_file":
            result = await manager_toolkit.process_input_data(
                arguments["input_file"],
                arguments.get("config", {})
            )
            
        elif name == "get_processing_statistics":
            result = encode_toolkit.get_processing_stats()
            
        else:
            raise ValueError(f"Unknown tool: {name}")
        
        return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
    
    except Exception as e:
        logger.error(f"Error calling tool {name}: {e}")
        return [TextContent(type="text", text=json.dumps({
            "error": str(e),
            "tool": name,
            "success": False
        }, ensure_ascii=False, indent=2))]

async def main():
    """启动 MCP server"""
    logger.info("Starting Encode Processor MCP Server...")
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await app.run(read_stream, write_stream, app.create_initialization_options())

if __name__ == "__main__":
    asyncio.run(main())
