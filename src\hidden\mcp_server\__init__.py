"""
MCP Server modules for intelligent pipeline processing.

This package contains Model Context Protocol (MCP) servers that handle
different aspects of the intelligent hidden pipeline:
- Pipeline Orchestrator: Manages workflow and decision making
- Digest Processor: Handles content summarization and digest generation
- Skeleton Processor: Creates and refines document outlines
- Group Processor: Performs semantic grouping of content
"""

__version__ = "1.0.0"
__author__ = "LLMxMapReduce Team"

# Import all server modules for easy access
# from .pipeline_orchestrator_server import PipelineOrchestratorServer
# from .digest_processor_server import DigestProcessorServer
# from .skeleton_processor_server import SkeletonProcessorServer
# from .group_processor_server import GroupProcessorServer

# __all__ = [
#     "PipelineOrchestratorServer",
#     "DigestProcessorServer", 
#     "SkeletonProcessorServer",
#     "GroupProcessorServer"
# ]
