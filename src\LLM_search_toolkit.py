import os
import re
import asyncio
import traceback
import requests
import logging
from typing import Optional, Literal, Dict, Any, List
from tenacity import retry, stop_after_attempt, before_log, retry_if_exception_type

from camel.toolkits import BaseToolkit, FunctionTool
from request import RequestWrapper
from prompts import (
    QUERY_EXPAND_PROMPT_WITH_ABSTRACT,
    QUERY_EXPAND_PROMPT_WITHOUT_ABSTRACT,
    LLM_CHECK_PROMPT,
    SNIPPET_FILTER_PROMPT,
)

logger = logging.getLogger(__name__)


class QueryParseError(Exception):
    pass


class SearchQueryToolkit(BaseToolkit):

    def __init__(self, model: str = "claude-3-5-haiku-20241022", infer_type: str = "OpenAI", timeout: Optional[float] = None):
        super().__init__(timeout=timeout)
        self.model = model
        self.request_pool = RequestWrapper(model=model, infer_type=infer_type)

    @retry(
        stop=stop_after_attempt(5),
        retry=retry_if_exception_type(QueryParseError),
        before=before_log(logger, logging.DEBUG),
    )
    async def generate_search_queries(self, topic: str, description: str = "") -> List[str]:
        """根据研究主题生成优化的搜索查询列表
        
        Args:
            topic: 研究主题
            description: 主题的描述或上下文（可选）
            
        Returns:
            list: 优化的搜索查询列表
        """
        if description:
            prompt = QUERY_EXPAND_PROMPT_WITH_ABSTRACT.format(
                topic=topic, abstract=description
            )
        else:
            prompt = QUERY_EXPAND_PROMPT_WITHOUT_ABSTRACT.format(topic=topic)

        response = self.request_pool.completion(prompt)
        reg = r"```markdown\n([\s\S]*?)```"
        match = re.search(reg, response)
        
        if not match:
            raise QueryParseError(
                f"Unable to parse query list from response, current response: {response}"
            )

        queries = match.group(1).strip().split(";")
        queries = [query.replace('"', "").strip() for query in queries if query.strip()]
        logger.info(f"Generated {len(queries)} search queries:\n{queries}")
        
        return queries

    def get_tools(self) -> List[FunctionTool]:
        return [
            FunctionTool(self.generate_search_queries)
        ]


class WebSearchToolkit(BaseToolkit):
    def __init__(self, engine: Literal["google", "baidu", "bing"] = "google", 
                 each_query_result: int = 10, filter_date: Optional[str] = None,
                 timeout: Optional[float] = None):
        super().__init__(timeout=timeout)
        self.engine = engine
        self.each_query_result = each_query_result
        self.filter_date = filter_date
        
        self.bing_subscription_key = os.getenv('BING_SEARCH_V7_SUBSCRIPTION_KEY')
        self.bing_endpoint = os.getenv('BING_SEARCH_V7_ENDPOINT', "https://api.bing.microsoft.com/v7.0/search")
        self.serpapi_key = os.getenv("SERP_API_KEY")
        
        # 验证API密钥
        if self.serpapi_key is not None:
            logger.info("Using SERPAPI for web search.")
        elif self.bing_subscription_key is not None:
            logger.info("Using Bing Search API for web search.")
        else:
            logger.warning("No valid search engine key provided. Please check environment variables.")

    async def search_web(self, query: str) -> Dict[str, Any]:
        """执行单个查询的网络搜索
        
        Args:
            query: 搜索查询字符串
            
        Returns:
            dict: 搜索结果，包含标题、URL、日期、来源和摘要
        """
        if self.serpapi_key is not None:
            return await self._serpapi_web_search(query)
        elif self.bing_subscription_key is not None:
            return await self._bing_web_search(query)
        else:
            raise ValueError("No valid search engine key provided.")
 
    async def _bing_web_search(self, query: str) -> Dict[str, Any]:
        """使用Bing搜索API执行网络搜索
        
        Args:
            query: 搜索查询字符串
            
        Returns:
            dict: Bing搜索结果
        """
        mkt = 'zh-CN'
        params = {
            'q': query.lstrip('\"').rstrip('\"'),
            'mkt': mkt,
            'count': self.each_query_result,
        }
        headers = {
            'Ocp-Apim-Subscription-Key': self.bing_subscription_key
        }

        try:
            response = requests.get(self.bing_endpoint, headers=headers, params=params)
            response.raise_for_status()
            if response.status_code == 200:
                results = response.json()
            else:
                raise ValueError(response.json())

            if "webPages" not in results or "value" not in results["webPages"]:
                raise Exception(f"No results found for query: '{query}'")

            web_snippets = {}
            for idx, page in enumerate(results["webPages"]["value"]):
                redacted_version = {
                    'title': page.get('name', ''),
                    'url': page.get('url', ''),
                    'snippet': page.get('snippet', ''),
                }
                if 'dateLastCrawled' in page:
                    redacted_version['date'] = page['dateLastCrawled']
                if 'displayUrl' in page:
                    redacted_version['source'] = page['displayUrl']
                web_snippets[str(idx)] = redacted_version

            return web_snippets

        except Exception as e:
            logger.error(f"Error during Bing search: {e}")
            raise e
    
    async def _serpapi_web_search(self, query: str) -> Dict[str, Any]:
        """使用SerpAPI执行网络搜索
        
        Args:
            query: 搜索查询字符串
            
        Returns:
            dict: SerpAPI搜索结果
        """
        params = {
            "engine": self.engine,
            "q": query.lstrip('"').rstrip('"'),
            "api_key": self.serpapi_key,
        }

        if self.engine == "google":
            params["google_domain"] = "google.com"
            params["num"] = self.each_query_result
            if self.filter_date is not None:
                params["tbs"] = f"cdr:1,cd_min:{self.filter_date}"
        elif self.engine == "baidu":
            params["rn"] = self.each_query_result
            if self.filter_date is not None:
                params["gpc"] = f"cdr:1,cd_min:{self.filter_date}"
        elif self.engine == "bing":
            params["count"] = self.each_query_result
            if self.filter_date is not None:
                params["filters"] = f"cdr:1,cd_min:{self.filter_date}"

        response = requests.get("https://serpapi.com/search.json", params=params)

        if response.status_code == 200:
            results = response.json()
        else:
            raise ValueError(response.json())

        if "organic_results" not in results.keys():
            error_msg = f"No results found for query: '{query}'"
            if self.filter_date is not None:
                error_msg += f" with filtering on date={self.filter_date}"
            raise Exception(error_msg)
            
        if len(results["organic_results"]) == 0:
            error_msg = f"No results found for '{query}'"
            if self.filter_date is not None:
                error_msg += f" with filter date={self.filter_date}"
            return {"error": error_msg}

        web_snippets = {}
        if "organic_results" in results:
            for idx, page in enumerate(results["organic_results"]):
                redacted_version = {
                    "title": page["title"],
                    "url": page["link"],
                }

                if "date" in page:
                    redacted_version["date"] = page["date"]
                if "source" in page:
                    redacted_version["source"] = page["source"]
                if "snippet" in page:
                    redacted_version["snippet"] = page["snippet"]
                if "snippet_highlighted_words" in page:
                    redacted_version["snippet_highlighted_words"] = list(
                        set(page["snippet_highlighted_words"])
                    )

                web_snippets[str(idx)] = redacted_version
                
        return web_snippets

    async def batch_search(self, queries: List[str]) -> Dict[str, Dict[str, Any]]:
        """批量执行多个查询的搜索
        
        Args:
            queries: 搜索查询列表
            
        Returns:
            dict: 每个查询的搜索结果
        """
        results = {}
        for query in queries:
            if not query:
                continue
                
            logger.info(f"Searching: {query}")
            try:
                web_snippets = await self.search_web(query)
                results[query] = web_snippets
            except Exception as e:
                logger.error(f"Error searching for '{query}': {e}")
                results[query] = {"error": str(e)}
                
        return results

    def get_tools(self) -> List[FunctionTool]:
        return [
            FunctionTool(self.search_web),
            FunctionTool(self.batch_search)
        ]


class RelevanceToolkit(BaseToolkit):

    def __init__(self, model: str = "claude-3-5-haiku-20241022", infer_type: str = "OpenAI", 
                 max_workers: int = 10, timeout: Optional[float] = None):
        super().__init__(timeout=timeout)
        self.model = model
        self.max_workers = max_workers
        self.request_pool = RequestWrapper(model=model, infer_type=infer_type)

    async def calculate_relevance(self, topic: str, snippet: str) -> float:
        """计算搜索结果与主题的相关性分数
        
        Args:
            topic: 研究主题
            snippet: 要比较的文本片段
            
        Returns:
            float: 0到100之间的相关性分数
        """
        prompt = SNIPPET_FILTER_PROMPT.format(
            topic=topic,
            snippet=snippet,
        )
        try:
            res = self.request_pool.completion(prompt)
            matches = re.findall(r"<SCORE>(\d+)</SCORE>", res)
            if not matches:
                raise ValueError("No valid SCORE found in response.")
                
            score = float(matches[-1])
            if score < 0 or score > 100:
                raise ValueError(f"Invalid similarity score: {score}")
                
            return score
        except Exception as e:
            logger.error(f"Error calculating similarity score: {e}")
            return 0.0

    async def filter_results_by_relevance(self, topic: str, search_results: Dict[str, Dict[str, Any]], 
                                         top_n: int = 20) -> List[Dict[str, Any]]:
        """根据相关性过滤搜索结果
        
        Args:
            topic: 研究主题
            search_results: 搜索结果字典
            top_n: 返回的最相关结果数量
            
        Returns:
            list: 按相关性排序的前N个结果
        """
        unique_results = {}
        for query_results in search_results.values():
            for _, result in query_results.items():
                if isinstance(result, dict) and "url" in result and "snippet" in result:
                    url = result["url"]
                    if url not in unique_results and result["snippet"]:
                        unique_results[url] = result
        
        if not unique_results:
            return []
            
        logger.info(f"Found {len(unique_results)} unique results for relevance filtering")
        
        async def process_relevance():
            tasks = []
            for url, result in unique_results.items():
                if result.get("snippet"):
                    tasks.append(self.calculate_relevance(topic, result["snippet"]))
                    
            scores = await asyncio.gather(*tasks)
            
            scored_results = []
            i = 0
            for url, result in unique_results.items():
                if result.get("snippet"):
                    scored_results.append((scores[i], result))
                    i += 1
                    
            return scored_results
            
        scored_results = await process_relevance()
        scored_results.sort(reverse=True, key=lambda x: x[0])
        top_results = []
        
        for score, result in scored_results[:top_n]:
            result_with_score = result.copy()
            result_with_score["relevance_score"] = score
            top_results.append(result_with_score)
            
        return top_results

    def get_tools(self) -> List[FunctionTool]:
        return [
            FunctionTool(self.calculate_relevance),
            FunctionTool(self.filter_results_by_relevance)
        ]