#!/usr/bin/env python3
"""
Convolution MCP Toolkit - 卷积层处理工具包
将原始的卷积算法转换为可通过自然语言控制的智能工具
"""

import json
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from camel.toolkits import BaseToolkit, FunctionTool

from src.data_structure import Survey, Feedback, Skeleton
from src.hidden.convolution_block.convolution_module import ConvolutionLayerModule
from src.hidden.convolution_block.neurons import (
    ModifyOutlineNeuron,
    EvalOutlineNeuron,
    ConvolutionKernelNeuron,
    FeedbackClusterNeuron
)

logger = logging.getLogger(__name__)


class ConvolutionLayerToolkit(BaseToolkit):
    """卷积层处理工具包 - 智能优化outline和处理feedback"""

    def __init__(self, config: Dict[str, Any], timeout: Optional[float] = None):
        """初始化卷积层工具包
        
        Args:
            config: 卷积层配置
            timeout: 操作超时时间
        """
        super().__init__(timeout=timeout)
        self.config = config
        self.convolution_layer = config.get("convolution_layer", 3)
        self.receptive_field = config.get("receptive_field", 5)
        self.result_num = config.get("result_num", 10)
        self.top_k = config.get("top_k", 3)
        
        # 初始化神经元
        self._init_neurons()
        
        logger.info("ConvolutionLayerToolkit initialized")

    def _init_neurons(self):
        """初始化卷积神经元"""
        try:
            self.modify_neuron = ModifyOutlineNeuron(self.config.get("modify", {}), "residual")
            self.eval_neuron = EvalOutlineNeuron(self.config.get("eval", {}))
            self.convolution_kernel = ConvolutionKernelNeuron(self.config.get("kernel", {}))
            self.feedback_cluster = FeedbackClusterNeuron(self.config.get("cluster", {}))
        except Exception as e:
            logger.warning(f"Some neurons failed to initialize: {e}")

    @FunctionTool
    async def apply_convolution_layer(
        self,
        survey_data: Dict[str, Any],
        feedback_list: List[Dict[str, Any]],
        original_outline: str,
        processing_strategy: str = "adaptive"
    ) -> Dict[str, Any]:
        """应用卷积层处理来优化outline
        
        Args:
            survey_data: 调研数据
            feedback_list: 反馈建议列表
            original_outline: 原始大纲
            processing_strategy: 处理策略 ("adaptive", "aggressive", "conservative")
            
        Returns:
            Dict[str, Any]: 处理结果包含优化后的outline和质量评估
        """
        try:
            logger.info(f"Applying convolution layer with strategy: {processing_strategy}")
            
            # 创建Survey对象
            survey = Survey(survey_data)
            
            # 转换feedback数据
            feedbacks = [Feedback(fb) for fb in feedback_list]
            
            # 评估原始outline质量
            original_score = await self._evaluate_outline_quality(survey.title, original_outline)
            
            # 应用卷积处理
            convolution_results = await self._process_convolution_layers(
                survey, feedbacks, original_outline, processing_strategy
            )
            
            # 选择最佳结果
            best_result = await self._select_best_outline(
                survey.title, original_outline, convolution_results, original_score
            )
            
            return {
                "success": True,
                "original_outline": original_outline,
                "original_score": original_score,
                "optimized_outline": best_result["outline"],
                "optimized_score": best_result["score"],
                "improvement": best_result["score"] - original_score,
                "processing_strategy": processing_strategy,
                "convolution_layers_applied": len(convolution_results),
                "confidence": best_result.get("confidence", 0.8)
            }
            
        except Exception as e:
            logger.error(f"Error in convolution layer processing: {e}")
            return {
                "success": False,
                "error": str(e),
                "original_outline": original_outline,
                "optimized_outline": original_outline,
                "confidence": 0.0
            }

    @FunctionTool
    async def evaluate_outline_quality(
        self,
        survey_title: str,
        outline: str,
        evaluation_criteria: List[str] = None
    ) -> Dict[str, Any]:
        """评估outline质量
        
        Args:
            survey_title: 调研标题
            outline: 待评估的大纲
            evaluation_criteria: 评估标准列表
            
        Returns:
            Dict[str, Any]: 质量评估结果
        """
        try:
            criteria = evaluation_criteria or [
                "逻辑结构", "内容完整性", "层次清晰度", "主题相关性", "可操作性"
            ]
            
            # 使用评估神经元
            overall_score = await self._evaluate_outline_quality(survey_title, outline)
            
            # 详细评估各个维度
            detailed_scores = {}
            for criterion in criteria:
                score = await self._evaluate_specific_criterion(
                    survey_title, outline, criterion
                )
                detailed_scores[criterion] = score
            
            avg_detailed_score = sum(detailed_scores.values()) / len(detailed_scores)
            
            return {
                "overall_score": overall_score,
                "detailed_scores": detailed_scores,
                "average_detailed_score": avg_detailed_score,
                "evaluation_criteria": criteria,
                "recommendations": await self._generate_improvement_recommendations(
                    outline, detailed_scores
                )
            }
            
        except Exception as e:
            logger.error(f"Error evaluating outline quality: {e}")
            return {
                "overall_score": 0.5,
                "detailed_scores": {},
                "error": str(e)
            }

    @FunctionTool
    async def modify_outline_intelligently(
        self,
        current_outline: str,
        modification_instructions: str,
        survey_context: Dict[str, Any],
        modification_mode: str = "residual"
    ) -> Dict[str, Any]:
        """智能修改outline
        
        Args:
            current_outline: 当前大纲
            modification_instructions: 修改指令
            survey_context: 调研上下文
            modification_mode: 修改模式 ("residual", "complete", "incremental")
            
        Returns:
            Dict[str, Any]: 修改结果
        """
        try:
            logger.info(f"Modifying outline with mode: {modification_mode}")
            
            # 根据模式选择修改策略
            if modification_mode == "residual":
                modified_outline = await self._apply_residual_modification(
                    current_outline, modification_instructions, survey_context
                )
            elif modification_mode == "complete":
                modified_outline = await self._apply_complete_modification(
                    current_outline, modification_instructions, survey_context
                )
            else:  # incremental
                modified_outline = await self._apply_incremental_modification(
                    current_outline, modification_instructions, survey_context
                )
            
            # 评估修改质量
            original_quality = await self._evaluate_outline_quality(
                survey_context.get("title", ""), current_outline
            )
            modified_quality = await self._evaluate_outline_quality(
                survey_context.get("title", ""), modified_outline
            )
            
            return {
                "success": True,
                "original_outline": current_outline,
                "modified_outline": modified_outline,
                "modification_instructions": modification_instructions,
                "modification_mode": modification_mode,
                "quality_improvement": modified_quality - original_quality,
                "original_quality": original_quality,
                "modified_quality": modified_quality
            }
            
        except Exception as e:
            logger.error(f"Error modifying outline: {e}")
            return {
                "success": False,
                "error": str(e),
                "original_outline": current_outline,
                "modified_outline": current_outline
            }

    @FunctionTool
    async def cluster_feedback_intelligently(
        self,
        feedback_list: List[Dict[str, Any]],
        clustering_strategy: str = "semantic",
        target_clusters: int = None
    ) -> Dict[str, Any]:
        """智能聚类反馈信息
        
        Args:
            feedback_list: 反馈列表
            clustering_strategy: 聚类策略 ("semantic", "topic", "priority")
            target_clusters: 目标聚类数量
            
        Returns:
            Dict[str, Any]: 聚类结果
        """
        try:
            logger.info(f"Clustering feedback with strategy: {clustering_strategy}")
            
            if not feedback_list:
                return {"clusters": [], "cluster_count": 0}
            
            # 自动确定聚类数量
            if target_clusters is None:
                target_clusters = min(max(2, len(feedback_list) // 3), 8)
            
            clusters = await self._perform_feedback_clustering(
                feedback_list, clustering_strategy, target_clusters
            )
            
            # 评估聚类质量
            cluster_quality = await self._evaluate_clustering_quality(clusters)
            
            return {
                "clusters": clusters,
                "cluster_count": len(clusters),
                "clustering_strategy": clustering_strategy,
                "target_clusters": target_clusters,
                "cluster_quality": cluster_quality,
                "cluster_summaries": [
                    await self._summarize_cluster(cluster) for cluster in clusters
                ]
            }
            
        except Exception as e:
            logger.error(f"Error clustering feedback: {e}")
            return {
                "clusters": [],
                "cluster_count": 0,
                "error": str(e)
            }

    # 私有辅助方法
    async def _evaluate_outline_quality(self, title: str, outline: str) -> float:
        """评估outline质量"""
        try:
            if hasattr(self, 'eval_neuron'):
                return self.eval_neuron(title, outline)
            else:
                # 简单的启发式评估
                return min(1.0, len(outline.split('\n')) / 10.0)
        except Exception as e:
            logger.warning(f"Error in outline evaluation: {e}")
            return 0.5

    async def _evaluate_specific_criterion(self, title: str, outline: str, criterion: str) -> float:
        """评估特定标准"""
        # 这里可以实现更详细的评估逻辑
        base_score = await self._evaluate_outline_quality(title, outline)
        # 根据不同标准调整分数
        adjustment = {
            "逻辑结构": 0.1,
            "内容完整性": 0.05,
            "层次清晰度": 0.08,
            "主题相关性": 0.12,
            "可操作性": -0.02
        }.get(criterion, 0.0)
        
        return max(0.0, min(1.0, base_score + adjustment))

    async def _generate_improvement_recommendations(
        self, outline: str, detailed_scores: Dict[str, float]
    ) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        for criterion, score in detailed_scores.items():
            if score < 0.6:
                if criterion == "逻辑结构":
                    recommendations.append("建议重新组织大纲的逻辑顺序，确保内容递进合理")
                elif criterion == "内容完整性":
                    recommendations.append("建议补充缺失的关键内容部分")
                elif criterion == "层次清晰度":
                    recommendations.append("建议调整章节层次，使结构更加清晰")
                elif criterion == "主题相关性":
                    recommendations.append("建议加强内容与主题的相关性")
                elif criterion == "可操作性":
                    recommendations.append("建议增加更具体的操作指导")
        
        return recommendations

    async def _process_convolution_layers(
        self, survey: Survey, feedbacks: List[Feedback], 
        original_outline: str, strategy: str
    ) -> List[Dict[str, Any]]:
        """处理卷积层"""
        results = []
        current_outline = original_outline
        
        for layer in range(self.convolution_layer):
            try:
                # 应用卷积操作
                layer_result = await self._apply_single_convolution_layer(
                    survey, feedbacks, current_outline, layer, strategy
                )
                results.append(layer_result)
                current_outline = layer_result["outline"]
                
            except Exception as e:
                logger.warning(f"Error in convolution layer {layer}: {e}")
                continue
        
        return results

    async def _apply_single_convolution_layer(
        self, survey: Survey, feedbacks: List[Feedback], 
        outline: str, layer_index: int, strategy: str
    ) -> Dict[str, Any]:
        """应用单个卷积层"""
        # 这里实现具体的卷积逻辑
        # 根据strategy调整处理强度
        processing_intensity = {
            "conservative": 0.3,
            "adaptive": 0.6,
            "aggressive": 0.9
        }.get(strategy, 0.6)
        
        # 模拟卷积处理
        modified_outline = await self._apply_convolution_kernel(
            outline, feedbacks, processing_intensity
        )
        
        score = await self._evaluate_outline_quality(survey.title, modified_outline)
        
        return {
            "layer_index": layer_index,
            "outline": modified_outline,
            "score": score,
            "processing_intensity": processing_intensity
        }

    async def _apply_convolution_kernel(
        self, outline: str, feedbacks: List[Feedback], intensity: float
    ) -> str:
        """应用卷积核"""
        # 简化的卷积操作
        if hasattr(self, 'convolution_kernel') and feedbacks:
            try:
                # 使用实际的卷积核神经元
                result = self.convolution_kernel(outline, feedbacks[:self.receptive_field])
                return result
            except Exception as e:
                logger.warning(f"Convolution kernel error: {e}")
        
        # 回退到简单处理
        return outline

    async def _select_best_outline(
        self, title: str, original: str, results: List[Dict[str, Any]], original_score: float
    ) -> Dict[str, Any]:
        """选择最佳outline"""
        if not results:
            return {
                "outline": original,
                "score": original_score,
                "confidence": 0.5
            }
        
        # 找到得分最高的结果
        best_result = max(results, key=lambda x: x["score"])
        
        # 如果改进不明显，保留原始outline
        if best_result["score"] <= original_score + 0.05:
            return {
                "outline": original,
                "score": original_score,
                "confidence": 0.7
            }
        
        return {
            "outline": best_result["outline"],
            "score": best_result["score"],
            "confidence": 0.9
        }

    async def _apply_residual_modification(
        self, outline: str, instructions: str, context: Dict[str, Any]
    ) -> str:
        """应用残差修改"""
        if hasattr(self, 'modify_neuron'):
            try:
                return self.modify_neuron.forward(outline, instructions, context)
            except Exception as e:
                logger.warning(f"Residual modification error: {e}")
        
        # 简单的文本修改逻辑
        return outline + f"\n\n# 修改说明\n{instructions}"

    async def _apply_complete_modification(
        self, outline: str, instructions: str, context: Dict[str, Any]
    ) -> str:
        """应用完整修改"""
        # 完整重写outline的逻辑
        return f"# 重写大纲\n基于指令: {instructions}\n\n{outline}"

    async def _apply_incremental_modification(
        self, outline: str, instructions: str, context: Dict[str, Any]
    ) -> str:
        """应用增量修改"""
        # 增量修改逻辑
        lines = outline.split('\n')
        modified_lines = []
        
        for line in lines:
            modified_lines.append(line)
            # 根据instructions在适当位置插入内容
            if "增加" in instructions and len(modified_lines) % 3 == 0:
                modified_lines.append(f"  - 补充内容: {instructions}")
        
        return '\n'.join(modified_lines)

    async def _perform_feedback_clustering(
        self, feedback_list: List[Dict[str, Any]], strategy: str, target_clusters: int
    ) -> List[List[Dict[str, Any]]]:
        """执行反馈聚类"""
        if hasattr(self, 'feedback_cluster'):
            try:
                # 使用实际的聚类神经元
                clusters = await self._neural_clustering(feedback_list, target_clusters)
                return clusters
            except Exception as e:
                logger.warning(f"Neural clustering error: {e}")
        
        # 简单的聚类逻辑
        return self._simple_clustering(feedback_list, target_clusters)

    def _simple_clustering(
        self, feedback_list: List[Dict[str, Any]], target_clusters: int
    ) -> List[List[Dict[str, Any]]]:
        """简单聚类实现"""
        if len(feedback_list) <= target_clusters:
            return [[fb] for fb in feedback_list]
        
        # 按内容长度简单分组
        clusters = [[] for _ in range(target_clusters)]
        for i, feedback in enumerate(feedback_list):
            cluster_idx = i % target_clusters
            clusters[cluster_idx].append(feedback)
        
        return [cluster for cluster in clusters if cluster]

    async def _neural_clustering(
        self, feedback_list: List[Dict[str, Any]], target_clusters: int
    ) -> List[List[Dict[str, Any]]]:
        """使用神经网络聚类"""
        # 这里可以实现更复杂的神经网络聚类逻辑
        return self._simple_clustering(feedback_list, target_clusters)

    async def _evaluate_clustering_quality(self, clusters: List[List[Dict[str, Any]]]) -> float:
        """评估聚类质量"""
        if not clusters:
            return 0.0
        
        # 简单的聚类质量评估
        cluster_sizes = [len(cluster) for cluster in clusters]
        size_variance = sum((size - sum(cluster_sizes)/len(cluster_sizes))**2 for size in cluster_sizes)
        
        # 平衡性评分 (越平衡分数越高)
        balance_score = 1.0 / (1.0 + size_variance / len(clusters))
        
        return balance_score

    async def _summarize_cluster(self, cluster: List[Dict[str, Any]]) -> str:
        """总结聚类内容"""
        if not cluster:
            return "空聚类"
        
        # 简单的聚类总结
        content_lengths = [len(str(item.get("content", ""))) for item in cluster]
        avg_length = sum(content_lengths) / len(content_lengths)
        
        return f"包含{len(cluster)}个反馈，平均长度{avg_length:.1f}字符"


# 导出工具包
def get_convolution_tools() -> List[FunctionTool]:
    """获取卷积处理相关的工具列表"""
    toolkit = ConvolutionLayerToolkit({})
    return toolkit.get_tools()
