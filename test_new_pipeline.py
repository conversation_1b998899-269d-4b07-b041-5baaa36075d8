"""
测试新编码管道的功能
"""

import sys
import logging
from pathlib import Path
from argparse import Namespace

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from src.encode.new_encode_pipeline import create_new_encode_pipeline

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_topic_mode():
    """
    测试主题搜索模式
    """
    logger.info("=== Testing Topic Mode ===")
    
    # 创建模拟参数
    args = Namespace(
        topic="machine learning",
        description="Recent advances in machine learning algorithms",
        top_n=5,
        data_num=3,
        parallel_num=1,
        output_file="output/test_topic_result.json"
    )
    
    try:
        # 创建管道
        pipeline = create_new_encode_pipeline(args)
        
        # 运行管道
        result = pipeline.run()
        
        logger.info(f"Topic mode test completed successfully!")
        logger.info(f"Result survey title: {result.title}")
        logger.info(f"Number of papers: {len(result.papers)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Topic mode test failed: {e}")
        return False


def test_file_mode():
    """
    测试文件输入模式
    """
    logger.info("=== Testing File Mode ===")
    
    # 首先创建一个测试文件
    test_file = "test_literature.jsonl"
    create_test_literature_file(test_file)
    
    # 创建模拟参数
    args = Namespace(
        input_file=test_file,
        data_num=2,
        parallel_num=1,
        output_file="output/test_file_result.json"
    )
    
    try:
        # 创建管道
        pipeline = create_new_encode_pipeline(args)
        
        # 运行管道
        result = pipeline.run()
        
        logger.info(f"File mode test completed successfully!")
        logger.info(f"Result survey title: {result.title}")
        logger.info(f"Number of papers: {len(result.papers)}")
        
        # 清理测试文件
        Path(test_file).unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        logger.error(f"File mode test failed: {e}")
        # 清理测试文件
        Path(test_file).unlink(missing_ok=True)
        return False


def create_test_literature_file(filename: str):
    """
    创建测试用的文献文件
    """
    import json
    
    test_papers = [
        {
            "title": "Deep Learning for Natural Language Processing",
            "abstract": "This paper presents a comprehensive survey of deep learning techniques for NLP.",
            "url": "https://example.com/paper1",
            "txt": "Full text of the deep learning NLP paper...",
            "authors": ["Alice Smith", "Bob Johnson"],
            "year": 2023,
            "venue": "ACL 2023"
        },
        {
            "title": "Transformer Models in Computer Vision",
            "abstract": "An analysis of transformer architectures applied to computer vision tasks.",
            "url": "https://example.com/paper2", 
            "txt": "Full text of the transformer vision paper...",
            "authors": ["Carol Davis", "David Wilson"],
            "year": 2022,
            "venue": "ICCV 2022"
        },
        {
            "title": "Reinforcement Learning Applications",
            "abstract": "Recent applications of reinforcement learning in various domains.",
            "url": "https://example.com/paper3",
            "txt": "Full text of the reinforcement learning paper...",
            "authors": ["Eve Brown", "Frank Miller"],
            "year": 2023,
            "venue": "ICML 2023"
        }
    ]
    
    with open(filename, 'w', encoding='utf-8') as f:
        for paper in test_papers:
            f.write(json.dumps(paper, ensure_ascii=False) + '\n')
    
    logger.info(f"Created test literature file: {filename}")


def main():
    """
    运行所有测试
    """
    logger.info("Starting New Encode Pipeline Tests")
    
    # 确保输出目录存在
    Path("output").mkdir(exist_ok=True)
    
    # 运行测试
    topic_success = test_topic_mode()
    file_success = test_file_mode()
    
    # 总结结果
    logger.info("=== Test Summary ===")
    logger.info(f"Topic mode test: {'PASSED' if topic_success else 'FAILED'}")
    logger.info(f"File mode test: {'PASSED' if file_success else 'FAILED'}")
    
    if topic_success and file_success:
        logger.info("All tests PASSED!")
        return 0
    else:
        logger.error("Some tests FAILED!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
