#!/usr/bin/env python3
"""
LLM搜索主机 (LLM Search Host) - 智能搜索协调器
替代原有的LLM_search模块，通过LLM对话选择合适的搜索工具
保持与原有接口完全一致，实现无缝替换

工作流程：
1. Pipeline调用LLM_search方法
2. 启动LLM对话程序
3. LLM分析任务并选择合适的MCP搜索工具
4. 执行搜索并返回结果
"""

import asyncio
import logging
import os
import traceback
from typing import List, Literal, Optional

from .llm_conversation_agent import LLMConversationAgent

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LLM_search:
    
    def __init__(
        self,
        model: str = "gemini-2.0-flash-thinking-exp-01-21",
        infer_type: str = "OpenAI",
        engine: Literal["google", "baidu", "bing"] = "google",
        each_query_result: int = 10,
        filter_date: Optional[str] = None,
        max_workers: int = 10,
    ):

        self.model = model
        self.infer_type = infer_type
        self.engine = engine
        self.each_query_result = each_query_result
        self.filter_date = filter_date
        self.max_workers = max_workers

        self.mcp_client = None
        self.server_config = {  # TODO：后续从config中读入
            "command": "python",
            "args": ["-m", "src.search.llm_search_mcp_server"],
            "env": {
                "PYTHONPATH": ".",
                "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", ""),
                "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY", ""),
                "SERP_API_KEY": os.getenv("SERP_API_KEY", ""),
                "BING_SEARCH_V7_SUBSCRIPTION_KEY": os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY", "")
            }
        }

        self._check_api_keys()
        
        logger.info(f"LLM Search Host initialized with model={model}, engine={engine}")
    
    def _check_api_keys(self):
        llm_keys = ["OPENAI_API_KEY", "GOOGLE_API_KEY"]
        llm_available = any(os.getenv(key) for key in llm_keys)
        search_keys = ["SERP_API_KEY", "BING_SEARCH_V7_SUBSCRIPTION_KEY"]
        search_available = any(os.getenv(key) for key in search_keys)
        if not llm_available:
            logger.warning("No LLM API key found. Query generation may fail.")
        if not search_available:
            raise ValueError(
                "No valid search engine key provided. Please set SERP_API_KEY or BING_SEARCH_V7_SUBSCRIPTION_KEY."
            )
    
    async def _ensure_mcp_connection(self):
        if self.mcp_client is None:
            self.mcp_client = LLMSearchMCPClient(self.server_config)
            await self.mcp_client.connect()
            logger.info("MCP client connected successfully")
    
    async def _cleanup_mcp_connection(self):
        if self.mcp_client:
            await self.mcp_client.disconnect()
            self.mcp_client = None
            logger.info("MCP client disconnected")
    
    def get_queries(self, topic: str, description: str = "") -> List[str]:
        try:
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
            async def _async_get_queries():
                await self._ensure_mcp_connection()
                try:
                    result = await self.mcp_client.generate_search_queries(
                        topic=topic,
                        description=description,
                        model=self.model
                    )
                    return result["queries"]
                finally:
                    await self._cleanup_mcp_connection()
            
            queries = loop.run_until_complete(_async_get_queries())
            logger.info(f"Final count {len(queries)}:\n{queries}")
            return queries
            
        except Exception as e:
            logger.error(f"Error generating queries: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def web_search(self, query: str):

        try:
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            async def _async_web_search():
                await self._ensure_mcp_connection()
                try:
                    result = await self.mcp_client.web_search(
                        queries=[query],
                        topic=query  # 使用查询本身作为主题
                        top_n=self.each_query_result,
                        engine=self.engine
                    )
                    
                    urls = result["urls"]
                    web_snippets = {}
                    for idx, url in enumerate(urls):
                        web_snippets[idx] = {
                            "title": f"Result {idx + 1}",
                            "url": url,
                            "snippet": f"Search result for: {query}",
                        }
                    
                    return web_snippets
                finally:
                    await self._cleanup_mcp_connection()
            
            return loop.run_until_complete(_async_web_search())
            
        except Exception as e:
            logger.error(f"Error in web search for query '{query}': {e}")
            raise
    
    def batch_web_search(self, queries: List[str], topic: str, top_n: int = 20) -> List[str]:

        try:
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            async def _async_batch_search():
                await self._ensure_mcp_connection()
                try:
                    result = await self.mcp_client.web_search(
                        queries=queries,
                        topic=topic,
                        top_n=top_n,
                        engine=self.engine
                    )
                    return result["urls"]
                finally:
                    await self._cleanup_mcp_connection()
            
            urls = loop.run_until_complete(_async_batch_search())
            logger.info(f"Returning top {len(urls)} most relevant URLs.")
            return urls
            
        except Exception as e:
            logger.error(f"Error in batch web search: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def snippet_filter(self, topic: str, snippet: str) -> float:

        try:
            # TODO 相似度矩阵
            logger.info(f"Calculating similarity for topic: {topic}")
            return 75.0 
            
        except Exception as e:
            logger.error(f"Error calculating similarity score: {e}")
            return 0.0

def create_llm_search(
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI",
    engine: Literal["google", "baidu", "bing"] = "google",
    each_query_result: int = 10,
    filter_date: Optional[str] = None,
    max_workers: int = 10,
) -> LLM_search:

    return LLM_search(
        model=model,
        infer_type=infer_type,
        engine=engine,
        each_query_result=each_query_result,
        filter_date=filter_date,
        max_workers=max_workers
    )
