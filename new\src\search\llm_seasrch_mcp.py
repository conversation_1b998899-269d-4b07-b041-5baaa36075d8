
import asyncio
import logging
import os
import traceback
from typing import List, Literal, Optional

from .llm_search_agent import LLMConversationAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LLM_search:

    def __init__(
        self,
        model: str = "gemini-2.0-flash-thinking-exp-01-21",
        infer_type: str = "OpenAI",
        engine: Literal["google", "baidu", "bing"] = "google",
        each_query_result: int = 10,
        filter_date: Optional[str] = None,
        max_workers: int = 10,
    ):

        self.model = model
        self.infer_type = infer_type
        self.engine = engine
        self.each_query_result = each_query_result
        self.filter_date = filter_date
        self.max_workers = max_workers
        
        self.conversation_agent = LLMConversationAgent(
            model=model,
            infer_type=infer_type
        )
        
        self._check_api_keys()
        
        logger.info(f"Intelligent LLM Search initialized with model={model}, engine={engine}")
    
    def _check_api_keys(self):

        llm_keys = ["OPENAI_API_KEY", "GOOGLE_API_KEY"]
        llm_available = any(os.getenv(key) for key in llm_keys)
        

        search_keys = ["SERP_API_KEY", "BING_SEARCH_V7_SUBSCRIPTION_KEY"]
        search_available = any(os.getenv(key) for key in search_keys)
        
        if not llm_available:
            logger.warning("No LLM API key found. Query generation may fail.")
        
        if not search_available:
            raise ValueError(
                "No valid search engine key provided. Please set SERP_API_KEY or BING_SEARCH_V7_SUBSCRIPTION_KEY."
            )
    
    def get_queries(self, topic: str, description: str = "") -> List[str]:

        try:
            # 构建任务描述
            task_description = f"""生成搜索查询任务：
主题: {topic}
描述: {description}
要求: 生成优化的搜索查询列表，用于学术研究

请使用generate_search_queries工具，参数：
- topic: {topic}
- description: {description}
- model: {self.model}"""
            
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                self.conversation_agent.execute_search_task(task_description)
            )
            
            if result["success"]:
                queries = result["tool_result"]["queries"]
                logger.info(f"Final count {len(queries)}:\n{queries}")
                return queries
            else:
                raise Exception(f"Search task failed: {result.get('error', 'Unknown error')}")
            
        except Exception as e:
            logger.error(f"Error generating queries: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def web_search(self, query: str):
        try:
            # 构建任务描述
            task_description = f"""单个查询搜索任务：
查询: {query}
引擎: {self.engine}
结果数量: {self.each_query_result}

请使用web_search工具，参数：
- queries: ["{query}"]
- topic: {query}
- top_n: {self.each_query_result}
- engine: {self.engine}"""
            
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                self.conversation_agent.execute_search_task(task_description)
            )
            
            if result["success"]:
                urls = result["tool_result"]["urls"]
                web_snippets = {}
                for idx, url in enumerate(urls):
                    web_snippets[idx] = {
                        "title": f"Result {idx + 1}",
                        "url": url,
                        "snippet": f"Search result for: {query}",
                    }
                
                return web_snippets
            else:
                raise Exception(f"Search task failed: {result.get('error', 'Unknown error')}")
            
        except Exception as e:
            logger.error(f"Error in web search for query '{query}': {e}")
            raise
    
    def batch_web_search(self, queries: List[str], topic: str, top_n: int = 20) -> List[str]:

        try:
            # 构建任务描述
            queries_str = "\n".join([f"- {q}" for q in queries])
            task_description = f"""批量搜索任务：
主题: {topic}
查询列表:
{queries_str}
引擎: {self.engine}
最终结果数量: {top_n}

请使用web_search工具，参数：
- queries: {queries}
- topic: {topic}
- top_n: {top_n}
- engine: {self.engine}"""
            
            # 创建事件循环（如果不存在）
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 执行智能搜索任务
            result = loop.run_until_complete(
                self.conversation_agent.execute_search_task(task_description)
            )
            
            if result["success"]:
                urls = result["tool_result"]["urls"]
                logger.info(f"Returning top {len(urls)} most relevant URLs.")
                return urls
            else:
                raise Exception(f"Search task failed: {result.get('error', 'Unknown error')}")
            
        except Exception as e:
            logger.error(f"Error in batch web search: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def snippet_filter(self, topic: str, snippet: str) -> float:
        try:
            # TODO:相似度计算
            logger.info(f"Calculating similarity for topic: {topic}")
            return 75.0 
            
        except Exception as e:
            logger.error(f"Error calculating similarity score: {e}")
            return 0.0
    
    def add_search_engine(self, name: str, config: dict):
        # TODO：新加server
        self.conversation_agent.add_search_server(name, config)
        logger.info(f"Added new search engine: {name}")
    
    def list_available_engines(self) -> List[str]:
        return self.conversation_agent.list_available_servers()

def create_llm_search(
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI",
    engine: Literal["google", "baidu", "bing"] = "google",
    each_query_result: int = 10,
    filter_date: Optional[str] = None,
    max_workers: int = 10,
) -> LLM_search:
    return LLM_search(
        model=model,
        infer_type=infer_type,
        engine=engine,
        each_query_result=each_query_result,
        filter_date=filter_date,
        max_workers=max_workers
    )
