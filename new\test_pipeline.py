"""
测试管道功能的简单脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pipeline import run_pipeline_simple
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_simple_pipeline():
    """
    测试简化的管道流程
    """
    print("Testing MapxReduceV3 Pipeline...")
    
    try:
        # 测试参数
        task = "machine learning optimization"
        description = "Research on optimization techniques in machine learning"
        top_n = 5
        output_file = "new/test/test_output.json"
        
        print(f"Task: {task}")
        print(f"Description: {description}")
        print(f"Top N papers: {top_n}")
        print(f"Output file: {output_file}")
        print("-" * 50)
        
        # 运行管道
        surveys = run_pipeline_simple(
            task=task,
            description=description,
            top_n=top_n,
            output_file=output_file
        )

        # 输出结果
        print("Pipeline completed successfully!")
        print(f"Generated {len(surveys)} surveys:")
        for i, survey in enumerate(surveys, 1):
            print(f"  Survey {i}: {survey.title}")
            print(f"    Papers: {len(survey.papers)}")
        print(f"Output saved to: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_individual_components():
    """
    测试各个组件
    """
    print("\nTesting individual components...")
    
    try:
        # 测试analyse接口
        print("1. Testing analyse interface...")
        from src.search.analyse import analyse
        
        task = "test_task"
        result_dir = analyse(task, "test description", top_n=3)
        print(f"   Analyse result: {result_dir}")
        
        # 测试encode_pipeline
        print("2. Testing encode_pipeline...")
        from src.encode.encode_pipeline import encode_pipeline
        
        survey = encode_pipeline(result_dir, task_name=task)
        print(f"   Encode result: Survey '{survey.title}' with {len(survey.papers)} papers")
        
        print("All components tested successfully!")
        return True
        
    except Exception as e:
        print(f"Component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("MapxReduceV3 Pipeline Test")
    print("=" * 50)
    
    # 测试各个组件
    component_success = test_individual_components()
    
    # 测试完整管道
    pipeline_success = test_simple_pipeline()
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"Component tests: {'PASSED' if component_success else 'FAILED'}")
    print(f"Pipeline test: {'PASSED' if pipeline_success else 'FAILED'}")
    
    if component_success and pipeline_success:
        print("All tests PASSED! 🎉")
        sys.exit(0)
    else:
        print("Some tests FAILED! ❌")
        sys.exit(1)
