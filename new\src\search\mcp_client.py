#!/usr/bin/env python3
"""
通用MCP客户端 - 纯粹的通信桥梁
只负责与MCP服务器的连接和基础通信，不包含业务逻辑
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from mcp import ClientSession
from mcp.client.stdio import stdio_client
from mcp.types import CallToolRequest, TextContent, Tool

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MCPClient:
    """
    通用MCP客户端 - 纯粹的通信桥梁
    
    职责：
    1. 建立与MCP服务器的连接
    2. 发送工具调用请求
    3. 接收和解析响应
    4. 管理连接生命周期
    
    不包含任何业务逻辑！
    """
    
    def __init__(self, server_config: Dict[str, Any]):
        """
        初始化MCP客户端
        
        Args:
            server_config: 服务器配置，包含command、args、env等
        """
        self.server_config = server_config
        self.session = None
        self.read_stream = None
        self.write_stream = None
        
    async def connect(self):
        """连接到MCP服务器"""
        try:
            logger.info("Connecting to MCP Server...")
            
            # 创建stdio客户端连接
            params = {
                "command": self.server_config["command"],
                "args": self.server_config.get("args", []),
                "env": self.server_config.get("env", None)
            }
            
            self.read_stream, self.write_stream = await stdio_client(**params).__aenter__()
            self.session = await ClientSession(self.read_stream, self.write_stream).__aenter__()
            
            # 初始化会话
            await self.session.initialize()
            
            logger.info("Successfully connected to MCP server")
            
        except Exception as e:
            logger.error(f"Failed to connect to MCP server: {e}")
            raise
    
    async def disconnect(self):
        """断开与MCP服务器的连接"""
        try:
            if self.session:
                await self.session.__aexit__(None, None, None)
            if self.read_stream and self.write_stream:
                await stdio_client(
                    command=self.server_config["command"],
                    args=self.server_config.get("args", []),
                    env=self.server_config.get("env", None)
                ).__aexit__(None, None, None)
            logger.info("Disconnected from MCP server")
        except Exception as e:
            logger.error(f"Error disconnecting from MCP server: {e}")
    
    async def list_tools(self) -> List[Tool]:
        """列出服务器可用的工具"""
        try:
            if not self.session:
                raise RuntimeError("Not connected to MCP server")
            
            tools_response = await self.session.list_tools()
            logger.info(f"Available tools: {[tool.name for tool in tools_response.tools]}")
            
            return tools_response.tools
            
        except Exception as e:
            logger.error(f"Error listing tools: {e}")
            raise
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """
        调用MCP工具
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具调用的原始结果
        """
        try:
            if not self.session:
                raise RuntimeError("Not connected to MCP server")
            
            logger.info(f"Calling tool: {tool_name}")
            
            result = await self.session.call_tool(
                CallToolRequest(
                    name=tool_name,
                    arguments=arguments
                )
            )
            
            logger.info(f"Tool {tool_name} executed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error calling tool {tool_name}: {e}")
            raise
    
    async def call_tool_and_parse_json(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具并解析JSON响应
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            解析后的JSON数据
        """
        result = await self.call_tool(tool_name, arguments)
        
        # 解析结果
        if result.content and len(result.content) > 0:
            content = result.content[0]
            if isinstance(content, TextContent):
                return json.loads(content.text)
        
        raise ValueError(f"No valid response received from tool {tool_name}")

class MCPClientManager:
    """MCP客户端管理器 - 管理客户端的生命周期"""
    
    def __init__(self, server_config: Dict[str, Any]):
        self.server_config = server_config
        self.client = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.client = MCPClient(self.server_config)
        await self.client.connect()
        return self.client
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.client:
            await self.client.disconnect()

# 便捷函数
async def create_mcp_client(server_config: Dict[str, Any]) -> MCPClient:
    """
    创建并连接MCP客户端
    
    Args:
        server_config: 服务器配置
        
    Returns:
        已连接的MCP客户端实例
    """
    client = MCPClient(server_config)
    await client.connect()
    return client

def load_mcp_config(config_path: str, server_name: str) -> Dict[str, Any]:
    """
    从配置文件加载MCP服务器配置
    
    Args:
        config_path: 配置文件路径
        server_name: 服务器名称
        
    Returns:
        服务器配置字典
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if "servers" not in config or server_name not in config["servers"]:
            raise ValueError(f"Server '{server_name}' not found in config")
        
        return config["servers"][server_name]
        
    except Exception as e:
        logger.error(f"Failed to load MCP config: {e}")
        raise

# 示例使用
async def example_usage():
    """示例用法 - 展示如何使用纯粹的MCP客户端"""
    
    # 方式1: 使用上下文管理器
    server_config = {
        "command": "python",
        "args": ["-m", "src.search.llm_search_mcp_server"],
        "env": {"PYTHONPATH": "."}
    }
    
    async with MCPClientManager(server_config) as client:
        # 列出工具
        tools = await client.list_tools()
        print("Available tools:", [tool.name for tool in tools])
        
        # 调用工具
        result = await client.call_tool_and_parse_json(
            "generate_search_queries",
            {"topic": "machine learning", "description": "AI research"}
        )
        print("Query generation result:", result)
    
    # 方式2: 手动管理连接
    client = await create_mcp_client(server_config)
    try:
        tools = await client.list_tools()
        print("Tools:", [tool.name for tool in tools])
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(example_usage())
