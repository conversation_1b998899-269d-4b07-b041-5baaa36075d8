"""
Intelligent Pipeline Prompts
智能流水线专用提示词
"""

# Pipeline 编排提示词
INTELLIGENT_ORCHESTRATION_PROMPT = """
你是一个智能的数据处理流水线编排专家。请分析当前的调研数据状态，并决定最优的处理策略。

## 当前数据状态
调研标题: {title}
数据摘要数量: {digest_count}
骨架完整度: {skeleton_completeness}
当前处理轮次: {current_cycle}/{max_cycles}
数据质量评分: {data_quality_score}

## 历史处理记录
{processing_history}

## 可选处理动作
1. **group**: 对数据进行智能分组，适用于数据分散、主题不明确的情况
2. **skeleton**: 生成或优化研究大纲，适用于结构不完整或需要重新组织的情况
3. **digest**: 生成或改进摘要内容，适用于内容质量不足或需要深度提炼的情况
4. **refine**: 进行精化处理和迭代优化，适用于已有基础内容但需要提升质量的情况
5. **complete**: 处理完成，输出最终结果

## 决策标准
请基于以下标准做出决策：
- **数据完整性**: 当前数据是否完整覆盖研究主题
- **结构合理性**: 大纲结构是否逻辑清晰、层次分明
- **内容质量**: 摘要内容是否准确、深入、有价值
- **处理效率**: 考虑计算资源和时间成本
- **迭代收益**: 进一步处理是否能带来显著改进

## 输出要求
请以JSON格式输出你的决策：
{{
  "next_action": "选择的动作",
  "confidence": 0.0-1.0,
  "reasoning": "详细的决策理由",
  "parameters": {{
    "priority_areas": ["需要重点关注的领域"],
    "processing_focus": "处理重点",
    "expected_improvement": "预期改进效果",
    "resource_requirements": "资源需求评估"
  }},
  "alternative_actions": ["备选方案"],
  "risk_assessment": "风险评估"
}}
"""

# 智能分组提示词
INTELLIGENT_GROUPING_PROMPT = """
你是一个智能的内容分组专家。请基于内容的语义相似性、主题相关性和逻辑关联性，对给定的数据进行最优分组。

## 输入数据
{dataset_description}

## 分组目标
{grouping_objective}

## 分组原则
1. **语义一致性**: 相似语义内容归为一组
2. **主题连贯性**: 相同主题领域的内容聚集
3. **逻辑关联性**: 有逻辑递进关系的内容相邻
4. **均衡分布**: 各组大小相对均衡，避免极端偏斜
5. **可解释性**: 每个分组都有明确的主题标识

## 智能分析要求
- 自动识别数据中的隐含主题和模式
- 发现内容间的潜在关联关系
- 评估不同分组方案的优劣
- 提供分组合理性的量化指标

## 输出格式
请提供详细的分组方案和分析报告：
{{
  "grouping_strategy": "分组策略说明",
  "groups": [
    {{
      "group_id": "唯一标识",
      "group_name": "组名",
      "theme_description": "主题描述",
      "key_characteristics": ["关键特征"],
      "item_ids": ["包含的数据项ID"],
      "coherence_score": 0.0-1.0,
      "size": 0
    }}
  ],
  "quality_metrics": {{
    "overall_coherence": 0.0-1.0,
    "group_balance": 0.0-1.0,
    "theme_clarity": 0.0-1.0,
    "coverage_completeness": 0.0-1.0
  }},
  "optimization_suggestions": ["改进建议"],
  "confidence": 0.0-1.0
}}
"""

# 智能骨架生成提示词
INTELLIGENT_SKELETON_PROMPT = """
你是一个智能的研究大纲架构师。请基于研究主题和已有数据，设计一个结构化、层次化、逻辑清晰的研究大纲。

## 研究背景
研究主题: {topic}
研究描述: {description}
数据概览: {data_overview}
目标受众: {target_audience}

## 大纲设计原则
1. **层次清晰**: 建立明确的主-次级结构关系
2. **逻辑连贯**: 各部分之间有合理的逻辑递进
3. **覆盖全面**: 充分涵盖研究主题的各个重要方面
4. **深度适宜**: 详细程度符合目标和数据量
5. **实用可操作**: 每个节点都可以具体展开和填充

## 智能设计要求
- 基于数据特征调整大纲结构和深度
- 识别研究中的关键问题和核心议题
- 预测可能的内容缺口和补充方向
- 考虑不同受众的认知习惯和需求

## 输出要求
请提供完整的研究大纲和设计说明：
{{
  "skeleton": {{
    "title": "研究标题",
    "abstract": "研究摘要",
    "structure": [
      {{
        "id": "section_id",
        "level": 1,
        "title": "章节标题", 
        "description": "章节描述",
        "key_points": ["关键要点"],
        "subsections": [
          {{
            "id": "subsection_id",
            "level": 2,
            "title": "子章节标题",
            "description": "子章节描述",
            "estimated_content": "预期内容概述"
          }}
        ]
      }}
    ]
  }},
  "design_rationale": "设计理念和考虑",
  "quality_assessment": {{
    "structural_completeness": 0.0-1.0,
    "logical_coherence": 0.0-1.0,
    "depth_appropriateness": 0.0-1.0,
    "practical_feasibility": 0.0-1.0
  }},
  "potential_improvements": ["潜在改进点"],
  "confidence": 0.0-1.0
}}
"""

# 智能摘要生成提示词
INTELLIGENT_DIGEST_PROMPT = """
你是一个智能的学术摘要生成专家。请基于原始内容和目标大纲，生成高质量、结构化的学术摘要。

## 输入材料
原始内容: {content}
目标大纲: {outline}
研究主题: {topic}
摘要类型: {digest_type}

## 摘要生成标准
1. **准确性**: 准确反映原始内容的核心观点和发现
2. **完整性**: 覆盖原始内容的主要论点和关键信息
3. **结构性**: 按照目标大纲组织内容，保持逻辑清晰
4. **简洁性**: 去除冗余，保持表达精炼有力
5. **学术性**: 使用恰当的学术语言和表达规范

## 智能优化要求
- 自动识别内容中的关键信息和核心论点
- 根据大纲要求调整内容的组织和重点
- 发现原始内容的不足并提出补充建议
- 确保摘要的逻辑一致性和可读性

## 输出要求
请提供高质量的摘要和详细分析：
{{
  "digest_content": "生成的摘要内容",
  "content_structure": {{
    "main_points": ["主要论点"],
    "supporting_evidence": ["支撑证据"], 
    "conclusions": ["结论要点"],
    "methodology": "研究方法概述"
  }},
  "quality_metrics": {{
    "accuracy": 0.0-1.0,
    "completeness": 0.0-1.0,
    "conciseness": 0.0-1.0,
    "academic_rigor": 0.0-1.0
  }},
  "improvement_suggestions": ["改进建议"],
  "content_gaps": ["内容缺口"],
  "confidence": 0.0-1.0
}}
"""

# 智能精化提示词
INTELLIGENT_REFINEMENT_PROMPT = """
你是一个智能的内容精化专家。请分析当前的研究内容，识别问题和不足，并提供具体的改进方案。

## 当前状态
研究标题: {title}
当前大纲: {current_skeleton}
摘要内容: {current_digests}
处理轮次: {iteration_count}

## 精化评估维度
1. **内容质量**: 信息的准确性、深度和价值
2. **结构合理性**: 组织逻辑和层次关系
3. **完整性**: 内容覆盖的全面程度
4. **一致性**: 不同部分间的协调统一
5. **可读性**: 表达的清晰度和流畅性

## 智能诊断要求
- 识别当前内容的主要问题和薄弱环节
- 分析不同部分的质量差异和改进潜力
- 评估继续精化的收益和必要性
- 提供具体可行的改进建议

## 输出要求
请提供全面的精化分析和建议：
{{
  "current_assessment": {{
    "overall_quality": 0.0-1.0,
    "content_quality": 0.0-1.0,
    "structural_quality": 0.0-1.0,
    "completeness": 0.0-1.0,
    "consistency": 0.0-1.0
  }},
  "identified_issues": [
    {{
      "issue_type": "问题类型",
      "description": "问题描述",
      "severity": "严重程度",
      "affected_areas": ["影响区域"]
    }}
  ],
  "refinement_recommendations": [
    {{
      "action": "建议行动",
      "priority": "优先级",
      "expected_improvement": "预期改进",
      "implementation_steps": ["实施步骤"]
    }}
  ],
  "iteration_necessity": {{
    "should_continue": true/false,
    "reasoning": "继续/停止的理由",
    "expected_benefits": "预期收益",
    "resource_cost": "资源成本"
  }},
  "confidence": 0.0-1.0
}}
"""

# 质量评估提示词
QUALITY_ASSESSMENT_PROMPT = """
你是一个智能的内容质量评估专家。请对给定的研究内容进行全面的质量评估，并提供客观的评分和建议。

## 评估对象
{content_to_assess}

## 评估标准
1. **学术严谨性**: 内容的科学性和准确性
2. **逻辑一致性**: 论证的逻辑性和连贯性
3. **信息完整性**: 信息的全面性和充分性
4. **创新价值**: 内容的新颖性和贡献度
5. **表达质量**: 语言的清晰性和专业性

## 评估要求
- 提供客观、量化的质量评分
- 识别内容的优势和不足
- 对比行业或学术标准
- 提供具体的改进方向

## 输出格式
{{
  "quality_scores": {{
    "academic_rigor": 0.0-1.0,
    "logical_consistency": 0.0-1.0,
    "information_completeness": 0.0-1.0,
    "innovation_value": 0.0-1.0,
    "expression_quality": 0.0-1.0,
    "overall_score": 0.0-1.0
  }},
  "detailed_analysis": {{
    "strengths": ["优势点"],
    "weaknesses": ["不足点"],
    "improvement_areas": ["改进领域"],
    "benchmark_comparison": "与标准的对比"
  }},
  "actionable_recommendations": [
    {{
      "recommendation": "具体建议",
      "impact": "预期影响",
      "difficulty": "实施难度"
    }}
  ],
  "confidence": 0.0-1.0
}}
"""
