import os
import asyncio
import logging
import argparse
from typing import List, Dict, Any, Optional

from search_toolkits import SearchQueryToolkit, WebSearchToolkit, RelevanceToolkit
from mcp import MCPServer

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@MCPServer()
class IntegratedSearchServer:
    """集成的搜索服务器，提供完整的搜索流程"""
    
    def __init__(self, 
                 model: str = "claude-3-5-haiku-20241022",
                 infer_type: str = "OpenAI",
                 engine: str = "google", 
                 each_query_result: int = 10,
                 max_workers: int = 10):
        """初始化集成搜索服务器
        
        Args:
            model: 使用的LLM模型
            infer_type: 推理类型
            engine: 搜索引擎类型
            each_query_result: 每个查询返回的结果数量
            max_workers: 并行处理的最大工作线程数
        """
        # 初始化各个工具包
        self.query_toolkit = SearchQueryToolkit(model=model, infer_type=infer_type)
        self.search_toolkit = WebSearchToolkit(engine=engine, each_query_result=each_query_result)
        self.relevance_toolkit = RelevanceToolkit(model=model, infer_type=infer_type, max_workers=max_workers)
        logger.info("Integrated search server initialized")
        
    async def execute_search(self, topic: str, description: str = "", top_n: int = 20) -> List[Dict[str, Any]]:
        """执行完整的搜索流程
        
        Args:
            topic: 研究主题
            description: 主题描述（可选）
            top_n: 返回的最相关结果数量
            
        Returns:
            list: 按相关性排序的前N个搜索结果
        """
        logger.info(f"Starting integrated search for topic: {topic}")
        
        # 1. 生成搜索查询
        logger.info("Step 1: Generating search queries")
        queries = await self.query_toolkit.generate_search_queries(topic=topic, description=description)
        logger.info(f"Generated {len(queries)} search queries")
        
        # 2. 执行网络搜索
        logger.info("Step 2: Executing web search")
        search_results = await self.search_toolkit.batch_search(queries=queries)
        logger.info(f"Retrieved search results for {len(search_results)} queries")
        
        # 3. 按相关性过滤结果
        logger.info("Step 3: Filtering results by relevance")
        filtered_results = await self.relevance_toolkit.filter_results_by_relevance(
            topic=topic, 
            search_results=search_results,
            top_n=top_n
        )
        
        logger.info(f"Search complete. Found {len(filtered_results)} relevant results")
        return filtered_results
    
    async def get_search_queries(self, topic: str, description: str = "") -> List[str]:
        """仅生成搜索查询
        
        Args:
            topic: 研究主题
            description: 主题描述（可选）
            
        Returns:
            list: 优化的搜索查询列表
        """
        return await self.query_toolkit.generate_search_queries(topic=topic, description=description)
    
    async def search_with_queries(self, queries: List[str]) -> Dict[str, Dict[str, Any]]:
        """使用已有查询执行搜索
        
        Args:
            queries: 查询列表
            
        Returns:
            dict: 搜索结果
        """
        return await self.search_toolkit.batch_search(queries=queries)
    
    async def filter_by_relevance(self, topic: str, search_results: Dict[str, Dict[str, Any]], top_n: int = 20) -> List[Dict[str, Any]]:
        """过滤搜索结果
        
        Args:
            topic: 研究主题
            search_results: 搜索结果
            top_n: 返回的结果数量
            
        Returns:
            list: 按相关性排序的结果
        """
        return await self.relevance_toolkit.filter_results_by_relevance(
            topic=topic,
            search_results=search_results,
            top_n=top_n
        )

def parse_args():
    parser = argparse.ArgumentParser(description="启动集成搜索MCP服务器")
    parser.add_argument("--model", type=str, default="claude-3-5-haiku-20241022", 
                        help="用于生成查询和评估相关性的LLM模型")
    parser.add_argument("--infer-type", type=str, default="OpenAI", 
                        help="推理类型")
    parser.add_argument("--engine", type=str, choices=["google", "baidu", "bing"], 
                        default="google", help="搜索引擎")
    parser.add_argument("--results-per-query", type=int, default=10, 
                        help="每个查询返回的结果数量")
    parser.add_argument("--max-workers", type=int, default=10, 
                        help="并行处理的最大工作线程数")
    parser.add_argument("--port", type=int, default=8080, 
                        help="服务器端口")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # 创建服务器实例
    server = IntegratedSearchServer(
        model=args.model,
        infer_type=args.infer_type,
        engine=args.engine,
        each_query_result=args.results_per_query,
        max_workers=args.max_workers
    )
    
    # 启动服务器
    server.mcp.run_server(host="0.0.0.0", port=args.port)
    logger.info(f"Integrated search server started on port {args.port}")

if __name__ == "__main__":
    main()