"""
Pipeline Runner
新编码管道的运行器和示例
"""

import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.args import parse_args
from src.encode.new_encode_pipeline import create_new_encode_pipeline

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """
    主函数：运行新的编码管道
    """
    try:
        # 解析命令行参数
        args = parse_args()
        
        logger.info("Starting New Encode Pipeline")
        logger.info(f"Arguments: {vars(args)}")
        
        # 创建管道实例
        pipeline = create_new_encode_pipeline(args)
        
        # 运行管道
        result_survey = pipeline.run()
        
        # 输出结果信息
        logger.info(f"Pipeline completed successfully!")
        logger.info(f"Survey title: {result_survey.title}")
        logger.info(f"Number of papers: {len(result_survey.papers)}")
        
        # 可选：保存结果
        if hasattr(args, 'output_file') and args.output_file:
            save_survey_result(result_survey, args.output_file)
        
    except Exception as e:
        logger.error(f"Pipeline execution failed: {e}")
        raise


def save_survey_result(survey, output_file: str):
    """
    保存Survey结果到文件
    
    Args:
        survey: Survey对象
        output_file: 输出文件路径
    """
    try:
        import json
        
        # 转换为字典格式
        survey_dict = survey.to_dict()
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(survey_dict, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Survey result saved to: {output_file}")
        
    except Exception as e:
        logger.error(f"Error saving survey result: {e}")
        raise


if __name__ == "__main__":
    main()
