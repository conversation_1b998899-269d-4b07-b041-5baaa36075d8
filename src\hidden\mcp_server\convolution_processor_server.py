#!/usr/bin/env python3
"""
Convolution MCP Server - 卷积层处理服务器
提供智能的outline优化和feedback处理能力
"""

import json
import logging
import asyncio
from typing import Dict, Any, List
from mcp.server.fastmcp import FastMCP

from src.hidden.convolution_block.convolution_mcp_toolkit import ConvolutionLayerToolkit

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastMCP服务器实例
mcp = FastMCP("Convolution Layer Processor")

# 全局工具包实例
toolkit = None

@mcp.tool()
async def apply_convolution_layer(
    survey_data: dict,
    feedback_list: list,
    original_outline: str,
    processing_strategy: str = "adaptive"
) -> dict:
    """应用卷积层处理来优化outline
    
    Args:
        survey_data: 调研数据字典
        feedback_list: 反馈建议列表
        original_outline: 原始大纲文本
        processing_strategy: 处理策略 (adaptive/aggressive/conservative)
    
    Returns:
        dict: 包含优化后outline和质量评估的结果
    """
    global toolkit
    if toolkit is None:
        # 默认配置
        config = {
            "convolution_layer": 3,
            "receptive_field": 5,
            "result_num": 10,
            "top_k": 3,
            "modify": {"model": "gpt-3.5-turbo", "infer_type": "chat"},
            "eval": {"model": "gpt-3.5-turbo", "infer_type": "chat"},
            "kernel": {"model": "gpt-3.5-turbo", "infer_type": "chat"},
            "cluster": {"model": "gpt-3.5-turbo", "infer_type": "chat"}
        }
        toolkit = ConvolutionLayerToolkit(config)
    
    return await toolkit.apply_convolution_layer(
        survey_data, feedback_list, original_outline, processing_strategy
    )

@mcp.tool()
async def evaluate_outline_quality(
    survey_title: str,
    outline: str,
    evaluation_criteria: list = None
) -> dict:
    """评估outline质量
    
    Args:
        survey_title: 调研标题
        outline: 待评估的大纲
        evaluation_criteria: 评估标准列表
    
    Returns:
        dict: 质量评估结果
    """
    global toolkit
    if toolkit is None:
        config = {
            "eval": {"model": "gpt-3.5-turbo", "infer_type": "chat"}
        }
        toolkit = ConvolutionLayerToolkit(config)
    
    return await toolkit.evaluate_outline_quality(
        survey_title, outline, evaluation_criteria
    )

@mcp.tool()
async def modify_outline_intelligently(
    current_outline: str,
    modification_instructions: str,
    survey_context: dict,
    modification_mode: str = "residual"
) -> dict:
    """智能修改outline
    
    Args:
        current_outline: 当前大纲
        modification_instructions: 修改指令
        survey_context: 调研上下文
        modification_mode: 修改模式 (residual/complete/incremental)
    
    Returns:
        dict: 修改结果
    """
    global toolkit
    if toolkit is None:
        config = {
            "modify": {"model": "gpt-3.5-turbo", "infer_type": "chat"}
        }
        toolkit = ConvolutionLayerToolkit(config)
    
    return await toolkit.modify_outline_intelligently(
        current_outline, modification_instructions, survey_context, modification_mode
    )

@mcp.tool()
async def cluster_feedback_intelligently(
    feedback_list: list,
    clustering_strategy: str = "semantic",
    target_clusters: int = None
) -> dict:
    """智能聚类反馈信息
    
    Args:
        feedback_list: 反馈列表
        clustering_strategy: 聚类策略 (semantic/topic/priority)
        target_clusters: 目标聚类数量
    
    Returns:
        dict: 聚类结果
    """
    global toolkit
    if toolkit is None:
        config = {
            "cluster": {"model": "gpt-3.5-turbo", "infer_type": "chat"}
        }
        toolkit = ConvolutionLayerToolkit(config)
    
    return await toolkit.cluster_feedback_intelligently(
        feedback_list, clustering_strategy, target_clusters
    )

@mcp.tool()
async def optimize_convolution_parameters(
    survey_characteristics: dict,
    performance_requirements: dict
) -> dict:
    """优化卷积参数
    
    Args:
        survey_characteristics: 调研特征 (复杂度、规模等)
        performance_requirements: 性能要求 (质量、速度等)
    
    Returns:
        dict: 优化后的参数配置
    """
    try:
        # 基于调研特征和性能要求优化参数
        complexity = survey_characteristics.get("complexity", "medium")
        scale = survey_characteristics.get("scale", "medium")
        
        quality_priority = performance_requirements.get("quality_priority", 0.7)
        speed_priority = performance_requirements.get("speed_priority", 0.3)
        
        # 参数优化逻辑
        if complexity == "high":
            convolution_layer = max(3, int(5 * quality_priority))
            receptive_field = max(5, int(8 * quality_priority))
        elif complexity == "low":
            convolution_layer = max(1, int(3 * quality_priority))
            receptive_field = max(3, int(5 * quality_priority))
        else:  # medium
            convolution_layer = max(2, int(4 * quality_priority))
            receptive_field = max(4, int(6 * quality_priority))
        
        # 速度优化
        if speed_priority > 0.7:
            convolution_layer = max(1, convolution_layer - 1)
            receptive_field = max(3, receptive_field - 1)
        
        result_num = min(20, max(5, int(15 * quality_priority)))
        top_k = min(5, max(2, int(4 * quality_priority)))
        
        optimized_config = {
            "convolution_layer": convolution_layer,
            "receptive_field": receptive_field,
            "result_num": result_num,
            "top_k": top_k,
            "optimization_rationale": {
                "complexity_factor": complexity,
                "scale_factor": scale,
                "quality_weight": quality_priority,
                "speed_weight": speed_priority
            }
        }
        
        return {
            "success": True,
            "optimized_config": optimized_config,
            "expected_performance": {
                "processing_time": "fast" if speed_priority > 0.6 else "medium",
                "quality_level": "high" if quality_priority > 0.7 else "medium"
            }
        }
        
    except Exception as e:
        logger.error(f"Error optimizing convolution parameters: {e}")
        return {
            "success": False,
            "error": str(e),
            "default_config": {
                "convolution_layer": 3,
                "receptive_field": 5,
                "result_num": 10,
                "top_k": 3
            }
        }

@mcp.tool()
async def analyze_convolution_performance(
    processing_history: list,
    performance_metrics: dict
) -> dict:
    """分析卷积处理性能
    
    Args:
        processing_history: 处理历史记录
        performance_metrics: 性能指标
    
    Returns:
        dict: 性能分析报告
    """
    try:
        if not processing_history:
            return {
                "analysis": "no_data",
                "message": "No processing history available for analysis"
            }
        
        # 性能分析
        total_processes = len(processing_history)
        successful_processes = sum(1 for p in processing_history if p.get("success", False))
        success_rate = successful_processes / total_processes if total_processes > 0 else 0
        
        # 质量改进分析
        quality_improvements = [
            p.get("improvement", 0) for p in processing_history 
            if p.get("improvement") is not None
        ]
        
        avg_improvement = sum(quality_improvements) / len(quality_improvements) if quality_improvements else 0
        
        # 处理时间分析
        processing_times = [
            p.get("processing_time", 0) for p in processing_history 
            if p.get("processing_time") is not None
        ]
        
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # 生成建议
        recommendations = []
        
        if success_rate < 0.8:
            recommendations.append("建议检查输入数据质量和参数配置")
        
        if avg_improvement < 0.1:
            recommendations.append("建议增加卷积层数或调整处理策略")
        
        if avg_processing_time > 30:  # 秒
            recommendations.append("建议优化参数以提高处理速度")
        
        return {
            "analysis_summary": {
                "total_processes": total_processes,
                "success_rate": success_rate,
                "average_quality_improvement": avg_improvement,
                "average_processing_time": avg_processing_time
            },
            "performance_trends": {
                "quality_trend": "improving" if avg_improvement > 0.1 else "stable",
                "speed_trend": "fast" if avg_processing_time < 10 else "moderate"
            },
            "recommendations": recommendations,
            "overall_rating": "excellent" if success_rate > 0.9 and avg_improvement > 0.2 else "good"
        }
        
    except Exception as e:
        logger.error(f"Error analyzing convolution performance: {e}")
        return {
            "analysis": "error",
            "error": str(e)
        }

if __name__ == "__main__":
    # 运行MCP服务器
    mcp.run()
