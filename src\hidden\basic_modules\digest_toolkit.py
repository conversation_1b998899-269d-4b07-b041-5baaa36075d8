import re
import logging
from typing import List, Dict, Any, Optional

from tenacity import retry, stop_after_attempt, after_log, retry_if_exception_type
from camel.toolkits import BaseToolkit, FunctionTool

from request import RequestWrapper
from src.base_method.data import Dataset
from src.data_structure import Digest, Survey
from src.exceptions import (
    BibkeyNotFoundError,
    StructureNotCorrespondingError,
    MdNotFoundError,
)
from src.utils.process_str import (
    str2list,
    list2str,
    remove_illegal_bibkeys,
    parse_md_content,
)
from src.prompts import SINGLE_DIGEST_PROMPT

logger = logging.getLogger(__name__)


class DigestManagerToolkit(BaseToolkit):
    """管理整个调研的摘要生成工作流程"""

    def __init__(self, timeout: Optional[float] = None):
        """初始化摘要管理工具包
        
        Args:
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        logger.info("DigestManagerToolkit initialized")

    async def process_survey_digests(
        self, 
        survey_dict: Dict[str, Any],
        single_digest_module_url: str,
        merge_digest_url: str
    ) -> Dict[str, Any]:
        """处理整个调研的摘要生成流程
        
        Args:
            survey_dict: 调研字典，包含骨架和摘要集合
            single_digest_module_url: SingleDigestModule服务URL
            merge_digest_url: MergeDigest服务URL
            
        Returns:
            Dict[str, Any]: 处理后的调研字典
        """
        # 导入客户端工具
        from mcp_toolkit import MCPClient
        
        # 恢复Survey对象
        survey = Survey.from_dict(survey_dict)
        outline = survey.skeleton
        
        logger.info(f"Processing survey: {survey.title} with {len(survey.digests)} digest collections")
        
        # 创建客户端连接
        single_module_client = MCPClient(single_digest_module_url)
        merge_client = MCPClient(merge_digest_url)
        
        try:
            # 连接服务
            await single_module_client.connect()
            await merge_client.connect()
            
            # 获取工具
            process_digest_tool = next(
                tool for tool in single_module_client.get_tools() 
                if tool.get_function_name() == "process_digest_collection"
            )
            
            # 处理每个摘要集合
            for digest_key, digest in survey.digests.items():
                logger.info(f"Processing digest collection: {digest_key}")
                result = await process_digest_tool.async_call(
                    digest_dict=digest.to_dict(),
                    outline=outline.to_dict(),
                    merge_digest_url=merge_digest_url
                )
                
                if result.get("success", False):
                    # 更新调研中的摘要
                    updated_digest = Digest.from_dict(result["digest"])
                    survey.digests[digest_key] = updated_digest
                    logger.info(f"Successfully updated digest collection: {digest_key}")
                else:
                    logger.warning(f"Failed to process digest collection: {digest_key}")
                    
            logger.info(f"Completed processing all digest collections for survey: {survey.title}")
            return survey.to_dict()
            
        finally:
            # 关闭客户端连接
            await single_module_client.disconnect()
            await merge_client.disconnect()

    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.process_survey_digests)
        ]


class SingleDigestModuleToolkit(BaseToolkit):
    """处理单个摘要集合的工具包"""

    def __init__(
        self,
        model: str = "gpt-4",
        infer_type: str = "OpenAI",
        timeout: Optional[float] = None
    ):
        """初始化单个摘要集合处理工具包
        
        Args:
            model: 使用的LLM模型
            infer_type: 推理类型
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.model = model
        self.infer_type = infer_type
        logger.info(f"SingleDigestModuleToolkit initialized with model: {model}")

    async def process_digest_collection(
        self,
        digest_dict: Dict[str, Any],
        outline: Dict[str, Any],
        merge_digest_url: str
    ) -> Dict[str, Any]:
        """处理一个摘要集合，生成所有论文的摘要并合并
        
        Args:
            digest_dict: 摘要集合字典
            outline: 大纲字典
            merge_digest_url: 合并摘要服务URL
            
        Returns:
            Dict[str, Any]: 处理后的摘要集合
        """
        from mcp_toolkit import MCPClient
        
        # 恢复Digest对象
        digest = Digest.from_dict(digest_dict)
        topic = digest.survey_title
        
        # 获取论文信息
        paper_infos = digest.get_paper_infos()
        for paper_info in paper_infos:
            paper_info["content"] = paper_info.get("origin_content", "")
            
        bibkeys = list2str(digest.bibkeys)
        logger.info(f"Processing digest collection with {len(paper_infos)} papers: {bibkeys}")
        
        # 连接到SingleDigestNeuron服务
        neuron_client = MCPClient(f"http://localhost:8094/mcp")  # 假设运行在此端口
        merge_client = MCPClient(merge_digest_url)
        
        try:
            # 连接服务
            await neuron_client.connect()
            await merge_client.connect()
            
            # 获取工具
            generate_tool = next(
                tool for tool in neuron_client.get_tools()
                if tool.get_function_name() == "generate_single_digest"
            )
            
            merge_tool = next(
                tool for tool in merge_client.get_tools()
                if tool.get_function_name() == "merge_digests"
            )
            
            # 为每篇论文生成摘要
            digest_results = []
            for paper_info in paper_infos:
                logger.info(f"Generating digest for paper: {paper_info['bibkey']}")
                result = await generate_tool.async_call(
                    paper_info=paper_info,
                    outline_dict=outline,
                    survey_title=topic,
                    bibkeys=digest.bibkeys
                )
                
                if result.get("success", False):
                    digest_results.append(result)
                    
            # 筛选有效的摘要
            valid_results = [result for result in digest_results if result.get("success", False)]
            
            if not valid_results:
                logger.warning(f"No valid digests generated for collection: {bibkeys}")
                return {"success": False, "error": "No valid digests were generated"}
            
            # 合并摘要
            merged_result = await merge_tool.async_call(
                digest_dicts=valid_results,
                outline_dict=outline,
                origin_digest_dict=digest_dict
            )
            
            logger.info(f"Completed processing digest collection: {bibkeys}")
            return merged_result
            
        finally:
            # 关闭客户端连接
            await neuron_client.disconnect()
            await merge_client.disconnect()

    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.process_digest_collection)
        ]


class SingleDigestNeuronToolkit(BaseToolkit):
    """用于生成单个论文摘要的工具包"""

    def __init__(
        self, 
        model: str = "gpt-4",
        infer_type: str = "OpenAI",
        timeout: Optional[float] = None
    ):
        """初始化单篇论文摘要生成工具包
        
        Args:
            model: 使用的LLM模型
            infer_type: 推理类型
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.prompt = SINGLE_DIGEST_PROMPT
        self.request_pool = RequestWrapper(model=model, infer_type=infer_type)
        logger.info(f"SingleDigestNeuronToolkit initialized with model: {model}")

    @retry(
        stop=stop_after_attempt(10),
        after=after_log(logger, logging.WARNING),
        retry=retry_if_exception_type(
            (
                BibkeyNotFoundError,
                StructureNotCorrespondingError,
                MdNotFoundError,
                IndexError,
                ValueError,
            )
        ),
    )
    async def generate_single_digest(
        self, 
        paper_info: Dict[str, Any], 
        outline_dict: Dict[str, Any],
        survey_title: str,
        bibkeys: List[str] = None
    ) -> Dict[str, Any]:
        """为单篇论文生成摘要
        
        Args:
            paper_info: 包含论文信息的字典
            outline_dict: 大纲字典
            survey_title: 调研标题
            bibkeys: 论文引用关系列表（可选）
            
        Returns:
            Dict[str, Any]: 包含摘要内容的字典
        """
        # 恢复对象结构
        from src.data_structure import Skeleton
        outline = Skeleton.from_dict(outline_dict)
        new_digest = Digest(bibkeys or [], survey_title)
        
        # 准备输入数据
        outline_content = outline.all_skeleton(construction=True, with_index=True)
        if bibkeys:
            outline_content = remove_illegal_bibkeys(
                outline_content, bibkeys, raise_warning=False
            )
        outline_example = outline.all_skeleton(
            with_digest_placeholder=True, with_index=True
        )

        paper_bibkey = paper_info["bibkey"]
        paper_content = paper_info.get("content", "")
        if not paper_content and "origin_content" in paper_info:
            paper_content = paper_info["origin_content"]
            
        paper_content = paper_content.replace("#", "")
        
        # 构建提示
        prompt = self.prompt.format(
            survey_title=survey_title,
            paper_bibkey=f"{paper_bibkey}",
            paper_content=paper_content,
            survey_outline=outline_content,
            outline_example=outline_example,
        )
        
        new_digest.failure_count = 0
        result = ""
        
        try:
            # 获取LLM响应
            result = self.request_pool.completion(prompt)
            result = result.replace("['BIBKEY']", f"['{paper_bibkey}']")
            result = result.replace("[BIBKEY]", f"['{paper_bibkey}']")
            logger.info(f"Single Digest Generate Finished: {paper_bibkey}")
            
            # 解析结果
            new_digest.paper_infos = [paper_info]
            new_digest.parse_suggestion(result, paper_bibkey)
            new_digest = new_digest.parse_raw_digest(result, outline)
            
            # 转换为可序列化的结构
            return {
                "digest": new_digest.to_dict(),
                "bibkey": paper_bibkey,
                "success": True
            }
            
        except Exception as e:
            new_digest.failure_count += 1
            
            # 如果内容太长，尝试截断
            content_len = int(len(paper_content) * 0.5)
            paper_info["content"] = paper_content[:int(content_len)]
            
            if new_digest.failure_count >= 5:
                logger.warning(
                    f"Single Digest Generate Failed: {paper_bibkey}, Error: {e}, \nprompt: {prompt}, \nresult: {result}"
                )
                
            if new_digest.failure_count >= 9:
                # 最大重试次数后返回空摘要
                empty_digest = new_digest.parse_raw_digest(
                    f"```markdown\n{outline.all_skeleton(with_index=True)}\n```", 
                    outline
                )
                logger.warning(
                    f"Single Digest Generate Failed, return empty: {paper_bibkey}"
                )
                return {
                    "digest": empty_digest.to_dict(),
                    "bibkey": paper_bibkey,
                    "success": False,
                    "error": str(e)
                }
                
            # 重新抛出异常以便retry装饰器捕获
            raise e

    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.generate_single_digest)
        ]


class MergeDigestToolkit(BaseToolkit):
    """用于合并多个论文摘要的工具包"""

    def __init__(
        self, 
        model: str = "gpt-4",
        infer_type: str = "OpenAI",
        timeout: Optional[float] = None
    ):
        """初始化摘要合并工具包
        
        Args:
            model: 使用的LLM模型
            infer_type: 推理类型
            timeout: 操作超时时间（秒）
        """
        super().__init__(timeout=timeout)
        self.request_pool = RequestWrapper(model=model, infer_type=infer_type)
        logger.info(f"MergeDigestToolkit initialized with model: {model}")

    @retry(
        stop=stop_after_attempt(15),
        after=after_log(logger, logging.WARNING),
        retry=retry_if_exception_type(
            (
                BibkeyNotFoundError,
                StructureNotCorrespondingError,
                MdNotFoundError,
                IndexError,
                ValueError,
            )
        ),
    )
    async def merge_digests(
        self, 
        digest_dicts: List[Dict[str, Any]], 
        outline_dict: Dict[str, Any],
        origin_digest_dict: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """合并多个摘要生成一个完整的摘要
        
        Args:
            digest_dicts: 摘要字典列表
            outline_dict: 大纲字典
            origin_digest_dict: 原始摘要字典（可选）
            
        Returns:
            Dict[str, Any]: 合并后的摘要字典
        """
        # 恢复对象结构
        from src.data_structure import Skeleton, Digest
        
        outline = Skeleton.from_dict(outline_dict)
        
        # 恢复Digest对象
        digests = []
        for d in digest_dicts:
            if d.get("success", False) and "digest" in d:
                try:
                    digest = Digest.from_dict(d["digest"])
                    digests.append(digest)
                except Exception as e:
                    logger.error(f"Failed to parse digest: {e}")
        
        if not digests:
            logger.warning("No valid digests to merge")
            return {"success": False, "error": "No valid digests to merge"}
        
        # 获取原始摘要（如果有）
        origin_digest = None
        if origin_digest_dict:
            try:
                origin_digest = Digest.from_dict(origin_digest_dict)
            except Exception as e:
                logger.error(f"Failed to parse origin digest: {e}")
        
        # 从多个摘要创建新摘要
        new_digest = Digest.from_multiple_digests(digests, outline)
        
        # 合并每个部分的描述
        for i, section in enumerate(new_digest.root.all_section):
            section.description = ""
            descriptions = []
            for digest in digests:
                if i < len(digest.root.all_section):
                    d_section = digest.root.all_section[i]
                    if d_section.description:
                        descriptions.append(
                            f"Paper bibkey: [{''.join(digest.bibkeys)}]\nDigest: \n{d_section.description}"
                        )
            section.description = "--------------------\n".join(descriptions)
        
        # 如果有原始摘要，复制一些额外信息
        if origin_digest:
            new_digest.survey_title = origin_digest.survey_title
            new_digest.suggestions = origin_digest.suggestions.copy() if hasattr(origin_digest, "suggestions") else {}
            
        # 返回合并的摘要
        bibkeys = []
        for digest in digests:
            bibkeys.extend(digest.bibkeys)
            
        logger.info(f"Merged {len(digests)} digests successfully")
        return {
            "digest": new_digest.to_dict(),
            "bibkeys": bibkeys,
            "success": True
        }

    def get_tools(self) -> List[FunctionTool]:
        """返回工具包中的函数工具列表"""
        return [
            FunctionTool(self.merge_digests)
        ]