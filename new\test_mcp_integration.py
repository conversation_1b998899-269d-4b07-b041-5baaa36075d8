#!/usr/bin/env python3
"""
MCP集成测试脚本
测试MCP版本的LLM搜索是否能完全替代原有的LLM_search模块
"""

import asyncio
import json
import logging
import os
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_interface_compatibility():
    """测试接口兼容性"""
    print("=" * 60)
    print("测试1: 接口兼容性测试")
    print("=" * 60)
    
    try:
        # 测试导入
        from src.search.llm_search_host import LLM_search
        print("✅ 成功导入MCP版本的LLM_search")
        
        # 测试初始化（与原有接口相同）
        retriever = LLM_search(
            model='gemini-2.0-flash-thinking-exp-01-21',
            infer_type="OpenAI",
            engine='google',
            each_query_result=10
        )
        print("✅ 成功初始化LLM_search实例")
        
        # 检查方法是否存在
        required_methods = ['get_queries', 'web_search', 'batch_web_search', 'snippet_filter']
        for method in required_methods:
            if hasattr(retriever, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 接口兼容性测试失败: {e}")
        return False

def test_query_generation():
    """测试查询生成功能"""
    print("\n" + "=" * 60)
    print("测试2: 查询生成功能测试")
    print("=" * 60)
    
    try:
        # 检查API密钥
        if not os.getenv("OPENAI_API_KEY") and not os.getenv("GOOGLE_API_KEY"):
            print("⚠️  警告: 未设置LLM API密钥，跳过查询生成测试")
            return False
        
        from src.search.llm_search_host import LLM_search
        
        retriever = LLM_search(
            model='gemini-2.0-flash-thinking-exp-01-21',
            infer_type="OpenAI",
            engine='google',
            each_query_result=10
        )
        
        # 测试查询生成
        start_time = time.time()
        queries = retriever.get_queries(
            topic="机器学习优化算法",
            description="研究机器学习中的优化技术和算法"
        )
        end_time = time.time()
        
        print(f"✅ 查询生成成功，耗时: {end_time - start_time:.2f}秒")
        print(f"  生成查询数量: {len(queries)}")
        print("  生成的查询:")
        for i, query in enumerate(queries, 1):
            print(f"    {i}. {query}")
        
        # 验证返回类型
        if isinstance(queries, list) and all(isinstance(q, str) for q in queries):
            print("✅ 返回类型正确 (List[str])")
        else:
            print("❌ 返回类型错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 查询生成测试失败: {e}")
        return False

def test_batch_search():
    """测试批量搜索功能"""
    print("\n" + "=" * 60)
    print("测试3: 批量搜索功能测试")
    print("=" * 60)
    
    try:
        # 检查搜索API密钥
        if not os.getenv("SERP_API_KEY") and not os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY"):
            print("⚠️  警告: 未设置搜索API密钥，跳过批量搜索测试")
            return False
        
        from src.search.llm_search_host import LLM_search
        
        retriever = LLM_search(
            model='gemini-2.0-flash-thinking-exp-01-21',
            infer_type="OpenAI",
            engine='google',
            each_query_result=5
        )
        
        # 使用预定义查询进行测试
        test_queries = [
            "machine learning optimization algorithms",
            "deep learning optimization techniques"
        ]
        
        start_time = time.time()
        urls = retriever.batch_web_search(
            queries=test_queries,
            topic="机器学习优化",
            top_n=5
        )
        end_time = time.time()
        
        print(f"✅ 批量搜索成功，耗时: {end_time - start_time:.2f}秒")
        print(f"  搜索查询数量: {len(test_queries)}")
        print(f"  返回URL数量: {len(urls)}")
        print("  搜索结果URL:")
        for i, url in enumerate(urls, 1):
            print(f"    {i}. {url}")
        
        # 验证返回类型
        if isinstance(urls, list) and all(isinstance(url, str) for url in urls):
            print("✅ 返回类型正确 (List[str])")
        else:
            print("❌ 返回类型错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 批量搜索测试失败: {e}")
        return False

def test_original_usage_pattern():
    """测试原有使用模式"""
    print("\n" + "=" * 60)
    print("测试4: 原有使用模式兼容性测试")
    print("=" * 60)
    
    try:
        # 模拟原有的使用方式
        from src.search.llm_search_host import LLM_search
        
        # 原有的初始化方式
        retriever = LLM_search(
            model='gemini-2.0-flash-thinking-exp-01-21', 
            infer_type="OpenAI", 
            engine='google', 
            each_query_result=10
        )
        
        print("✅ 原有初始化方式兼容")
        
        # 模拟原有的调用流程（如start_pipeline.py中的使用）
        topic = "artificial intelligence ethics"
        description = "Research on ethical considerations in AI"
        top_n = 5
        
        # 步骤1: 生成查询
        if os.getenv("OPENAI_API_KEY") or os.getenv("GOOGLE_API_KEY"):
            queries = retriever.get_queries(topic=topic, description=description)
            print(f"✅ 查询生成兼容，生成 {len(queries)} 个查询")
        else:
            queries = ["test query 1", "test query 2"]
            print("⚠️  使用模拟查询（无LLM API密钥）")
        
        # 步骤2: 批量搜索
        if os.getenv("SERP_API_KEY") or os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY"):
            url_list = retriever.batch_web_search(
                queries=queries, 
                topic=topic, 
                top_n=int(top_n * 1.2)
            )
            print(f"✅ 批量搜索兼容，返回 {len(url_list)} 个URL")
        else:
            print("⚠️  跳过批量搜索（无搜索API密钥）")
        
        # 步骤3: 测试其他方法
        score = retriever.snippet_filter(topic, "This is a test snippet about AI ethics")
        print(f"✅ 相似度计算兼容，得分: {score}")
        
        return True
        
    except Exception as e:
        print(f"❌ 原有使用模式测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("测试5: 错误处理测试")
    print("=" * 60)
    
    try:
        from src.search.llm_search_host import LLM_search
        
        # 测试无效参数
        try:
            retriever = LLM_search(engine="invalid_engine")
            print("⚠️  无效引擎参数未被检测到")
        except Exception:
            print("✅ 无效引擎参数被正确处理")
        
        # 测试正常初始化
        retriever = LLM_search()
        print("✅ 默认参数初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始MCP集成测试")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查环境变量
    print("\n📋 环境变量检查:")
    env_vars = ["OPENAI_API_KEY", "GOOGLE_API_KEY", "SERP_API_KEY", "BING_SEARCH_V7_SUBSCRIPTION_KEY"]
    for var in env_vars:
        value = os.getenv(var)
        status = "✅ 已设置" if value else "❌ 未设置"
        print(f"  {var}: {status}")
    
    # 运行测试
    tests = [
        ("接口兼容性", test_interface_compatibility),
        ("查询生成", test_query_generation),
        ("批量搜索", test_batch_search),
        ("原有使用模式", test_original_usage_pattern),
        ("错误处理", test_error_handling)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 发生异常: {e}")
            results.append((test_name, False))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！MCP版本可以完全替代原有的LLM_search模块。")
        print("\n📝 使用说明:")
        print("1. 将 'from src.LLM_search import LLM_search' 替换为:")
        print("   'from src.search.llm_search_host import LLM_search'")
        print("2. 或者使用兼容性包装器:")
        print("   'from src.LLM_search_mcp import LLM_search'")
        print("3. 确保MCP服务器正常运行")
        print("4. 设置必要的API密钥")
    else:
        print("⚠️  部分测试失败，请检查配置和实现。")

if __name__ == "__main__":
    main()
