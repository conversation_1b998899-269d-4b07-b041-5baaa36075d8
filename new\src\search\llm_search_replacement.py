#!/usr/bin/env python3
"""
LLM搜索替换模块
提供与原有LLM_search完全兼容的接口，但使用MCP协议实现
"""

# 导入新的LLM搜索主机
from .llm_search_host import LLM_search, create_llm_search

# 为了完全兼容，重新导出所有必要的类和函数
__all__ = ['LLM_search', 'create_llm_search']

# 如果需要，也可以直接导入异常类
try:
    from ..exceptions import QueryParseError
except ImportError:
    # 如果异常类不存在，定义一个简单的版本
    class QueryParseError(Exception):
        """查询解析错误"""
        pass

# 导出异常类
__all__.append('QueryParseError')
