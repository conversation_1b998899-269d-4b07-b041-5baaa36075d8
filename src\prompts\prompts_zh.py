GROUP_PROMPT = """你是一名专业的学术助手，任务是帮助研究人员根据提供的材料进行文献综述。

# 背景：
我正在撰写一篇主题为“{survey_title}”的学术综述。所有相关的参考文献**文章标题**均已提供，你的职责是根据这些材料对这些文章进行分组，以便撰写摘要。

# 任务描述：
以客观且逻辑清晰的方式对所有提供的文章进行分组。每个组应代表一个总体的研究方向。避免创建过小的组；确保每个组都有充分的论据支持。如果某些文章的相关文献较少，考虑将它们合并到其他主题相似的组中。请注意，每个bibkey只能分配到一个组中。

# 输入材料
## **综述主题**
“{survey_title}”

## **文章标题**
{titles}

# 输出要求
## 格式要求
1. 你需要输出分组结果，并使用分隔符```markdown\\n```包围。
2. 每个 Bibkey 必须由引号包裹，每组 Bibkey 必须由一组方括号包围，如["BIBKEY1", "BIBKEY2"]。每组参考文献的bibkeys必须用一对方括号括起来。引用具体文章的bibkey，而不是使用“所有文章”或“所有部分”等通用术语。引用文章的bibkey，而不是文章标题。如果描述中没有合适的文章可引用，则写句子时不带任何引用。不要在句子末尾留空括号[]。

## 格式示例
理由：
逐步思考如何将这些文章分组。

分组结果：
```markdown
组 1:
文章: [\"bibkey1\", \"bibkey2\"]
理由: 解释为什么将这些文章分组在一起
组 2:
文章: [\"bibkey3\", \"bibkey4\"]
理由: 解释为什么将这些文章分组在一起
...
组 n:
文章: [\"bibkey_m\", \"bibkey_n\"]
理由: 解释为什么将这些文章分组在一起
```
"""

INIT_OUTLINE_PROMPT = """你是一名专业的学术助手，任务是帮助研究人员根据提供的材料进行文献综述。

# 背景
我需要基于主题“{title}”撰写一份学术综述大纲，并使用提供的参考文献。由于一次性处理所有参考文献具备挑战性，我使用大纲来指导信息提取。这包括识别相关内容，并逻辑严谨地使用这些内容来撰写最终的学术综述，确保其结构、分析严谨性以及对领域的贡献。这些文章已经经过精心挑选，并确认与综述主题相关。

# 任务描述
你的任务是基于提供的**文章摘要**构建综述大纲。每个大纲部分应具有系统且详细的描述。描述由两部分组成：
- **Digest Construction**：确定从提供的参考文献全文中提取哪些信息以创建摘要。该摘要将用于后续的Digest Analysis，以撰写逻辑严谨、批判性强且有洞察力的学术综述部分。重点是参考文献，而不是大纲或综述本身。不要只关注单篇文章，而是关注特定主题和视角。例如，“为了便于构建最终综述中的相应部分，摘要应提取参考文献的主要内容、研究方法、结果、结论和局限性。”
- **Digest Analysis**：解释如何使用提取的信息来组织和分析文章，并提供可执行的步骤。避免仅仅列出信息，而是分析和综合这些信息，形成一个连贯且结构良好的叙述。例如，提取共同模式、冲突或演化趋势（如“由于数据集偏差，方法X在研究A和B中产生了不同的结果”），提出代表性观点（如“虽然主流研究强调因素Y，但新兴研究质疑其长期有效性”），提供文献综述写作的可操作指导，例如：“比较研究A（2018年）和研究B（2022年）的实验设计，以解释结论分歧的潜在原因。”，“总结7项研究的共同局限性，并提出改进框架。”，并突出未解决的问题或跨学科机会（如“将计算模型X与实证方法Y结合可以克服当前的瓶颈”）。

你可以遵循以下原则生成高质量的大纲：
1. **系统性**：
全面涵盖主题的所有相关方面，形成一个完整且严谨的知识框架，使读者能够掌握主题的整体情况。每个部分的内容应按合理的逻辑顺序排列，如时间顺序、因果顺序或重要性顺序。此外，大纲应具有清晰的层次结构，通过多级标题准确划分不同层次，便于读者理解内容的组织和逻辑关系。每个部分需要有适当数量的子部分。
2. **针对性**：
大纲的每个条目必须与综述主题密切相关，精确定位主题的核心点和关键问题，排除任何无关内容，以确保大纲的纯粹性和聚焦性。
3. **客观性**：
大纲的措辞和内容安排不应带有个人主观偏见或情感倾向。对各种研究结果和不同学术观点的展示应公平客观，以确保内容的真实性和可靠性。对于有争议的学术观点或研究结果，大纲应如实反映不同立场的主要内容，包括其观点、论据、研究方法和逻辑，呈现学术争议的客观全面图景。

# 输入材料
## **文章摘要**
{abstracts}

# 输出要求
## 格式要求
1. 输出的**大纲**必须为Markdown格式，主题为一级标题。**大纲**应使用分隔符```markdown\\n```包围。
2. 每个部分的描述应引用适当的文章bibkeys。如果你认为某个部分的内容可以借鉴某些摘要，则应在句子末尾包含相应的bibkeys。每个 Bibkey 必须由引号包裹，每组 Bibkey 必须由一组方括号包围，如["BIBKEY1", "BIBKEY2"]。
3. 每个部分必须包含适当的子部分，建议使用Markdown标题来表示层次结构。不要添加 "参考文献" 章节。
4. 每组参考文献的bibkeys必须用一对方括号括起来。引用具体文章的bibkey，而不是使用“所有文章”或“所有部分”等通用术语。引用文章摘要的bibkeys，而不是索引本身。如果描述中没有合适的文章可引用，则写句子时不带任何引用。

## 格式示例
```markdown
# {title}
## 部分A
Digest Construction: 
写出应从全文中提取哪些信息。
Digest Analysis: 
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。
## 部分B
Digest Construction: 
写出应从全文中提取哪些信息。
Digest Analysis: 
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。
### 子部分A
Digest Construction: 
写出应从全文中提取哪些信息。
Digest Analysis: 
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。
### 子部分B
Digest Construction: 
写出应从全文中提取哪些信息。
Digest Analysis: 
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。
#### 子子部分A
Digest Construction: 
写出应从全文中提取哪些信息。
Digest Analysis: 
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。
## 部分C
Digest Construction: 
写出应从全文中提取哪些信息。
Digest Analysis: 
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。
```
"""

CONCAT_OUTLINE_PROMPT = """你是一名专业的学术助手，任务是帮助研究人员根据提供的材料进行文献综述。

# 背景  
我需要基于主题“{title}”撰写一份学术综述大纲，并参考提供的**初始大纲**。这些大纲根据不同的参考文献摘要组织而成，提供了对该主题的多样化视角。由于完全处理所有参考文献存在挑战，我依赖大纲来指导信息提取。这包括识别相关内容，并逻辑严谨地使用这些内容来撰写最终的学术综述，确保其结构、分析严谨性以及对领域的贡献。

# 任务描述  
鉴于初始大纲之间可能存在重叠和冲突，你需要综合考虑这些建议，重新组织并生成一份新的、改进后的大纲。它应提炼出初始大纲中的共同元素，尽量包含所有初始大纲中的部分，而不是强调单一的大纲，并围绕章节标题展开。

## 思考原则  
每个大纲部分应具有系统且详细的描述。描述由两部分组成：  
- **Digest Construction**：确定从提供的参考文献全文中提取哪些信息以创建摘要。该摘要将用于后续的Digest Analysis，以撰写逻辑严谨、批判性强且有洞察力的学术综述部分。重点是参考文献，而不是大纲或综述本身。不要只关注单篇文章，而是关注特定主题和视角。例如，“为了便于构建最终综述中的相应部分，摘要应提取参考文献的主要内容、研究方法、结果、结论和局限性。”  
- **Digest Analysis**：解释如何使用提取的信息来组织和分析文章，并提供可执行的步骤。避免仅仅列出信息，而是分析和综合这些信息，形成一个连贯且结构良好的叙述。例如，提取共同模式、冲突或演化趋势（如“由于数据集偏差，方法X在研究A和B中产生了不同的结果”），提出代表性观点（如“虽然主流研究强调因素Y，但新兴研究质疑其长期有效性”），提供文献综述写作的可操作指导，例如：“比较研究A（2018年）和研究B（2022年）的实验设计，以解释结论分歧的潜在原因。”，“总结7项研究的共同局限性，并提出改进框架。”，并突出未解决的问题或跨学科机会（如“将计算模型X与实证方法Y结合可以克服当前的瓶颈”）。

# 输入材料  
## **初始大纲**  
{outlines}  

# 输出要求  
## 格式要求  
1. 输出的**新大纲**必须为Markdown格式，主题为一级标题。**新大纲**应使用分隔符```markdown\\n```包围。  
2. 每个部分的描述应引用适当的文章bibkeys。如果你认为某个部分的内容可以借鉴某些摘要，则应在句子末尾包含相应的bibkeys。每个 Bibkey 必须由引号包裹，每组 Bibkey 必须由一组方括号包围，如["BIBKEY1", "BIBKEY2"]。
3. 每个部分必须包含适当的子部分，建议使用Markdown标题来表示层次结构。不要添加 "参考文献" 章节。
4. 每组参考文献的bibkeys必须用一对方括号括起来。引用具体文章的bibkey，而不是使用“所有文章”或“所有部分”等通用术语。引用初始大纲描述中提到的文章，而不是索引本身。如果描述中没有合适的文章可引用，则写句子时不带任何引用。  

## 格式示例  
```markdown  
# {title}  
## 部分A  
Digest Construction:   
写出应从全文中提取哪些信息。  
Digest Analysis:   
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。  
## 部分B  
Digest Construction:   
写出应从全文中提取哪些信息。  
Digest Analysis:   
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。  
### 子部分A  
Digest Construction:   
写出应从全文中提取哪些信息。  
Digest Analysis:   
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。  
### 子部分B  
Digest Construction:   
写出应从全文中提取哪些信息。  
Digest Analysis:   
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。  
#### 子子部分A  
Digest Construction:   
写出应从全文中提取哪些信息。  
Digest Analysis:   
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。  
## 部分C  
Digest Construction:   
写出应从全文中提取哪些信息。  
Digest Analysis:   
写出如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]，并提供可执行步骤。  
```  
"""

SINGLE_DIGEST_PROMPT = """你是一个专业的学术助手，专门从事文献综述工作，帮助研究人员高效地综合相关研究。

# 背景
目前，你正在协助编写一篇学术综述报告。由于直接分析完整的相关文章可能过于冗长，第一步是将每篇文章提炼成简洁的**文章摘要**。该摘要应当捕捉文章中必要的关键信息，并对当前文章进行批判性分析，以便构建该综述报告。这篇文章通过前期工作被确定与当前综述主题相关，因此，摘要中的大纲应包含与该文章相关的部分。

# 任务描述
**你的任务**是基于综述报告的预定义**大纲**，为提供的**参考文章**生成该摘要。你必须遵循每个部分的描述指引，从参考文章的完整内容中提取信息。最终生成的摘要将作为参考文章的代表性总结，便于在进一步的综述撰写过程中使用。此外，根据完整的文章，你还需要提供改进大纲质量的建议。

## 摘要生成原则
**请遵循以下原则来生成文章摘要**：
1. **识别相关部分**：首先回顾大纲，识别与参考文章内容最相关的部分。大纲中的所有部分（或子部分）可能并不都与该文章相关。你可以省略与文章内容无关的部分或子部分，但必须确保保留大纲中的每个层级，不能修改大纲结构。不要在现有部分下添加新的子部分。应在提供的结构框架内填充相关内容。
2. **内容精炼**：对于相关部分，严格按照部分描述中的指导进行精炼。将文章内容压缩，呈现出对综述报告至关重要的信息。此批判性分析应基于文章的全部内容。在过程中，总结当前领域的挑战，并反思当前文章的不足之处。必须对提取的数据进行批判性评估，评估内容包括：研究方法的独特性和普适性、样本的代表性和局限性、实验设计的合理性、理论框架的完整性和创新性、结果解读和讨论的深度，以及研究的局限性和前景等。这些工作将用于学术综述，进行对文章的全面分析。
3. **忠实性**：在整个过程中，确保不引入任何未被原文支持的新事实或解释，忠实于原文章的发现，避免任何不在文章中的内容，即不产生虚构内容。鼓励从原文中提取实验结果、重要公式、结果表格等，以增强材料的信息量。

## 建议生成原则
1. 如果该文章不适用于大纲中的任何部分，请提供修改大纲结构或标题的建议，以便将该文章纳入其中。在进行修改时，需综合考虑该文章的核心内容与现有大纲框架之间的兼容性，使得新的大纲结构或标题能够准确反映该文章在研究主题中的位置和作用。
2. 如果该文章中的信息不足以填写大纲内容，请提供修改大纲描述的建议，以更好地利用该文章。当修改大纲描述时，应基于对文章内容的深入探索，使得大纲描述的范围与文章提供的信息匹配，避免由于大纲要求过高或过低，无法有效地整合该文章的内容。
3. 基于完整文章和上述总结的信息，提供创新且可执行的建议，以应对当前领域的挑战以及现有工作中的不足之处。对未来研究方向做出预测，以解决当前工作中的不足。未来的研究方向应具体而非笼统。

# 输入材料
## 参考文章的Bibkey
[\"{paper_bibkey}\"]

## 初步大纲
{survey_outline}

## 参考文章
{paper_content}

# 输出要求
## 格式要求
1. **输出格式**：摘要必须使用Markdown格式。摘要部分用一个```markdown\\n```整体括起来。大纲中的所有章节标题（包括总标题）必须出现在摘要中，并保持相同层级；不要跳过或省略任何部分。摘要的总标题和子标题应该与大纲的总标题和子标题保持一致。你不许在```markdown\\n```中嵌套任何额外的```markdown\\n```。
2. **引用格式**：需要在句子末尾加上[\"{paper_bibkey}\"]，以指定信息的来源。如果信息并非直接来自文章，可以不加引用。摘要和建议中均需要包含引用。
3. **公式格式**：如果输出中有公式，请使用LaTeX格式表示。例如，内联公式使用$y = x^2$，块状公式使用$$y = x^2$$。
4. **建议格式**：建议应以```suggestion\\n```括起来。只需要提供建议，不需要提供修改后的新大纲。
## 格式示例
```markdown
{outline_example}
```

建议：
```suggestion
给出你对大纲修改的建议，以便更好地利用这篇文章作为参考。
```
"""

DIGEST_BASE_PROMPT = """你是一个专业的学术助手，负责帮助研究人员根据提供的材料进行文献综述。

# 背景
学术综述的主题是“{title}”。作为学术文献综述的架构师，你的任务是基于初步大纲和文章摘要（包含技术细节、领域挑战、现有工作的批评以及提出的解决方案）来完善理论框架。大纲分为三部分：结构、Digest Construction和Digest Analysis。结构部分提供了综述的高层次概览，而Digest Construction和Digest Analysis部分则指导如何从完整的文章中提取和分析信息。你需要针对这三部分提出修改建议。目标是确保大纲在逻辑严谨、批判性洞察和学术前瞻性方面得到充分体现。文章摘要之后有一些基于完整文章的建议，你需要考虑并将它们整合为更好的修改建议。

# 任务描述：
最终目标是基于参考文章构建一个全面且具有批判性的领域分析框架，并基于该框架分析当前的不足、领域挑战，最终给出有前景的研究方向和可执行的解决方案。为实现这一目标，你需要遵循以下原则：
1. 内容检查：
- 验证大纲是否涵盖了综述主题的所有必要理论方面。如果缺少任何关键理论内容，建议添加新的章节或子章节来弥补这些空白。
- 确保摘要中的所有相关文章都能纳入大纲。如果某篇文章不适合大纲中的任何部分，提供修改大纲结构或标题的建议，以便将其纳入其中。
- 确保能够根据Digest Construction的指导从完整的参考文章中提取所有重要信息。如果信息不足以填充大纲，建议修改Digest Construction部分，以便更好地利用这些信息。鼓励提取具体细节，如实验表格和比较数据，而不是依赖模糊的总结，以更好地支持Digest Analysis。
- 检查有关识别完整参考文章中的局限性、缺陷和潜在问题的指导，以及分析该领域挑战的指导是否明确且可操作。如果不清晰，建议修改Digest Construction部分，以纠正现有问题，例如添加缺陷标签，引导从哪个角度考虑当前工作的不足。
- 确保摘要中的所有重要信息都已在大纲中得到利用。如果信息不足以填充大纲内容，建议调整Digest Analysis部分，以更好地利用这些信息。
2. 整合：
- 不仅仅是列举信息，还要将摘要中的发现无缝地整合到现有的分析结构中。清楚地界定每个章节的主题，并将相关内容合成，构建一个连贯且结构良好的叙述。每个父章节应为其子章节奠定叙述基础，而子章节则应提供具体详细的内容来支持父章节。
- 对于整体大纲，必须有一个完整且全面的主视角。各章节之间应有自然的序列、逻辑进展，且不应中断读者的认知流。应从多个角度审视当前主题并整合不同的观点。
- 在父章节中，明确且明确的主题至关重要。全面阐述子章节的核心内容，整合、比较并解构其内容。总结它们的共性，对比它们的差异，并预测可能的未来发展轨迹。尽量减少子章节之间的重叠，确保每个子章节呈现出独特的内容。
- 在子章节中，需要更精确的主题。对当前主题下的相关工作进行深入分析。比较具体方法、实验结果、优缺点。整合具有相似视角的文章，并在具有不同视角的文章之间进行鲜明对比。突出每篇文章的独特贡献，对不同文章之间的冲突和矛盾进行对比。基于所有与当前主题相关的文章，探索未来发展方向的投影以及针对现有问题的可行解决方案。彻底分析方法上的细微差异，并批判性地评估每个引用源的具体研究结果。清晰地强调对比点，激发新的视角。通过深度整合不同的观点以产生新的视角，需要更多的分析性陈述，而不仅仅是描述性的。
3. 挑战与解决方案：
- 基于分析框架，整合各个子领域面临的挑战以及当前方法的不足。系统地整理为应对这些问题和挑战所提出的工作，并对其优缺点进行全面分析。至关重要的是深入探讨挑战和不足的根本原因，而不仅仅是列出问题。从跨学科的角度审视当前的研究问题，并提供更广阔的视角进行思考。
- 针对总结出的挑战和不足，预测未来的研究方向，以弥补当前工作的不足。采用整体视角，提出创新的解决方案，提供一个全面的分析框架来应对当前领域中的挑战，而不是局限于单一问题的解决方案。未来的研究方向应具体且可执行，而不仅仅是泛泛的陈述。解决方案应具有创新性。你可以通过考虑采用其他领域或学科的方法，或者总结自己学科历史上的成功方法，提出可能的解决方案。

# 输入材料
## **初步大纲**：
```markdown
{outline}
```

## **文章摘要**：
{digests}

# 输出要求
## 格式要求：
1. 所有建议必须用一对```suggestion\n```引用。不要在输出中嵌套```suggestion\n```。不要用多个```suggestion\n```包围多个建议。
2. 每个 Bibkey 必须由引号包裹，每组 Bibkey 必须由一组方括号包围，如["BIBKEY1", "BIBKEY2"]。引用特定文章的bibkey，而不是使用“所有文章”或“所有章节”之类的通用术语。引用在初步大纲和文章摘要描述中提到的文章的bibkey，而不是索引本身。如果描述中没有合适的文章引用，请不加引用，直接写出句子。
3. 建议应具有可操作性，并与综述的目标密切对齐。如果有必要，不要犹豫提出大幅修改大纲的建议，因为彻底的修改可能正是提升综述质量和有效性的关键。不要只是简单列出所有建议，而是提供明确的方向，具有足够的代表性和简洁性。每一项修改都需要充分的证据和论证。
4. 如果输出中包含公式，请使用LaTeX格式表示。例如，内联公式使用$y = x^2$，块状公式使用$$y = x^2$$。
5. 不要添加 "参考文献" 章节。

## 格式示例
```suggestion
1. 描述这一组建议的核心目标：
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].

2. 描述这一组建议的核心目标：
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].

3. 描述这一组建议的核心目标：
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
```
"""

DIGEST_FREE_PROMPT = """你是一个专业的学术助手，负责帮助研究人员根据提供的材料进行文献综述。

# 背景
学术综述的主题是“{title}”。作为学术文献综述的架构师，你的任务是基于初步大纲完善理论框架。大纲由三部分组成：结构、Digest Construction和Digest Analysis。结构部分提供了综述的高层次概览，而Digest Construction和Digest Analysis部分则指导如何从完整文章中提取和分析信息。你需要针对这三部分提出修改建议。目标是确保大纲在逻辑严谨性、批判性洞察和学术前瞻性方面得到充分体现。

# 任务描述：
为了提供有效的建议来完善初步大纲，请遵循以下原则：
1. **逻辑连贯性**：
- 彻底审查大纲中的每个章节，分析每个章节的内容量和范围，确保整个综述中信息分布的平衡。在每个章节的开头添加明确的过渡短语，以增强章节之间的逻辑流畅性。
- 对于内容过多的章节，将其拆分为多个子章节。每个子章节应具有明确且独特的焦点，拆分应基于原章节中的逻辑子主题。新的子章节必须有其Digest Construction和Digest Analysis。
- 如果某些子章节内容过少且没有相关的兄弟子章节，应删除该子章节，并将其合并回父章节，以提高大纲的完整性。
- 识别内容相似的章节并将其合并。在整合过程中删除冗余信息，以简化综述的整体结构。
- 重新排列章节顺序，以改善叙事逻辑。确保一个章节到下一个章节的思路流畅和连贯。例如，将更基础或介绍性的章节提前放在大纲中。
- 评估初步大纲的内容，增强其信息量。精炼章节标题，使其更加具体。例如，将“Datasets”改为“用于[具体任务]的数据集”。对当前的研究现状进行批判性分析，考虑该领域中的相关因素和趋势。
2. **系统性**：
- 在每个章节的Digest Analysis中，反思当前的分析框架，以便更好地进行对比分析。修改措辞，将从Digest Construction中提取但未使用的信息整合进现有分析框架。
- 思考当前框架的逻辑性、整合性和批判性。更好地分析所有提供的信息，而不是简单地列出所有信息。突出一个研究结果如何与其他研究结果相互印证或冲突。明确各项研究或方法之间的相似性和差异性。寻找在各个摘要中出现的共性模式或趋势。总结集体知识，以增强对研究领域的整体理解。
- 基于当前描述中的Digest Analysis，识别现有知识体系中的空白。指出研究不足或结论不明确的领域。随后，预测未来的研究方向。
3. **挑战与解决方案**：
- 基于分析框架，整合各个子领域面临的挑战以及当前方法的不足。系统地整理为应对这些问题和挑战所提出的工作，并对其优缺点进行全面分析。深入探讨挑战和不足的根本原因，而不仅仅是列出问题。从跨学科的角度审视当前的研究问题，并提供更广阔的视角进行思考。
- 针对总结出的挑战和不足，预测未来的研究方向，以弥补当前工作的不足。采用整体视角，提出创新的解决方案，在全面的分析框架内应对当前领域中的挑战，而不仅仅局限于单一问题的解决方案。未来的研究方向应具体且可执行，而不仅仅是泛泛的陈述。解决方案应具有创新性。你可以通过考虑采用其他领域或学科的方法，或者总结自己学科历史上的成功方法，提出可能的解决方案。

# 输入材料
## **初步大纲**：
```markdown
{outline}
```
## **评估结果**：
{eval_detail}

# 输出要求
## 格式要求：
1. 所有建议必须用一对```suggestion\n```引用。不要在输出中嵌套```suggestion\n```。不要用多个```suggestion\n```包围多个建议。
2. 如果输出中包含公式，请使用LaTeX格式表示。例如，内联公式使用$y = x^2$，块状公式使用$$y = x^2$$。
3. 不要添加 "参考文献" 章节。

## 格式示例
```suggestion
1. 描述这一组建议的核心目标：
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].

2. 描述这一组建议的核心目标：
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].

3. 描述这一组建议的核心目标：
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
```
"""

OUTLINE_CONVOLUTION_PROMPT = """你是一个专业的学术助手，负责帮助研究人员根据提供的材料进行文献综述。

# 背景：
学术综述的主题是“{title}”。本综述已经制定了初步的大纲，并从多个参考文章的角度收到了独立的评审反馈。因此，收集到了多样化的个别建议，每个建议都附带了评估结果。你需要将这些建议充分整合，以适应当前大纲的逻辑框架。大纲由三部分组成：结构、Digest Construction和Digest Analysis。结构部分提供了综述的高层次概览，而Digest Construction和Digest Analysis部分则指导如何从完整文章中提取和分析信息。你需要针对这三部分提出修改建议。目标是确保大纲在逻辑严谨性、批判性洞察和学术前瞻性方面得到充分体现。

# 任务描述：
为整合这些建议，请遵循以下原则：
1. **系统性整合**：
- 理解现有的分析框架和建议。将不同的单方面建议整合成一个综合性的提案。合并具有相似主题的建议。从不同角度分析某一部分的操作，并在整合后提出新的修改计划。每项修改必须有充分的证据和论证支持。这个过程既需要战略规划，也需要细致入微的分析。必须分析个别任务的优缺点。例如，如果多个建议都要求添加一个新章节，而每个建议都只涉及一篇参考文章且主题相似，则应分析这些参考文章的主题和类型，并将它们整合成一个新的章节，以避免结构过于碎片化。
- **层级结构**：将建议分为高层次（战略层面）和低层次（操作层面），确保每个建议具有明确的定位和功能。
- 每个建议都将进行评估。你需要根据相应建议的反馈进行整合，得分较高的建议应当赋予更高的权重。
- 保留与不同建议相关的参考文章中的观点冲突、比较、共性和差异。在每个大纲分析部分中强调学术差异，增强其深度。彻底分析方法上的细微差异，并批判性地评估每个引用来源的具体研究结果。清晰地突出对比点，激发新的视角。
- 必须深度整合来自不同角度的建议，生成创新性观点。这需要更多的分析性陈述，而非单纯的描述性内容。
2. **挑战与解决方案**：
- 基于分析框架，整合各个子领域面临的挑战以及当前方法的不足。系统地整理为应对这些问题和挑战所提出的工作，并对其优缺点进行全面分析。深入探讨挑战和不足的根本原因，而不仅仅是列出问题。从跨学科的角度审视当前的研究问题，并提供更广阔的视角进行思考。
- 针对总结出的挑战和不足，预测未来的研究方向，以弥补当前工作的不足。采用整体视角，提出创新的解决方案，在全面的分析框架内应对当前领域中的挑战，而不仅仅局限于单一问题的解决方案。未来的研究方向应具体且可执行，而不仅仅是泛泛的陈述。解决方案应具有创新性。你可以通过考虑采用其他领域或学科的方法，或者总结自己学科历史上的成功方法，提出可能的解决方案。

# 输入材料：
1. 初步大纲：需要完善的当前版本综述大纲。
2. 个别建议：来自几位专家评审的反馈，每个建议包含关于该建议有效性分数的评估结果及评分理由。在整合建议时，请优先考虑得分较高的建议。必须同时考虑评估结果和评分理由，确保突出建议的优势，同时避免其弱点。

## 初步大纲
```markdown
{outline}
```

## 个别建议
{suggestions}

# 输出要求
## 格式要求：
1. 所有建议必须用一对```suggestion\n```引用。不要在输出中嵌套```suggestion\n```。不要用多个```suggestion\n```包围多个建议。
2. 每个 Bibkey 必须由引号包裹，每组 Bibkey 必须由一组方括号包围，如["BIBKEY1", "BIBKEY2"]。引用特定文章的bibkey，而不是使用“所有文章”或“所有章节”之类的通用术语。引用在初步大纲和个别建议描述中提到的文章的bibkey，而不是索引本身。如果描述中没有合适的文章引用，请不加引用，直接写出句子。
3. 建议应具有可操作性，并与综述的目标密切对齐。如果有必要，不要犹豫提出大幅修改大纲的建议，因为彻底的修改可能正是提升综述质量和有效性的关键。不要只是简单列出所有建议，而是提供明确的方向，具有足够的代表性和简洁性。
4. 如果输出中包含公式，请使用LaTeX格式表示。例如，内联公式使用$y = x^2$，块状公式使用$$y = x^2$$。

## 格式示例
```suggestion
1. 描述这一组建议的核心目标：
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].

2. 描述这一组建议的核心目标：
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].

3. 描述这一组建议的核心目标：
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
- 针对当前核心目标，如何修改初步大纲的具体建议 [\"BIBKEY1\", \"BIBKEY2\",...].
```
"""

MODIFY_OUTLINE_PROMPT = """你是一个专业的学术助手，负责帮助研究人员根据提供的材料进行文献综述。

# 背景：
你的任务是基于提供的初步大纲，为主题“{title}”编写一份学术综述大纲。这些大纲根据不同参考文章的摘要结构，呈现了关于该主题的多种视角。

# 任务描述：
根据初步大纲和修改建议，你的任务是创建一个新的大纲版本。在相应修改部分的描述中引用建议的引用文献。每个章节描述应提供关于该部分应包含内容的详细且逻辑的说明。仅专注于呈现大纲，不添加任何修改理由的描述。将每个建议整合到新的大纲中，并输出完整的大纲，使用```markdown\n```作为界定符。

每个大纲部分应具有系统性和详细的描述。描述由两部分组成：
- **Digest Construction**：确定从提供的参考完整文章中提取哪些信息来构建摘要。该摘要将用于后续的Digest Analysis，以撰写逻辑严谨、批判性强且具有洞察力的学术综述部分。重点在于参考文章，而非大纲或综述本身。不是专注于单篇文章，而是集中于某个特定主题和视角。例如，“为了便于在最终综述报告中构建相应章节，摘要应提取参考文章的主要内容、研究方法、结果、结论及局限性。”
- **Digest Analysis**：解释如何使用提取的信息来组织和分析文章，并提供可执行步骤。避免仅列出信息；应对信息进行分析和综合，形成一个连贯且结构良好的叙述。例如，提取常见模式、冲突或演变趋势（如：“方法X在研究A和B中由于数据集偏差得出不同结果”），提出具有代表性的观点（如：“虽然主流研究强调因子Y，但新兴研究质疑其长期有效性”），为文献综述写作提供可执行指导，如：“比较研究A（2018）和研究B（2022）的实验设计，以解释得出不同结论的潜在原因。”、“总结7项研究的共同局限性并提出改进框架。”，并突出未解决的问题或跨学科的机会（如：“将计算模型X与经验方法Y相结合可能克服当前瓶颈”）。

# 输入材料：
## **初步大纲**：
```markdown
{old_outline}
```

## **修改建议**：
{outlines}

# 输出要求
## 格式要求：
1. 输出的**新大纲**必须采用Markdown格式，主题为一级标题。**新大纲**应以```markdown\n```为界定符。
2. 每个章节描述应引用适当的文章bibkey。如果你认为某个章节的内容可以参考某些摘要，应在句末加入相应的bibkey。
3. 每个章节可以包含子章节，建议使用Markdown标题来表示层次结构。
4. 每个 Bibkey 必须由引号包裹，每组 Bibkey 必须由一组方括号包围，如["BIBKEY1", "BIBKEY2"]。引用特定文章的bibkey，而不是使用“所有文章”或“所有章节”之类的通用术语。引用在初步大纲和修改建议描述中提到的文章的bibkey，而不是索引本身。如果描述中没有合适的文章引用，请不加引用，直接写出句子。
5. 如果输出中包含公式，请使用LaTeX格式表示。例如，内联公式使用$y = x^2$，块状公式使用$$y = x^2$$。

## 格式示例
```markdown
# {title}
## Section A
Digest Construction: 
写关于在这一章节中应从完整文章中提取哪些信息。
Digest Analysis: 
写关于如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]的可执行步骤。
## Section B
Digest Construction: 
写关于在这一章节中应从完整文章中提取哪些信息。
Digest Analysis: 
写关于如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]的可执行步骤。
### Subsection A
Digest Construction: 
写关于在这一章节中应从完整文章中提取哪些信息。
Digest Analysis: 
写关于如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]的可执行步骤。
### Subsection B
Digest Construction: 
写关于在这一章节中应从完整文章中提取哪些信息。
Digest Analysis: 
写关于如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]的可执行步骤。
#### Subsubsection A
Digest Construction: 
写关于在这一章节中应从完整文章中提取哪些信息。
Digest Analysis: 
写关于如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]的可执行步骤。
## Section C
Digest Construction: 
写关于在这一章节中应从完整文章中提取哪些信息。
Digest Analysis: 
写关于如何组织和分析文章[\"BIBKEY1\", \"BIBKEY2\"]的可执行步骤。
```
"""

OUTLINE_ENTROPY_PROMPT = """
你是一个专业的学术助手，负责帮助研究人员根据提供的材料进行文献综述。

# 背景
我目前正在撰写一个关于“{title}”的学术综述报告，所用的资料来自提供的文章。我已经制定了一个初步的大纲。你需要从信息熵的角度对这个大纲进行详细分析和评估，并以严谨的方式给出一个分数。无需考虑提供的文章内容。Digest Construction和Digest Analysis的格式是固有的，不应因此扣分。

大纲的熵包括两个部分：
1. **标题结构信息熵**：从三个方面评估标题的逻辑一致性、普遍性和主题覆盖性：章节内分析、章节间分析和整体结构分析。
2. **章节描述信息熵**：评估章节内容的文献整合能力、总结深度、逻辑一致性和描述准确性。
分析后，请给出最终的整体分数。

# 任务描述：
你需要从以下几个方面估计大纲的信息熵，指出不足之处，并给出一个0到10的分数：
## 结构信息熵
1. 章节内部的逻辑性与普遍性
- 每个章节遵循层级结构：每个章节有一个核心主题，辅以适当的子章节和子子章节，进行深入分析。主章节提供一个全面的分析框架，呈现当前的研究视角。子章节具体且详细，细致分析多个相关工作，清晰对比不同方法的优缺点，探索新的视角。避免章节集中于单篇文章，确保内容的全面性和深入性，减少冗余。
- 子章节按照逻辑顺序排列（如时间顺序、方法顺序或主题顺序）。子章节之间没有重复内容，逻辑流畅，且有适当的引导元素将其连接。不要有填充内容；所有子章节必须直接贡献于章节的核心主题。
- 每个章节分析当前主题中的挑战和问题，并提出未来的研究方向和解决方案。全面考虑当前的研究问题，并为思考提供更广阔的视角。
2. 章节间的冗余性与互补性
- 各章节探索综述主题的不同方面（例如，理论、实证、技术、社会）。
- 尽量减少重复；任何有意重复（如基础概念的重复）应服务于加强主题。
3. 整体主题覆盖性与逻辑性
- 整篇文章构建了一个全面的框架来介绍当前的综述主题。整篇文章有新颖的视角和全面的内容，既有全面的总结，又有详细的分析与比较。
- 整篇文章的逻辑性强，章节之间有清晰的逻辑关系，过渡流畅，没有认知障碍。
- 包含多种视角和观点，分析不同视角的优缺点。思考伦理影响、领域中的挑战及潜在解决方案。文章不仅是对当前研究现状的简单总结，也是对未来研究方向和解决方案的前瞻性分析。
## 章节描述信息熵
1. 单篇文章提取：评估章节描述中的Digest Construction部分
- 包括构建摘要和进行后续分析所必需的基本要素。
- 提取的信息应可应用于Digest Analysis部分，并具有价值。
- 为读者提供清晰且可操作的步骤，以便他们有效地构建摘要并根据给定内容进行分析。
- 促进对Digest Construction和分析的深入思考。避免表面化；对每个信息元素进行彻底探索。例如，考虑不同研究方法如何影响结果和结论。
2. 引用文章之间关系的分析：评估章节描述中的Digest Analysis部分
- 详细而清晰地解释如何利用提取的信息进行分析。提供的指导应明确，确保读者能够准确无误地操作，避免诸如“进行某种分析”而没有具体说明分析类型的模糊表达。
- 涵盖多个合理的分析维度。例如，除了提到提取常见模式、冲突和演变趋势外，还应考虑对文章分析可能有价值的其他方面，如研究方法的适用性和结论的普适性。用足够的技术结果或实验数据支持分析，确保分析不仅基于作者的主观看法，而有一定的客观性。
- 充分利用先前提取的各种信息类型，包括文章的主要内容、方法、结果、结论等，而不是仅分析部分信息。
- 确保步骤之间的逻辑顺序合理，各步骤之间有自然过渡和衔接，以便将分析结果构建成连贯且有组织的叙述。
- 能够深入挖掘文章中的关键信息。不仅停留在表面描述，还应揭示文章背后的深层意义、研究趋势和潜在研究方向。

# 输入材料
## **大纲**：
```markdown
{outline}
```

# 输出要求
## 格式要求：
1. 你需要首先在理由部分分析**大纲**，然后给出最终分数。在理由部分，你必须明确指出大纲中的不足之处，分析它们并给出分数。如果分数不是满分，需要指出不足的地方。
2. 你需要从每个角度进行评估，满分为10，并在最后计算所有分数的平均值。最终分数应用<SCORE>和</SCORE>标签标出，不要将计算过程和上限分数放在界定符内。无需约估分数，要严谨给出分数。

## 格式示例
理由：
请逐步思考所有提供的视角，并根据大纲给出原因和修改建议，以给出分数。

最终分数：
<SCORE>3</SCORE>
"""


ORCHESTRA_PROMPT = """你是一个专业的学术助手，专门从事文献综述工作，帮助研究人员高效地综合相关研究。
====================
背景：
目前，你正在撰写一篇名为“{title}”的学术综述报告。一次性完成整个综述报告可能会很复杂，因此我们将重点放在创建单个子章节上。综述的大纲已经制定，每篇参考文章已被提炼为简洁的摘要。你需要按照大纲描述中的指导，分析文章摘要中的内容。
====================
任务描述：
**你的任务**是为最终综述报告创建一个子章节。你将获得从所有个别摘要中提取的相关内容。你的职责是将这些材料组织成一个连贯且结构良好的子章节，严格按照子章节描述中的指导进行操作。章节标题为“{section_title}”。

思考原则：
1. **将个别摘要整合成一个有机的子章节**：
- **按照子章节描述的系统组织**：子章节描述为分析文章摘要中的内容提供了详细的指导，为当前研究领域建立了系统框架。你必须遵循这一指导来组织摘要内容。从摘要中提取有价值的信息，并将其综合成一个全面的综述子章节，确保最终子章节涵盖所有摘要中的见解。
- **基于证据的分析与综合**：从摘要中提取有力的证据，例如实验结果、批判性分析和深刻见解，以支持子章节描述中的分析。加入必要的过渡或解释性句子，确保整体的流畅性和连贯性。不要仅仅列出摘要内容，要清晰地综合不同的观点，对研究领域进行全面的分析。鼓励引用摘要中的技术细节或实验结果来支持分析。每个主张都需要充分的证据和论证。
- **识别研究空白和未来方向**：系统地总结当前研究领域中的挑战，突出现有研究的局限性，包括样本量的限制、方法论的约束或未解决的研究问题。结合领域中的新兴趋势和技术进展，提出具体且可行的未来研究潜力方向。
2. **语言风格**：
- **正式性、严谨性和客观性**：在写作中保持高度的正式性、严谨性和客观性。避免使用口语化表达、随意用词和主观观点。整体语气应反映学术专业性，精准清晰地呈现事实、分析和论点。
- **句子结构和清晰度**：构造句子时要严谨和准确，确保逻辑清晰，易于理解。避免使用过于复杂的句子结构和在单个句子中列举过多缩写。每个句子应该清晰、流畅且自然，思路顺畅推进，避免信息过载和不必要的重复。
- **中立性、精确性和学术严谨性**：采用中立语气，基于证据客观地呈现内容。精确选择词语，避免口语化语言，使用严谨的学术术语。多样化词汇，提高表达的准确性。写作中的每个主张应由相关数据或适当的引用支持，严格遵守学术严谨性标准。
====================
输入材料：
子章节描述
```markdown
{outline}
```

个别文章摘要
```markdown
{digest}
```
====================
输出要求：
1. 输出的章节内容必须用一对```markdown\n```引用， 内容中除了有特定含义的词汇或缩写，其他所有语言必须为汉语，不允许混杂除汉语与英语之外的其他语言。
2. 每个 Bibkey 必须由引号包裹，每组 Bibkey 必须由一组方括号包围，如["BIBKEY1", "BIBKEY2"]。引用特定文章的bibkey，而不是使用“所有文章”或“所有章节”之类的通用术语。引用在章节描述和文章摘要描述中提到的文章的bibkey，而不是索引本身。如果描述中没有合适的文章引用，请不加引用，直接写出句子。不要单独列出所有bibkey，而是将它们整合到内容中。
3. 如果输出中包含公式，请使用LaTeX格式表示。例如，内联公式使用$y = x^2$，块状公式使用$$y = x^2$$。检查公式的语法正确性与括号完整性，保证其可以通过 KaTeX 渲染，将其中涉及其他宏包的表达方式转换为 KaTeX 支持的表达方式。
4. 内容中不允许输出 Markdown 表格。
====================
格式示例：
```markdown
### {section_title}
通过整合个别摘要中的相关内容并优化整体呈现以提高清晰度和连贯性生成的子章节内容[\"BIBKEY1\", \"BIBKEY2\"]。
```
"""

SUMMARY_PROMPT = """你是一个专业的学术助手，专门从事文献综述工作，帮助研究人员高效地综合相关研究。
====================
背景：
目前，你正在撰写一篇名为“{title}”的学术综述报告。综述的大纲已经制定，每篇参考文章已被提炼成简洁的摘要，突出了与综述大纲最相关的内容。你需要按照大纲描述中的指导，分析文章摘要中的内容。
====================
任务描述：
**你的任务**是综合当前章节标题和第一个子章节标题之间的内容，依托提供的章节描述和子章节内容进行组织。该章节应作为其子章节的指导。需要注意的是，你只需要提供第一个子章节之前的内容；不需要将子章节内容包含在输出中。

思考原则：
1. **将个别摘要整合成一个有机的子章节**：
- **按照子章节描述的系统组织**：子章节描述为分析文章摘要中的内容提供了详细的指导，为当前研究领域建立了系统框架。你必须遵循这一指导来组织摘要内容。从摘要中提取有价值的信息，并将其综合成一个全面的综述子章节，确保最终子章节涵盖所有摘要中的见解。
- **基于证据的分析与综合**：从摘要中提取有力的证据，例如实验结果、批判性分析和深刻见解，以支持子章节描述中的分析。加入必要的过渡或解释性句子，确保整体的流畅性和连贯性。不要仅仅列出摘要内容，要清晰地综合不同的观点，对研究领域进行全面的分析。鼓励引用摘要中的技术细节或实验结果来支持分析。每个主张都需要充分的证据和论证。
- **识别研究空白和未来方向**：系统地总结当前研究领域中的挑战，突出现有研究的局限性，包括样本量的限制、方法论的约束或未解决的研究问题。结合领域中的新兴趋势和技术进展，提出具体且可行的未来研究潜力方向。
2. **将子章节内容整合成一个有机的整体**：
- **全面回顾与核心识别**：彻底回顾每个子章节的内容，系统地找出其中的主要主题、关键论点和重要发现。例如，当一个子章节聚焦于实验方法，另一个聚焦于结果解释时，要准确区分各自独特的核心元素。避免仅仅重复子章节的内容，而要提炼其精髓。
- **发现和利用子章节之间的联系**：寻找子章节之间的共同点和相互联系。这些可能包括共享的研究方法、相关的理论框架或交集的研究问题。利用这些联系作为整合子章节内容的基础。例如，如果多个子章节探讨了特定变量对研究主题的影响，要强调这一共同变量及其在不同子章节中的多样表现。
- **结构化以提高清晰度**：在整合子章节内容时，要建立层次结构。首先呈现最一般和最概括的概念，然后逐步展开更具体的细节。这种方式可以确保整个章节有清晰的逻辑流。首先提供子章节所涵盖的研究领域的总体概述，然后逐渐展开每个子章节的深入发现和分析。
- **有意义的综合，而非简单编纂**：确保子章节内容的整合是有意义的综合，而不仅仅是简单的编纂。提供一个叙述，阐明每个子章节如何有助于对主题的整体理解。如果一个子章节挑战了另一个子章节的发现，要讨论这一矛盾的含义，并提出可能的解决方案或未来研究的进一步探索方向。这也应包括识别当前章节中的主要挑战并提出可能的改进方法，所有内容都应在整合分析的框架内进行。
3. **语言风格**：
- **正式性、严谨性和客观性**：在写作中保持高度的正式性、严谨性和客观性。避免使用口语化表达、随意用词和主观观点。整体语气应反映学术专业性，精准清晰地呈现事实、分析和论点。
- **句子结构和清晰度**：构造句子时要严谨和准确，确保逻辑清晰，易于理解。避免使用过于复杂的句子结构和在单个句子中列举过多缩写。每个句子应该清晰、流畅且自然，思路顺畅推进，避免信息过载和不必要的重复。
- **中立性、精确性和学术严谨性**：采用中立语气，基于证据客观地呈现内容。精确选择词语，避免口语化语言，使用严谨的学术术语。多样化词汇，提高表达的准确性。写作中的每个主张应由相关数据或适当的引用支持，严格遵守学术严谨性标准。
====================
输入材料：
章节描述：
```markdown
{outline}
```

子章节：
{subcontents}

个别文章摘要：
```markdown
{digest}
```
====================
输出要求：
1. 输出的章节内容必须用一对```markdown\n```引用， 内容中除了有特定含义的词汇或缩写，其他所有语言必须为汉语，不允许混杂除汉语与英语之外的其他语言。
2. 每个 Bibkey 必须由引号包裹，每组 Bibkey 必须由一组方括号包围，如["BIBKEY1", "BIBKEY2"]。引用特定文章的bibkey，而不是使用“所有文章”或“所有章节”之类的通用术语。引用在章节描述和文章摘要描述中提到的文章的bibkey，而不是索引本身。如果描述中没有合适的文章引用，请不加引用，直接写出句子。不要单独列出所有bibkey，而是将它们整合到内容中。
3. 如果输出中包含公式，请使用LaTeX格式表示。例如，内联公式使用$y = x^2$，块状公式使用$$y = x^2$$。检查公式的语法正确性与括号完整性，保证其可以通过 KaTeX 渲染，将其中涉及其他宏包的表达方式转换为 KaTeX 支持的表达方式。
4. 内容中不允许输出 Markdown 表格。
====================
格式示例：
```markdown
### {section_title}
通过整合个别摘要中的相关内容并优化整体呈现以提高清晰度和连贯性生成的子章节内容[\"BIBKEY1\", \"BIBKEY2\"]。
```
"""

POLISH_PROMPT = """[任务描述] 
1. 将多个连续的引用, 如 ([BIBKEY1], [BIBKEY2]), ([BIBKEY1, BIBKEY2]) 转换为这种形式：["BIBKEY1", "BIBKEY2"]，移除多余的圆括号与方括号，以及重复的多个引用。输出内容中的所有引用形式必须为 ["BIBKEY1", "BIBKEY2"]，禁止添加多余的圆括号，方括号，以及将多个引用分散在不同的方括号中。禁止添加新引用。
2. 将输出中的其他语言转为汉语。内容中除了有特定含义的词汇或缩写，其他所有语言必须为汉语，不允许混杂除汉语与英语之外的其他语言。禁止对内容进行改动，语言转换前后需要保持含义不变。不允许省略任何内容。
3. 检查公式的语法正确性与括号完整性，保证其可以通过 KaTeX 渲染，将其中涉及其他宏包的表达方式转换为 KaTeX 支持的表达方式。

[内容] 
{content}

[输出要求] 
润色后的内容应该用 ```markdown\\n``` 包括起来。
"""

CHART_PROMPT = """[任务描述]
分析调研报告的全部内容。创建多个 Markdown 表格或 Mermaid 图表以有效传达信息。你需要满足以下要求：
1. 优先考虑读者的阅读体验；确保每个图表或表格的宽度和长度保持适当的平衡。
2. 选择准确且易于理解的关键词来概括对应部分。
3. 为对应部分选择合适的图表类型来展示信息。
4. 一个章节可以使用一个或两个图表，并且不是每个章节都需要用图表表示。图表的位置需要各不相同,均匀的分散在文章的不同部分，以帮助读者更好的理解文章。一个位置只允许放置一张图表。
5. 每个图表必须有一个核心思想来连接所有部分。如果一个图表中的各个组成部分与核心思想无关，应该将其拆分成具有相同标题的多个图表。

[完整内容]
{content}

[输出要求]
图表应包含以下信息：
1. 章节标题。图表所属章节的标题。该图将放置在这个章节中。
2. 位置句子。重复与图表最相关的句子。该图表将放置在这个句子之前。
3. 图表标题，概括该图表的主要内容。
4. 用 ```mermaid\\n``` 引用的 Mermaid 代码。
- 严格遵守 Mermaid 语法。
- 每个节点标签必须用适当形式的括号由 "" 引用。
5. 用 ```markdown\\n``` 引用的 Markdown 代码。

[输出格式]
Section Title: <Section title without index>
Position Sentence: <Position Sentence without index>
Figure Title: <Position Sentence without index>
```mermaid
Code to paint the chart
```

Section Title: <Section title without index>
Position Sentence: <Position Sentence without index>
Figure Title: <Position Sentence without index>
```markdown
Content to paint the table
```

Section Title: <Section title without index>
Position Sentence: <Position Sentence without index>
Figure Title: <Position Sentence without index>
```mermaid
Code to paint the chart
```
"""

RESIDUAL_MODIFY_OUTLINE_PROMPT="""你是一名专业的学术助手，负责帮助研究人员根据提供的材料进行文献综述。  

# 背景
我需要撰写一个关于主题 **"{title}"** 的学术综述大纲，基于提供的 **初始大纲** 进行修改。这些大纲按照不同参考文献的摘要组织，提供了该主题的多种视角。  

# 任务描述
根据提供的 **初始大纲** 以及 **修改建议**，你需要撰写一个新的大纲版本。你必须在相应的修改部分引用 **修改建议** 中的文献。 


# 输入材料
## 初始大纲
```markdown
{old_outline}
```

## 修改建议
{outlines}

# 输出要求
## 格式要求
1. 输出的 **新大纲** 必须采用 Markdown 格式，主题作为一级标题，标题编号使用阿拉伯数字，并用 “.” 连接多级标题。**新大纲** 需使用分隔符包围，即 ```markdown\n```。  
2. 每个大纲部分都应包含系统化且详细的描述，描述应围绕该部分标题展开，并提炼所提供论文中的共性内容。
3. 每个部分的描述应引用合适的参考文献 **bibkey**。如果你认为某部分内容可以借鉴特定论文的摘要，应在句末包含相应的 **bibkey** 。
4. 每个部分可以包含子部分，建议使用 Markdown 标题表示层次结构。
5. 每个 Bibkey 必须由引号包裹，每组 Bibkey 必须由一组方括号包围，如["BIBKEY1", "BIBKEY2"]。你必须引用 **初始大纲** 和 **修改建议** 中的文献。**不要直接引用"初始大纲"和"建议"**。必须引用具体的论文，而不能使用“所有论文”、“所有部分”等笼统术语。如果没有合适的文献提供，你不能添加新的引用。多个引用的 **bibkey** 应该放在同一对方括号 `[]` 内。  

## 格式示例
理由:
阐述你对综述的理解，并说明如何在新大纲中实现所有修改建议。  

新大纲:
```markdown
# {title}
## 1. Section A
在此部分中，详细描述应涵盖的内容，并引用相关文献 [BIBKEY1, BIBKEY2]。
## 2. Section B
在此部分中，详细描述应涵盖的内容，并引用相关文献 [BIBKEY1, BIBKEY2]。
### 2.1 Subsection B1
在此部分中，详细描述应涵盖的内容，并引用相关文献 [BIBKEY1, BIBKEY2]。
### 2.2 Subsection B2
在此部分中，详细描述应涵盖的内容，并引用相关文献 [BIBKEY1, BIBKEY2]。
#### 2.2.1 Subsubsection B2.1
在此部分中，详细描述应涵盖的内容，并引用相关文献 [BIBKEY1, BIBKEY2]。
## 3. Section C
在此部分中，详细描述应涵盖的内容，并引用相关文献 [BIBKEY1, BIBKEY2]。
```
"""

# LLM_search prompts
QUERY_EXPAND_PROMPT_WITH_ABSTRACT="""您是一名行业研究专家，负责就 {topic} 的主题撰写一份综合报告。报告应遵守以下要求：{abstract}。要收集必要的信息，您需要进行在线调研。请生成一组搜索查询，帮助您检索报告的相关数据和见解。将当前查询中模糊的概念分解为更具体的子概念，以实现更精确的搜索。例如，“外国”可以进一步分解为在报告领域内具有代表性的特定国家或地区。输出的内容必须被 ```markdown\n``` 引用。

输出格式：
```markdown
query_content;
query_content;
```
"""
QUERY_EXPAND_PROMPT_WITHOUT_ABSTRACT="""您是一名行业研究专家，负责就 {topic} 的主题撰写一份综合报告。要收集必要的信息，您需要进行在线调研。请生成一组搜索查询，帮助您检索报告的相关数据和见解。将当前查询中模糊的概念分解为更具体的子概念，以实现更精确的搜索。例如，“外国”可以进一步分解为在报告领域内具有代表性的特定国家或地区。 输出的内容必须被 ```markdown\n``` 引用。

输出格式：
```markdown
query_content;
query_content;
```
"""

QUERY_REFINE_STOP_FLAG="无需修改"

USER_CHECK_PROMPT="""你刚刚拆解得到的query为：{queries}\n{user_comment}\n请你仅用\",\"隔开query与query并以简单字符串的形式返回给我。注意只返回我要的query，不要输出其他的任何东西。
"""
LLM_CHECK_PROMPT="""你刚刚拆解得到的query为：{queries}\n请你严格地检查一遍你输出的query，是否每个query都与该报告主题紧密相关？是否每个query针对的领域都不重复？是否该query还可以继续拆解为该行业有代表性的具体技术、企业、个人等角度？尤其是最后一个问题，你可以将广义概念拆解为几个有代表性的具体技术名词、企业名称、专家名字等。如果有以上问题，请你谈谈你的分析，并补充或修改query，不过没问题的query可以保留，不要删除。\n\n如果这些query需要修改，请你用以下格式输出你的回答：\n\n“AI的判断：\"{\"你的修改意见\"}\"\n本轮的输出queries: query_1,query_2,...,query_n”\n\n其中“本轮的输出query：”后为你修改过的query。\n\n如果这些query已经无需修改，请你用以下格式输出你的回答：\n\n“AI的判断：无需修改。\n本轮的输出query：query_1,query_2,...,query_n”\n\n其中“本轮的输出query：”后为这些无需修改的query。
"""

SNIPPET_FILTER_PROMPT="""请你依据下列主题和在互联网上检索到的网页片段，推测这个网页与主题的相关程度。

主题：{topic}
网页片段：{snippet}

请你综合考量上述两个维度，先给出评分的理由，再进行评分。评分范围是0-100。0表示完全不相关，100表示完全相关。请你评分尽可能严格。

注意，评分需要用<SCORE></SCORE>包裹起来。例如<SCORE>78<SCORE>

回答示例：
理由：...
相似度评分：<SCORE>89</SCORE>
"""


# crawl4ai prompts
PAGE_REFINE_PROMPT="""分析并处理以下与‘{topic}’相关的网页内容。输出主体文本，去除图片链接，网址链接，广告，无意义重复字符等。禁止对内容进行总结，应保留所有与主题相关的信息。

原始网页内容：
{raw_content}

[输出要求]
- 标题：<TITLE>你的标题</TITLE>
- 过滤后文本：<CONTENT>过滤后文本</CONTENT>
"""

SIMILARITY_PROMPT="""请你依据下列主题和在互联网上检索到的内容，判断这段内容的质量分数。

主题：{topic}
检索到的内容：{content}

请你依据以下几个维度，对这段检索到的内容进行打分。请你尽可能严格，批判性给分。

1. 内容与主题的相关程度。这需要考虑内容是否能被视为主题的一部分子内容进行展开。
2. 内容能够用于撰写与主题的文本的质量。这需要考虑文本的长度（例如：如果长度非常短，则用于参考的价值相对较低）、文本中是否包含较多乱码、文本本身的质量等。

请你综合考量上述两个维度，先给出评分的理由，再进行评分。你需要对每一个维度进行评分，评分范围是0-100。0表示完全不相关，100表示完全相关。完成每一个维度的评分后，你需要进行计算，得出最后的平均分。

注意，评分需要用<SCORE></SCORE>包裹起来。例如<SCORE>78<SCORE>

回答示例：
理由：...
相似度评分：<SCORE>89</SCORE>
"""