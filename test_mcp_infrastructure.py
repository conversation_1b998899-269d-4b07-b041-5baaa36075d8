#!/usr/bin/env python3
"""
Test script to validate MCP server implementations and client connections.
This script tests the core functionality of the intelligent pipeline system.
"""
import asyncio
import json
import sys
import os

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.hidden.mcp_client.intelligent_pipeline_client import IntelligentPipelineClient

async def test_mcp_servers():
    """Test all MCP servers and client interactions."""
    print("🧪 Testing MCP Server Infrastructure")
    print("=" * 50)
    
    try:
        # Initialize the intelligent pipeline client
        config_path = "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2/config/mcp_config.json"
        client = IntelligentPipelineClient(config_path)
        
        print("✅ IntelligentPipelineClient initialized successfully")
        
        # Test 1: Orchestrate pipeline
        print("\n📋 Test 1: Pipeline Orchestration")
        # test_content = [
        #     {"id": 1, "content": "Introduction to machine learning algorithms and their applications."},
        #     {"id": 2, "content": "Deep learning neural networks and backpropagation explained."},
        #     {"id": 3, "content": "Natural language processing techniques for text analysis."}
        # ]

        # 给出一个测试样例
        with open('/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2/survey_data_full_1231_one_line.jsonl', 'r', encoding='utf-8') as f:
            for line in f:
                test_content = json.loads(line)

                workflow = await client.orchestrate_pipeline(test_content)
                print(f"Pipeline workflow: {workflow}")
                
                # Test 2: Group processing
                print("\n🔗 Test 2: Group Processing")
                groups = await client.process_groups(test_content)
                print(f"Generated groups: {groups}")
                
                # Test 3: Skeleton generation
                print("\n🦴 Test 3: Skeleton Generation")
                skeleton = await client.generate_skeleton(groups)
                print(f"Generated skeleton: {skeleton}")
            
                # Test 4: Digest processing
                print("\n📄 Test 4: Digest Processing")
                digest = await client.process_digest(test_content, skeleton)
                print(f"Generated digest: {digest}")
                
                print("\n✅ All MCP tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test MCP configuration file."""
    print("\n🔧 Testing Configuration")
    print("=" * 30)
    
    config_path = "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2/config/mcp_config.json"
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print("✅ Configuration file loaded successfully")
        
        # Check required servers
        required_servers = [
            "pipeline_orchestrator",
            "digest_processor", 
            "skeleton_processor",
            "group_processor",
            "convolution_processor",
            "basic_modules_processor",
            "neurons_processor"
        ]
        
        for server in required_servers:
            if server in config.get("servers", {}):
                print(f"✅ {server} server configured")
            else:
                print(f"❌ {server} server missing")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False

def test_imports():
    """Test that all modules can be imported correctly."""
    print("\n📦 Testing Module Imports")
    print("=" * 30)
    
    try:
        # Test server imports
        from src.hidden.mcp_server.pipeline_orchestrator_server import PipelineOrchestratorServer
        from src.hidden.mcp_server.digest_processor_server import DigestProcessorServer
        from src.hidden.mcp_server.skeleton_processor_server import SkeletonProcessorServer
        from src.hidden.mcp_server.group_processor_server import GroupProcessorServer
        
        # Test new MCP server imports
        try:
            from src.hidden.mcp_server.convolution_processor_server import app as convolution_app
            print("✅ Convolution processor server imported successfully")
        except Exception as e:
            print(f"⚠️ Convolution processor server import warning: {e}")
        
        try:
            from src.hidden.mcp_server.basic_modules_processor_server import app as basic_modules_app
            print("✅ Basic modules processor server imported successfully")
        except Exception as e:
            print(f"⚠️ Basic modules processor server import warning: {e}")
        
        try:
            from src.hidden.mcp_server.neurons_processor_server import app as neurons_app
            print("✅ Neurons processor server imported successfully")
        except Exception as e:
            print(f"⚠️ Neurons processor server import warning: {e}")
        
        print("✅ All MCP server modules imported successfully")
        
        # Test client import
        from src.hidden.mcp_client.intelligent_pipeline_client import IntelligentPipelineClient
        print("✅ MCP client module imported successfully")
        
        # Test intelligent pipeline import
        from src.hidden.intelligent_hidden_pipeline import IntelligentHiddenPipeline
        print("✅ IntelligentHiddenPipeline imported successfully")
        
        # Test prompt templates
        from src.prompts.intelligent_prompts import ORCHESTRATION_PROMPTS
        print("✅ Prompt templates imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_enhanced_mcp_servers():
    """测试增强的MCP服务器功能"""
    print("\n🧪 Testing Enhanced MCP Server Infrastructure")
    print("=" * 50)
    
    try:
        # Initialize the intelligent pipeline client
        config_path = "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2/config/mcp_config.json"
        client = IntelligentPipelineClient(config_path)
        
        print("✅ Enhanced IntelligentPipelineClient initialized successfully")
        
        # Test 1: Convolution Layer Processing
        print("\n📋 Test 1: Convolution Layer Processing")
        test_data = {
            "content": "测试内容用于卷积层处理",
            "structure": {"sections": ["introduction", "methods", "results"]}
        }
        
        try:
            convolution_result = await client.apply_convolution_layer(
                input_data=test_data,
                strategy="adaptive"
            )
            print(f"✅ Convolution layer processing: {convolution_result.get('confidence', 'N/A')}")
        except Exception as e:
            print(f"⚠️ Convolution layer test warning: {e}")
        
        # Test 2: Basic Modules Processing
        print("\n📋 Test 2: Basic Modules Processing")
        test_papers = [
            {"title": "机器学习基础", "content": "介绍机器学习的基本概念和算法"},
            {"title": "深度学习应用", "content": "深度学习在各个领域的应用案例"}
        ]
        
        try:
            grouping_result = await client.group_papers_intelligently(
                papers=test_papers,
                criteria="semantic_similarity"
            )
            print(f"✅ Paper grouping: {len(grouping_result.get('groups', []))} groups created")
        except Exception as e:
            print(f"⚠️ Basic modules test warning: {e}")
        
        # Test 3: Neurons Processing
        print("\n📋 Test 3: Neurons Processing")
        test_content = {"text": "测试内容质量评估", "structure": {"depth": 2}}
        
        try:
            evaluation_result = await client.evaluate_content_intelligently(
                content=test_content,
                criteria="comprehensive"
            )
            print(f"✅ Content evaluation: score {evaluation_result.get('score', 'N/A')}")
        except Exception as e:
            print(f"⚠️ Neurons processing test warning: {e}")
        
        # Test 4: Enhanced Pipeline Integration
        print("\n📋 Test 4: Enhanced Pipeline Integration")
        try:
            # Create a mock Survey object for testing
            from src.data_structure import Survey, Paper
            test_survey = Survey()
            test_survey.topic = "智能算法研究"
            test_survey.papers = [
                Paper("论文1", "内容1"),
                Paper("论文2", "内容2")
            ]
            
            config = {
                "skeleton_requirements": {"depth": 3},
                "grouping_criteria": "semantic_similarity",
                "digest_strategy": "comprehensive",
                "convolution_strategy": "adaptive",
                "quality_threshold": 0.7
            }
            
            enhanced_survey = await client.process_survey_with_mcp_pipeline(
                survey=test_survey,
                config=config
            )
            print(f"✅ Enhanced pipeline processing completed")
        except Exception as e:
            print(f"⚠️ Enhanced pipeline test warning: {e}")
        
        print("\n✅ Enhanced MCP server tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced MCP tests failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🚀 MCP Infrastructure Test Suite")
    print("=" * 50)
    
    # Test 1: Module imports
    # import_success = test_imports()
    
    # Test 2: Configuration
    config_success = test_configuration()
    
    # Test 3: MCP functionality (only if imports and config work)
    mcp_success = False
    # if import_success and config_success:
    if config_success:
        mcp_success = await test_mcp_servers()
    else:
        print("\n⚠️ Skipping MCP tests due to import/config failures")
    
    # Test 4: Enhanced MCP functionality
    enhanced_mcp_success = False
    # if import_success and config_success:
    if config_success:
        enhanced_mcp_success = await test_enhanced_mcp_servers()
    else:
        print("\n⚠️ Skipping enhanced MCP tests due to import/config failures")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    # print(f"Module Imports: {'✅ PASS' if import_success else '❌ FAIL'}")
    print(f"Configuration: {'✅ PASS' if config_success else '❌ FAIL'}")
    print(f"MCP Functionality: {'✅ PASS' if mcp_success else '❌ FAIL'}")
    print(f"Enhanced MCP Functionality: {'✅ PASS' if enhanced_mcp_success else '❌ FAIL'}")
    
    # if import_success and config_success and mcp_success and enhanced_mcp_success:
    #     print("\n🎉 All tests passed! MCP infrastructure is ready.")
    #     return 0
    # else:
    #     print("\n⚠️ Some tests failed. Please check the errors above.")
    #     return 1

if __name__ == "__main__":
    # exit_code = asyncio.run(main())
    # sys.exit(exit_code)
    asyncio.run(main())
